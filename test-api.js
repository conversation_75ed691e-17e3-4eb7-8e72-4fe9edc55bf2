// Script test đơn giản để kiểm tra API
const http = require('http');

function testAPI(email) {
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: `/apicanva?email=${encodeURIComponent(email)}`,
        method: 'GET'
    };

    const req = http.request(options, (res) => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Headers: ${JSON.stringify(res.headers)}`);
        
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log('Response:', data);
        });
    });

    req.on('error', (e) => {
        console.error(`Lỗi request: ${e.message}`);
    });

    req.end();
}

// Sử dụng
const testEmail = process.argv[2] || '<EMAIL>';
console.log(`Testing API với email: ${testEmail}`);
testAPI(testEmail);
