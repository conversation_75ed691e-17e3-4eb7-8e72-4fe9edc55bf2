{"version": 3, "file": "ipAddress.js", "sources": ["../../../src/utils/ipAddress.ts"], "sourcesContent": ["import type { Session, SessionAggregates } from '../types-hoist/session';\nimport type { User } from '../types-hoist/user';\n\n// By default, we want to infer the IP address, unless this is explicitly set to `null`\n// We do this after all other processing is done\n// If `ip_address` is explicitly set to `null` or a value, we leave it as is\n\n/**\n * @internal\n */\nexport function addAutoIpAddressToUser(objWithMaybeUser: { user?: User | null }): void {\n  if (objWithMaybeUser.user?.ip_address === undefined) {\n    objWithMaybeUser.user = {\n      ...objWithMaybeUser.user,\n      ip_address: '{{auto}}',\n    };\n  }\n}\n\n/**\n * @internal\n */\nexport function addAutoIpAddressToSession(session: Session | SessionAggregates): void {\n  if ('aggregates' in session) {\n    if (session.attrs?.['ip_address'] === undefined) {\n      session.attrs = {\n        ...session.attrs,\n        ip_address: '{{auto}}',\n      };\n    }\n  } else {\n    if (session.ipAddress === undefined) {\n      session.ipAddress = '{{auto}}';\n    }\n  }\n}\n"], "names": [], "mappings": ";;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,gBAAgB,EAAgC;AACvF,EAAE,IAAI,gBAAgB,CAAC,IAAI,EAAE,UAAA,KAAe,SAAS,EAAE;AACvD,IAAI,gBAAgB,CAAC,IAAA,GAAO;AAC5B,MAAM,GAAG,gBAAgB,CAAC,IAAI;AAC9B,MAAM,UAAU,EAAE,UAAU;AAC5B,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACO,SAAS,yBAAyB,CAAC,OAAO,EAAqC;AACtF,EAAE,IAAI,YAAa,IAAG,OAAO,EAAE;AAC/B,IAAI,IAAI,OAAO,CAAC,KAAK,GAAG,YAAY,CAAA,KAAM,SAAS,EAAE;AACrD,MAAM,OAAO,CAAC,KAAA,GAAQ;AACtB,QAAQ,GAAG,OAAO,CAAC,KAAK;AACxB,QAAQ,UAAU,EAAE,UAAU;AAC9B,OAAO;AACP;AACA,SAAS;AACT,IAAI,IAAI,OAAO,CAAC,SAAU,KAAI,SAAS,EAAE;AACzC,MAAM,OAAO,CAAC,SAAU,GAAE,UAAU;AACpC;AACA;AACA;;;;;"}