{"version": 3, "file": "getIpAddress.js", "sources": ["../../../src/vendor/getIpAddress.ts"], "sourcesContent": ["// Vendored / modified from @sergiodxa/remix-utils\n\n// https://github.com/sergiodxa/remix-utils/blob/02af80e12829a53696bfa8f3c2363975cf59f55e/src/server/get-client-ip-address.ts\n// MIT License\n\n// Copyright (c) 2021 <PERSON>\n\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\n\n// The headers to check, in priority order\nexport const ipHeaderNames = [\n  'X-Client-IP',\n  'X-Forwarded-For',\n  'Fly-Client-IP',\n  'CF-Connecting-IP',\n  'Fastly-Client-Ip',\n  'True-Client-Ip',\n  'X-Real-IP',\n  'X-Cluster-Client-IP',\n  'X-Forwarded',\n  'Forwarded-For',\n  'Forwarded',\n  'X-Vercel-Forwarded-For',\n];\n\n/**\n * Get the IP address of the client sending a request.\n *\n * It receives a Request headers object and use it to get the\n * IP address from one of the following headers in order.\n *\n * If the IP address is valid, it will be returned. Otherwise, null will be\n * returned.\n *\n * If the header values contains more than one IP address, the first valid one\n * will be returned.\n */\nexport function getClientIPAddress(headers: { [key: string]: string | string[] | undefined }): string | null {\n  // This will end up being Array<string | string[] | undefined | null> because of the various possible values a header\n  // can take\n  const headerValues = ipHeaderNames.map((headerName: string) => {\n    const rawValue = headers[headerName];\n    const value = Array.isArray(rawValue) ? rawValue.join(';') : rawValue;\n\n    if (headerName === 'Forwarded') {\n      return parseForwardedHeader(value);\n    }\n\n    return value?.split(',').map((v: string) => v.trim());\n  });\n\n  // Flatten the array and filter out any falsy entries\n  const flattenedHeaderValues = headerValues.reduce((acc: string[], val) => {\n    if (!val) {\n      return acc;\n    }\n\n    return acc.concat(val);\n  }, []);\n\n  // Find the first value which is a valid IP address, if any\n  const ipAddress = flattenedHeaderValues.find(ip => ip !== null && isIP(ip));\n\n  return ipAddress || null;\n}\n\nfunction parseForwardedHeader(value: string | null | undefined): string | null {\n  if (!value) {\n    return null;\n  }\n\n  for (const part of value.split(';')) {\n    if (part.startsWith('for=')) {\n      return part.slice(4);\n    }\n  }\n\n  return null;\n}\n\n//\n/**\n * Custom method instead of importing this from `net` package, as this only exists in node\n * Accepts:\n * 127.0.0.1\n * ***********\n * *************\n * ***************\n * ********\n * 0.0.0.0\n * 2b01:cb19:8350:ed00:d0dd:fa5b:de31:8be5\n *\n * Rejects:\n * ********\n * ************.1\n * 127.1\n * 192.168.1.256\n * -*******\n * *******.\n * 3...3\n * *************\n */\nfunction isIP(str: string): boolean {\n  const regex =\n    /(?:^(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}$)|(?:^(?:(?:[a-fA-F\\d]{1,4}:){7}(?:[a-fA-F\\d]{1,4}|:)|(?:[a-fA-F\\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|:[a-fA-F\\d]{1,4}|:)|(?:[a-fA-F\\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,2}|:)|(?:[a-fA-F\\d]{1,4}:){4}(?:(?::[a-fA-F\\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,3}|:)|(?:[a-fA-F\\d]{1,4}:){3}(?:(?::[a-fA-F\\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,4}|:)|(?:[a-fA-F\\d]{1,4}:){2}(?:(?::[a-fA-F\\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,5}|:)|(?:[a-fA-F\\d]{1,4}:){1}(?:(?::[a-fA-F\\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,6}|:)|(?::(?:(?::[a-fA-F\\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,7}|:)))(?:%[0-9a-zA-Z]{1,})?$)/;\n  return regex.test(str);\n}\n"], "names": [], "mappings": "AAAA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO,MAAM,gBAAgB;AAC7B,EAAE,aAAa;AACf,EAAE,iBAAiB;AACnB,EAAE,eAAe;AACjB,EAAE,kBAAkB;AACpB,EAAE,kBAAkB;AACpB,EAAE,gBAAgB;AAClB,EAAE,WAAW;AACb,EAAE,qBAAqB;AACvB,EAAE,aAAa;AACf,EAAE,eAAe;AACjB,EAAE,WAAW;AACb,EAAE,wBAAwB;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,OAAO,EAAmE;AAC7G;AACA;AACA,EAAE,MAAM,YAAa,GAAE,aAAa,CAAC,GAAG,CAAC,CAAC,UAAU,KAAa;AACjE,IAAI,MAAM,QAAS,GAAE,OAAO,CAAC,UAAU,CAAC;AACxC,IAAI,MAAM,KAAM,GAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAE,GAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAA,GAAI,QAAQ;;AAEzE,IAAI,IAAI,UAAW,KAAI,WAAW,EAAE;AACpC,MAAM,OAAO,oBAAoB,CAAC,KAAK,CAAC;AACxC;;AAEA,IAAI,OAAO,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAa,CAAC,CAAC,IAAI,EAAE,CAAC;AACzD,GAAG,CAAC;;AAEJ;AACA,EAAE,MAAM,qBAAA,GAAwB,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAY,GAAG,KAAK;AAC5E,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,GAAG;AAChB;;AAEA,IAAI,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC;AAC1B,GAAG,EAAE,EAAE,CAAC;;AAER;AACA,EAAE,MAAM,SAAU,GAAE,qBAAqB,CAAC,IAAI,CAAC,EAAA,IAAM,EAAA,KAAO,IAAK,IAAG,IAAI,CAAC,EAAE,CAAC,CAAC;;AAE7E,EAAE,OAAO,SAAU,IAAG,IAAI;AAC1B;;AAEA,SAAS,oBAAoB,CAAC,KAAK,EAA4C;AAC/E,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI;AACf;;AAEA,EAAE,KAAK,MAAM,IAAK,IAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACvC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;AACjC,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1B;AACA;;AAEA,EAAE,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI,CAAC,GAAG,EAAmB;AACpC,EAAE,MAAM,KAAM;AACd,IAAI,muCAAmuC;AACvuC,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;AACxB;;;;"}