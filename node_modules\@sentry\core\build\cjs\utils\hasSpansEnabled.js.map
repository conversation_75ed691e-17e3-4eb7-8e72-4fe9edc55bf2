{"version": 3, "file": "hasSpansEnabled.js", "sources": ["../../../src/utils/hasSpansEnabled.ts"], "sourcesContent": ["import { getClient } from '../currentScopes';\nimport type { Options } from '../types-hoist/options';\n\n// Treeshakable guard to remove all code related to tracing\ndeclare const __SENTRY_TRACING__: boolean | undefined;\n\n/**\n * Determines if span recording is currently enabled.\n *\n * Spans are recorded when at least one of `tracesSampleRate` and `tracesSampler`\n * is defined in the SDK config. This function does not make any assumption about\n * sampling decisions, it only checks if the SDK is configured to record spans.\n *\n * Important: This function only determines if span recording is enabled. Trace\n * continuation and propagation is separately controlled and not covered by this function.\n * If this function returns `false`, traces can still be propagated (which is what\n * we refer to by \"Tracing without Performance\")\n * @see https://develop.sentry.dev/sdk/telemetry/traces/tracing-without-performance/\n *\n * @param maybeOptions An SDK options object to be passed to this function.\n * If this option is not provided, the function will use the current client's options.\n */\nexport function hasSpansEnabled(\n  maybeOptions?: Pick<Options, 'tracesSampleRate' | 'tracesSampler'> | undefined,\n): boolean {\n  if (typeof __SENTRY_TRACING__ === 'boolean' && !__SENTRY_TRACING__) {\n    return false;\n  }\n\n  const options = maybeOptions || getClient()?.getOptions();\n  return (\n    !!options &&\n    // Note: This check is `!= null`, meaning \"nullish\". `0` is not \"nullish\", `undefined` and `null` are. (This comment was brought to you by 15 minutes of questioning life)\n    (options.tracesSampleRate != null || !!options.tracesSampler)\n  );\n}\n\n/**\n * @see JSDoc of `hasSpansEnabled`\n * @deprecated Use `hasSpansEnabled` instead, which is a more accurately named version of this function.\n * This function will be removed in the next major version of the SDK.\n */\n// TODO(v10): Remove this export\nexport const hasTracingEnabled = hasSpansEnabled;\n"], "names": ["getClient"], "mappings": ";;;;AAGA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe;AAC/B,EAAE,YAAY;AACd,EAAW;AACX,EAAE,IAAI,OAAO,kBAAA,KAAuB,SAAU,IAAG,CAAC,kBAAkB,EAAE;AACtE,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,MAAM,OAAQ,GAAE,YAAa,IAAGA,uBAAS,EAAE,EAAE,UAAU,EAAE;AAC3D,EAAE;AACF,IAAI,CAAC,CAAC,OAAQ;AACd;AACA,KAAK,OAAO,CAAC,gBAAiB,IAAG,IAAK,IAAG,CAAC,CAAC,OAAO,CAAC,aAAa;AAChE;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,iBAAkB,GAAE;;;;;"}