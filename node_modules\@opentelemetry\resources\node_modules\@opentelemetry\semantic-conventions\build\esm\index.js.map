{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH;;;GAGG;AAEH,+DAA+D;AAC/D,cAAc,SAAS,CAAC;AACxB,cAAc,YAAY,CAAC;AAE3B,oBAAoB;AACpB,cAAc,qBAAqB,CAAC;AACpC,cAAc,kBAAkB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable no-restricted-syntax --\n * These re-exports are only of constants, only two-levels deep, and\n * should not cause problems for tree-shakers.\n */\n\n// Deprecated. These are kept around for compatibility purposes\nexport * from './trace';\nexport * from './resource';\n\n// Use these instead\nexport * from './stable_attributes';\nexport * from './stable_metrics';\n"]}