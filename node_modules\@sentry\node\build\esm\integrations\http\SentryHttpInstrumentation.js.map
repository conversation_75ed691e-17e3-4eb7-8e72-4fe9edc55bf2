{"version": 3, "file": "SentryHttpInstrumentation.js", "sources": ["../../../../src/integrations/http/SentryHttpInstrumentation.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type { ChannelListener } from 'node:diagnostics_channel';\nimport { subscribe, unsubscribe } from 'node:diagnostics_channel';\nimport type * as http from 'node:http';\nimport type * as https from 'node:https';\nimport type { EventEmitter } from 'node:stream';\nimport { context, propagation } from '@opentelemetry/api';\nimport { isTracingSuppressed, VERSION } from '@opentelemetry/core';\nimport type { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';\nimport type { AggregationCounts, Client, SanitizedRequestData, Scope } from '@sentry/core';\nimport {\n  addBreadcrumb,\n  addNonEnumerableProperty,\n  generateSpanId,\n  getBreadcrumbLogLevelFromHttpStatusCode,\n  getClient,\n  getCurrentScope,\n  getIsolationScope,\n  getSanitizedUrlString,\n  getTraceData,\n  httpRequestToRequestData,\n  isError,\n  logger,\n  LRUMap,\n  parseUrl,\n  stripUrlQueryAndFragment,\n  withIsolationScope,\n} from '@sentry/core';\nimport { shouldPropagateTraceForUrl } from '@sentry/opentelemetry';\nimport { DEBUG_BUILD } from '../../debug-build';\nimport { mergeBaggageHeaders } from '../../utils/baggage';\nimport { getRequestUrl } from '../../utils/getRequestUrl';\n\nconst INSTRUMENTATION_NAME = '@sentry/instrumentation-http';\n\ntype Http = typeof http;\ntype Https = typeof https;\n\nexport type SentryHttpInstrumentationOptions = InstrumentationConfig & {\n  /**\n   * Whether breadcrumbs should be recorded for requests.\n   *\n   * @default `true`\n   */\n  breadcrumbs?: boolean;\n\n  /**\n   * Whether to extract the trace ID from the `sentry-trace` header for incoming requests.\n   * By default this is done by the HttpInstrumentation, but if that is not added (e.g. because tracing is disabled, ...)\n   * then this instrumentation can take over.\n   *\n   * @default `false`\n   */\n  extractIncomingTraceFromHeader?: boolean;\n\n  /**\n   * Whether to propagate Sentry trace headers in outgoing requests.\n   * By default this is done by the HttpInstrumentation, but if that is not added (e.g. because tracing is disabled)\n   * then this instrumentation can take over.\n   *\n   * @default `false`\n   */\n  propagateTraceInOutgoingRequests?: boolean;\n\n  /**\n   * Do not capture breadcrumbs for outgoing HTTP requests to URLs where the given callback returns `true`.\n   * For the scope of this instrumentation, this callback only controls breadcrumb creation.\n   * The same option can be passed to the top-level httpIntegration where it controls both, breadcrumb and\n   * span creation.\n   *\n   * @param url Contains the entire URL, including query string (if any), protocol, host, etc. of the outgoing request.\n   * @param request Contains the {@type RequestOptions} object used to make the outgoing request.\n   */\n  ignoreOutgoingRequests?: (url: string, request: http.RequestOptions) => boolean;\n\n  /**\n   * Do not capture the request body for incoming HTTP requests to URLs where the given callback returns `true`.\n   * This can be useful for long running requests where the body is not needed and we want to avoid capturing it.\n   *\n   * @param url Contains the entire URL, including query string (if any), protocol, host, etc. of the incoming request.\n   * @param request Contains the {@type RequestOptions} object used to make the incoming request.\n   */\n  ignoreIncomingRequestBody?: (url: string, request: http.RequestOptions) => boolean;\n\n  /**\n   * Controls the maximum size of incoming HTTP request bodies attached to events.\n   *\n   * Available options:\n   * - 'none': No request bodies will be attached\n   * - 'small': Request bodies up to 1,000 bytes will be attached\n   * - 'medium': Request bodies up to 10,000 bytes will be attached (default)\n   * - 'always': Request bodies will always be attached\n   *\n   * Note that even with 'always' setting, bodies exceeding 1MB will never be attached\n   * for performance and security reasons.\n   *\n   * @default 'medium'\n   */\n  maxIncomingRequestBodySize?: 'none' | 'small' | 'medium' | 'always';\n\n  /**\n   * Whether the integration should create [Sessions](https://docs.sentry.io/product/releases/health/#sessions) for incoming requests to track the health and crash-free rate of your releases in Sentry.\n   * Read more about Release Health: https://docs.sentry.io/product/releases/health/\n   *\n   * Defaults to `true`.\n   */\n  trackIncomingRequestsAsSessions?: boolean;\n\n  /**\n   * Number of milliseconds until sessions tracked with `trackIncomingRequestsAsSessions` will be flushed as a session aggregate.\n   *\n   * Defaults to `60000` (60s).\n   */\n  sessionFlushingDelayMS?: number;\n};\n\n// We only want to capture request bodies up to 1mb.\nconst MAX_BODY_BYTE_LENGTH = 1024 * 1024;\n\n/**\n * This custom HTTP instrumentation is used to isolate incoming requests and annotate them with additional information.\n * It does not emit any spans.\n *\n * The reason this is isolated from the OpenTelemetry instrumentation is that users may overwrite this,\n * which would lead to Sentry not working as expected.\n *\n * Important note: Contrary to other OTEL instrumentation, this one cannot be unwrapped.\n * It only does minimal things though and does not emit any spans.\n *\n * This is heavily inspired & adapted from:\n * https://github.com/open-telemetry/opentelemetry-js/blob/f8ab5592ddea5cba0a3b33bf8d74f27872c0367f/experimental/packages/opentelemetry-instrumentation-http/src/http.ts\n */\nexport class SentryHttpInstrumentation extends InstrumentationBase<SentryHttpInstrumentationOptions> {\n  private _propagationDecisionMap: LRUMap<string, boolean>;\n  private _ignoreOutgoingRequestsMap: WeakMap<http.ClientRequest, boolean>;\n\n  public constructor(config: SentryHttpInstrumentationOptions = {}) {\n    super(INSTRUMENTATION_NAME, VERSION, config);\n\n    this._propagationDecisionMap = new LRUMap<string, boolean>(100);\n    this._ignoreOutgoingRequestsMap = new WeakMap<http.ClientRequest, boolean>();\n  }\n\n  /** @inheritdoc */\n  public init(): [InstrumentationNodeModuleDefinition, InstrumentationNodeModuleDefinition] {\n    // We register handlers when either http or https is instrumented\n    // but we only want to register them once, whichever is loaded first\n    let hasRegisteredHandlers = false;\n\n    const onHttpServerRequestStart = ((_data: unknown) => {\n      const data = _data as { server: http.Server };\n      this._patchServerEmitOnce(data.server);\n    }) satisfies ChannelListener;\n\n    const onHttpClientResponseFinish = ((_data: unknown) => {\n      const data = _data as { request: http.ClientRequest; response: http.IncomingMessage };\n      this._onOutgoingRequestFinish(data.request, data.response);\n    }) satisfies ChannelListener;\n\n    const onHttpClientRequestError = ((_data: unknown) => {\n      const data = _data as { request: http.ClientRequest };\n      this._onOutgoingRequestFinish(data.request, undefined);\n    }) satisfies ChannelListener;\n\n    const onHttpClientRequestCreated = ((_data: unknown) => {\n      const data = _data as { request: http.ClientRequest };\n      this._onOutgoingRequestCreated(data.request);\n    }) satisfies ChannelListener;\n\n    const wrap = <T extends Http | Https>(moduleExports: T): T => {\n      if (hasRegisteredHandlers) {\n        return moduleExports;\n      }\n\n      hasRegisteredHandlers = true;\n\n      subscribe('http.server.request.start', onHttpServerRequestStart);\n      subscribe('http.client.response.finish', onHttpClientResponseFinish);\n\n      // When an error happens, we still want to have a breadcrumb\n      // In this case, `http.client.response.finish` is not triggered\n      subscribe('http.client.request.error', onHttpClientRequestError);\n\n      // NOTE: This channel only exist since Node 22\n      // Before that, outgoing requests are not patched\n      // and trace headers are not propagated, sadly.\n      if (this.getConfig().propagateTraceInOutgoingRequests) {\n        subscribe('http.client.request.created', onHttpClientRequestCreated);\n      }\n\n      return moduleExports;\n    };\n\n    const unwrap = (): void => {\n      unsubscribe('http.server.request.start', onHttpServerRequestStart);\n      unsubscribe('http.client.response.finish', onHttpClientResponseFinish);\n      unsubscribe('http.client.request.error', onHttpClientRequestError);\n      unsubscribe('http.client.request.created', onHttpClientRequestCreated);\n    };\n\n    /**\n     * You may be wondering why we register these diagnostics-channel listeners\n     * in such a convoluted way (as InstrumentationNodeModuleDefinition...)˝,\n     * instead of simply subscribing to the events once in here.\n     * The reason for this is timing semantics: These functions are called once the http or https module is loaded.\n     * If we'd subscribe before that, there seem to be conflicts with the OTEL native instrumentation in some scenarios,\n     * especially the \"import-on-top\" pattern of setting up ESM applications.\n     */\n    return [\n      new InstrumentationNodeModuleDefinition('http', ['*'], wrap, unwrap),\n      new InstrumentationNodeModuleDefinition('https', ['*'], wrap, unwrap),\n    ];\n  }\n\n  /**\n   * This is triggered when an outgoing request finishes.\n   * It has access to the final request and response objects.\n   */\n  private _onOutgoingRequestFinish(request: http.ClientRequest, response?: http.IncomingMessage): void {\n    DEBUG_BUILD && logger.log(INSTRUMENTATION_NAME, 'Handling finished outgoing request');\n\n    const _breadcrumbs = this.getConfig().breadcrumbs;\n    const breadCrumbsEnabled = typeof _breadcrumbs === 'undefined' ? true : _breadcrumbs;\n\n    // Note: We cannot rely on the map being set by `_onOutgoingRequestCreated`, because that is not run in Node <22\n    const shouldIgnore = this._ignoreOutgoingRequestsMap.get(request) ?? this._shouldIgnoreOutgoingRequest(request);\n    this._ignoreOutgoingRequestsMap.set(request, shouldIgnore);\n\n    if (breadCrumbsEnabled && !shouldIgnore) {\n      addRequestBreadcrumb(request, response);\n    }\n  }\n\n  /**\n   * This is triggered when an outgoing request is created.\n   * It has access to the request object, and can mutate it before the request is sent.\n   */\n  private _onOutgoingRequestCreated(request: http.ClientRequest): void {\n    const shouldIgnore = this._ignoreOutgoingRequestsMap.get(request) ?? this._shouldIgnoreOutgoingRequest(request);\n    this._ignoreOutgoingRequestsMap.set(request, shouldIgnore);\n\n    if (shouldIgnore) {\n      return;\n    }\n\n    // Add trace propagation headers\n    const url = getRequestUrl(request);\n\n    // Manually add the trace headers, if it applies\n    // Note: We do not use `propagation.inject()` here, because our propagator relies on an active span\n    // Which we do not have in this case\n    const tracePropagationTargets = getClient()?.getOptions().tracePropagationTargets;\n    const addedHeaders = shouldPropagateTraceForUrl(url, tracePropagationTargets, this._propagationDecisionMap)\n      ? getTraceData()\n      : undefined;\n\n    if (!addedHeaders) {\n      return;\n    }\n\n    const { 'sentry-trace': sentryTrace, baggage } = addedHeaders;\n\n    // We do not want to overwrite existing header here, if it was already set\n    if (sentryTrace && !request.getHeader('sentry-trace')) {\n      try {\n        request.setHeader('sentry-trace', sentryTrace);\n        DEBUG_BUILD && logger.log(INSTRUMENTATION_NAME, 'Added sentry-trace header to outgoing request');\n      } catch (error) {\n        DEBUG_BUILD &&\n          logger.error(\n            INSTRUMENTATION_NAME,\n            'Failed to add sentry-trace header to outgoing request:',\n            isError(error) ? error.message : 'Unknown error',\n          );\n      }\n    }\n\n    if (baggage) {\n      // For baggage, we make sure to merge this into a possibly existing header\n      const newBaggage = mergeBaggageHeaders(request.getHeader('baggage'), baggage);\n      if (newBaggage) {\n        try {\n          request.setHeader('baggage', newBaggage);\n          DEBUG_BUILD && logger.log(INSTRUMENTATION_NAME, 'Added baggage header to outgoing request');\n        } catch (error) {\n          DEBUG_BUILD &&\n            logger.error(\n              INSTRUMENTATION_NAME,\n              'Failed to add baggage header to outgoing request:',\n              isError(error) ? error.message : 'Unknown error',\n            );\n        }\n      }\n    }\n  }\n\n  /**\n   * Patch a server.emit function to handle process isolation for incoming requests.\n   * This will only patch the emit function if it was not already patched.\n   */\n  private _patchServerEmitOnce(server: http.Server): void {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const originalEmit = server.emit;\n\n    // This means it was already patched, do nothing\n    if ((originalEmit as { __sentry_patched__?: boolean }).__sentry_patched__) {\n      return;\n    }\n\n    DEBUG_BUILD && logger.log(INSTRUMENTATION_NAME, 'Patching server.emit');\n\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const instrumentation = this;\n    const { ignoreIncomingRequestBody, maxIncomingRequestBodySize = 'medium' } = instrumentation.getConfig();\n\n    const newEmit = new Proxy(originalEmit, {\n      apply(target, thisArg, args: [event: string, ...args: unknown[]]) {\n        // Only traces request events\n        if (args[0] !== 'request') {\n          return target.apply(thisArg, args);\n        }\n\n        DEBUG_BUILD && logger.log(INSTRUMENTATION_NAME, 'Handling incoming request');\n\n        const isolationScope = getIsolationScope().clone();\n        const request = args[1] as http.IncomingMessage;\n        const response = args[2] as http.OutgoingMessage;\n\n        const normalizedRequest = httpRequestToRequestData(request);\n\n        // request.ip is non-standard but some frameworks set this\n        const ipAddress = (request as { ip?: string }).ip || request.socket?.remoteAddress;\n\n        const url = request.url || '/';\n        if (!ignoreIncomingRequestBody?.(url, request) && maxIncomingRequestBodySize !== 'none') {\n          patchRequestToCaptureBody(request, isolationScope, maxIncomingRequestBodySize);\n        }\n\n        // Update the isolation scope, isolate this request\n        isolationScope.setSDKProcessingMetadata({ normalizedRequest, ipAddress });\n\n        // attempt to update the scope's `transactionName` based on the request URL\n        // Ideally, framework instrumentations coming after the HttpInstrumentation\n        // update the transactionName once we get a parameterized route.\n        const httpMethod = (request.method || 'GET').toUpperCase();\n        const httpTarget = stripUrlQueryAndFragment(url);\n\n        const bestEffortTransactionName = `${httpMethod} ${httpTarget}`;\n\n        isolationScope.setTransactionName(bestEffortTransactionName);\n\n        if (instrumentation.getConfig().trackIncomingRequestsAsSessions !== false) {\n          recordRequestSession({\n            requestIsolationScope: isolationScope,\n            response,\n            sessionFlushingDelayMS: instrumentation.getConfig().sessionFlushingDelayMS ?? 60_000,\n          });\n        }\n\n        return withIsolationScope(isolationScope, () => {\n          // Set a new propagationSpanId for this request\n          // We rely on the fact that `withIsolationScope()` will implicitly also fork the current scope\n          // This way we can save an \"unnecessary\" `withScope()` invocation\n          getCurrentScope().getPropagationContext().propagationSpanId = generateSpanId();\n\n          // If we don't want to extract the trace from the header, we can skip this\n          if (!instrumentation.getConfig().extractIncomingTraceFromHeader) {\n            return target.apply(thisArg, args);\n          }\n\n          const ctx = propagation.extract(context.active(), normalizedRequest.headers);\n          return context.with(ctx, () => {\n            return target.apply(thisArg, args);\n          });\n        });\n      },\n    });\n\n    addNonEnumerableProperty(newEmit, '__sentry_patched__', true);\n\n    server.emit = newEmit;\n  }\n\n  /**\n   * Check if the given outgoing request should be ignored.\n   */\n  private _shouldIgnoreOutgoingRequest(request: http.ClientRequest): boolean {\n    if (isTracingSuppressed(context.active())) {\n      return true;\n    }\n\n    const ignoreOutgoingRequests = this.getConfig().ignoreOutgoingRequests;\n\n    if (!ignoreOutgoingRequests) {\n      return false;\n    }\n\n    const options = getRequestOptions(request);\n    const url = getRequestUrl(request);\n    return ignoreOutgoingRequests(url, options);\n  }\n}\n\n/** Add a breadcrumb for outgoing requests. */\nfunction addRequestBreadcrumb(request: http.ClientRequest, response: http.IncomingMessage | undefined): void {\n  const data = getBreadcrumbData(request);\n\n  const statusCode = response?.statusCode;\n  const level = getBreadcrumbLogLevelFromHttpStatusCode(statusCode);\n\n  addBreadcrumb(\n    {\n      category: 'http',\n      data: {\n        status_code: statusCode,\n        ...data,\n      },\n      type: 'http',\n      level,\n    },\n    {\n      event: 'response',\n      request,\n      response,\n    },\n  );\n}\n\nfunction getBreadcrumbData(request: http.ClientRequest): Partial<SanitizedRequestData> {\n  try {\n    // `request.host` does not contain the port, but the host header does\n    const host = request.getHeader('host') || request.host;\n    const url = new URL(request.path, `${request.protocol}//${host}`);\n    const parsedUrl = parseUrl(url.toString());\n\n    const data: Partial<SanitizedRequestData> = {\n      url: getSanitizedUrlString(parsedUrl),\n      'http.method': request.method || 'GET',\n    };\n\n    if (parsedUrl.search) {\n      data['http.query'] = parsedUrl.search;\n    }\n    if (parsedUrl.hash) {\n      data['http.fragment'] = parsedUrl.hash;\n    }\n\n    return data;\n  } catch {\n    return {};\n  }\n}\n\n/**\n * This method patches the request object to capture the body.\n * Instead of actually consuming the streamed body ourselves, which has potential side effects,\n * we monkey patch `req.on('data')` to intercept the body chunks.\n * This way, we only read the body if the user also consumes the body, ensuring we do not change any behavior in unexpected ways.\n */\nfunction patchRequestToCaptureBody(\n  req: http.IncomingMessage,\n  isolationScope: Scope,\n  maxIncomingRequestBodySize: 'small' | 'medium' | 'always',\n): void {\n  let bodyByteLength = 0;\n  const chunks: Buffer[] = [];\n\n  DEBUG_BUILD && logger.log(INSTRUMENTATION_NAME, 'Patching request.on');\n\n  /**\n   * We need to keep track of the original callbacks, in order to be able to remove listeners again.\n   * Since `off` depends on having the exact same function reference passed in, we need to be able to map\n   * original listeners to our wrapped ones.\n   */\n  const callbackMap = new WeakMap();\n\n  const maxBodySize =\n    maxIncomingRequestBodySize === 'small'\n      ? 1_000\n      : maxIncomingRequestBodySize === 'medium'\n        ? 10_000\n        : MAX_BODY_BYTE_LENGTH;\n\n  try {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    req.on = new Proxy(req.on, {\n      apply: (target, thisArg, args: Parameters<typeof req.on>) => {\n        const [event, listener, ...restArgs] = args;\n\n        if (event === 'data') {\n          DEBUG_BUILD &&\n            logger.log(INSTRUMENTATION_NAME, `Handling request.on(\"data\") with maximum body size of ${maxBodySize}b`);\n\n          const callback = new Proxy(listener, {\n            apply: (target, thisArg, args: Parameters<typeof listener>) => {\n              try {\n                const chunk = args[0] as Buffer | string;\n                const bufferifiedChunk = Buffer.from(chunk);\n\n                if (bodyByteLength < maxBodySize) {\n                  chunks.push(bufferifiedChunk);\n                  bodyByteLength += bufferifiedChunk.byteLength;\n                } else if (DEBUG_BUILD) {\n                  logger.log(\n                    INSTRUMENTATION_NAME,\n                    `Dropping request body chunk because maximum body length of ${maxBodySize}b is exceeded.`,\n                  );\n                }\n              } catch (err) {\n                DEBUG_BUILD && logger.error(INSTRUMENTATION_NAME, 'Encountered error while storing body chunk.');\n              }\n\n              return Reflect.apply(target, thisArg, args);\n            },\n          });\n\n          callbackMap.set(listener, callback);\n\n          return Reflect.apply(target, thisArg, [event, callback, ...restArgs]);\n        }\n\n        return Reflect.apply(target, thisArg, args);\n      },\n    });\n\n    // Ensure we also remove callbacks correctly\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    req.off = new Proxy(req.off, {\n      apply: (target, thisArg, args: Parameters<typeof req.off>) => {\n        const [, listener] = args;\n\n        const callback = callbackMap.get(listener);\n        if (callback) {\n          callbackMap.delete(listener);\n\n          const modifiedArgs = args.slice();\n          modifiedArgs[1] = callback;\n          return Reflect.apply(target, thisArg, modifiedArgs);\n        }\n\n        return Reflect.apply(target, thisArg, args);\n      },\n    });\n\n    req.on('end', () => {\n      try {\n        const body = Buffer.concat(chunks).toString('utf-8');\n        if (body) {\n          // Using Buffer.byteLength here, because the body may contain characters that are not 1 byte long\n          const bodyByteLength = Buffer.byteLength(body, 'utf-8');\n          const truncatedBody =\n            bodyByteLength > maxBodySize\n              ? `${Buffer.from(body)\n                  .subarray(0, maxBodySize - 3)\n                  .toString('utf-8')}...`\n              : body;\n\n          isolationScope.setSDKProcessingMetadata({ normalizedRequest: { data: truncatedBody } });\n        }\n      } catch (error) {\n        if (DEBUG_BUILD) {\n          logger.error(INSTRUMENTATION_NAME, 'Error building captured request body', error);\n        }\n      }\n    });\n  } catch (error) {\n    if (DEBUG_BUILD) {\n      logger.error(INSTRUMENTATION_NAME, 'Error patching request to capture body', error);\n    }\n  }\n}\n\nfunction getRequestOptions(request: http.ClientRequest): http.RequestOptions {\n  return {\n    method: request.method,\n    protocol: request.protocol,\n    host: request.host,\n    hostname: request.host,\n    path: request.path,\n    headers: request.getHeaders(),\n  };\n}\n\n/**\n * Starts a session and tracks it in the context of a given isolation scope.\n * When the passed response is finished, the session is put into a task and is\n * aggregated with other sessions that may happen in a certain time window\n * (sessionFlushingDelayMs).\n *\n * The sessions are always aggregated by the client that is on the current scope\n * at the time of ending the response (if there is one).\n */\n// Exported for unit tests\nexport function recordRequestSession({\n  requestIsolationScope,\n  response,\n  sessionFlushingDelayMS,\n}: {\n  requestIsolationScope: Scope;\n  response: EventEmitter;\n  sessionFlushingDelayMS?: number;\n}): void {\n  requestIsolationScope.setSDKProcessingMetadata({\n    requestSession: { status: 'ok' },\n  });\n  response.once('close', () => {\n    // We need to grab the client off the current scope instead of the isolation scope because the isolation scope doesn't hold any client out of the box.\n    const client = getClient();\n    const requestSession = requestIsolationScope.getScopeData().sdkProcessingMetadata.requestSession;\n\n    if (client && requestSession) {\n      DEBUG_BUILD && logger.debug(`Recorded request session with status: ${requestSession.status}`);\n\n      const roundedDate = new Date();\n      roundedDate.setSeconds(0, 0);\n      const dateBucketKey = roundedDate.toISOString();\n\n      const existingClientAggregate = clientToRequestSessionAggregatesMap.get(client);\n      const bucket = existingClientAggregate?.[dateBucketKey] || { exited: 0, crashed: 0, errored: 0 };\n      bucket[({ ok: 'exited', crashed: 'crashed', errored: 'errored' } as const)[requestSession.status]]++;\n\n      if (existingClientAggregate) {\n        existingClientAggregate[dateBucketKey] = bucket;\n      } else {\n        DEBUG_BUILD && logger.debug('Opened new request session aggregate.');\n        const newClientAggregate = { [dateBucketKey]: bucket };\n        clientToRequestSessionAggregatesMap.set(client, newClientAggregate);\n\n        const flushPendingClientAggregates = (): void => {\n          clearTimeout(timeout);\n          unregisterClientFlushHook();\n          clientToRequestSessionAggregatesMap.delete(client);\n\n          const aggregatePayload: AggregationCounts[] = Object.entries(newClientAggregate).map(\n            ([timestamp, value]) => ({\n              started: timestamp,\n              exited: value.exited,\n              errored: value.errored,\n              crashed: value.crashed,\n            }),\n          );\n          client.sendSession({ aggregates: aggregatePayload });\n        };\n\n        const unregisterClientFlushHook = client.on('flush', () => {\n          DEBUG_BUILD && logger.debug('Sending request session aggregate due to client flush');\n          flushPendingClientAggregates();\n        });\n        const timeout = setTimeout(() => {\n          DEBUG_BUILD && logger.debug('Sending request session aggregate due to flushing schedule');\n          flushPendingClientAggregates();\n        }, sessionFlushingDelayMS).unref();\n      }\n    }\n  });\n}\n\nconst clientToRequestSessionAggregatesMap = new Map<\n  Client,\n  { [timestampRoundedToSeconds: string]: { exited: number; crashed: number; errored: number } }\n>();\n"], "names": [], "mappings": ";;;;;;;;;;AAkCA,MAAM,oBAAA,GAAuB,8BAA8B;;AAmF3D;AACA,MAAM,oBAAqB,GAAE,IAAK,GAAE,IAAI;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,yBAAA,SAAkC,mBAAmB,CAAmC;;AAIrG,GAAS,WAAW,CAAC,MAAM,GAAqC,EAAE,EAAE;AACpE,IAAI,KAAK,CAAC,oBAAoB,EAAE,OAAO,EAAE,MAAM,CAAC;;AAEhD,IAAI,IAAI,CAAC,uBAAwB,GAAE,IAAI,MAAM,CAAkB,GAAG,CAAC;AACnE,IAAI,IAAI,CAAC,0BAAA,GAA6B,IAAI,OAAO,EAA+B;AAChF;;AAEA;AACA,GAAS,IAAI,GAA+E;AAC5F;AACA;AACA,IAAI,IAAI,qBAAsB,GAAE,KAAK;;AAErC,IAAI,MAAM,wBAAyB,IAAG,CAAC,KAAK,KAAc;AAC1D,MAAM,MAAM,IAAK,GAAE,KAAM;AACzB,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC;AAC5C,KAAK,CAAE;;AAEP,IAAI,MAAM,0BAA2B,IAAG,CAAC,KAAK,KAAc;AAC5D,MAAM,MAAM,IAAK,GAAE,KAAM;AACzB,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC;AAChE,KAAK,CAAE;;AAEP,IAAI,MAAM,wBAAyB,IAAG,CAAC,KAAK,KAAc;AAC1D,MAAM,MAAM,IAAK,GAAE,KAAM;AACzB,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC;AAC5D,KAAK,CAAE;;AAEP,IAAI,MAAM,0BAA2B,IAAG,CAAC,KAAK,KAAc;AAC5D,MAAM,MAAM,IAAK,GAAE,KAAM;AACzB,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC;AAClD,KAAK,CAAE;;AAEP,IAAI,MAAM,IAAA,GAAO,CAAyB,aAAa,KAAW;AAClE,MAAM,IAAI,qBAAqB,EAAE;AACjC,QAAQ,OAAO,aAAa;AAC5B;;AAEA,MAAM,qBAAA,GAAwB,IAAI;;AAElC,MAAM,SAAS,CAAC,2BAA2B,EAAE,wBAAwB,CAAC;AACtE,MAAM,SAAS,CAAC,6BAA6B,EAAE,0BAA0B,CAAC;;AAE1E;AACA;AACA,MAAM,SAAS,CAAC,2BAA2B,EAAE,wBAAwB,CAAC;;AAEtE;AACA;AACA;AACA,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,gCAAgC,EAAE;AAC7D,QAAQ,SAAS,CAAC,6BAA6B,EAAE,0BAA0B,CAAC;AAC5E;;AAEA,MAAM,OAAO,aAAa;AAC1B,KAAK;;AAEL,IAAI,MAAM,MAAA,GAAS,MAAY;AAC/B,MAAM,WAAW,CAAC,2BAA2B,EAAE,wBAAwB,CAAC;AACxE,MAAM,WAAW,CAAC,6BAA6B,EAAE,0BAA0B,CAAC;AAC5E,MAAM,WAAW,CAAC,2BAA2B,EAAE,wBAAwB,CAAC;AACxE,MAAM,WAAW,CAAC,6BAA6B,EAAE,0BAA0B,CAAC;AAC5E,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO;AACX,MAAM,IAAI,mCAAmC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC;AAC1E,MAAM,IAAI,mCAAmC,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC;AAC3E,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA,GAAU,wBAAwB,CAAC,OAAO,EAAsB,QAAQ,EAA+B;AACvG,IAAI,WAAA,IAAe,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,oCAAoC,CAAC;;AAEzF,IAAI,MAAM,eAAe,IAAI,CAAC,SAAS,EAAE,CAAC,WAAW;AACrD,IAAI,MAAM,kBAAmB,GAAE,OAAO,YAAA,KAAiB,WAAY,GAAE,IAAK,GAAE,YAAY;;AAExF;AACA,IAAI,MAAM,YAAa,GAAE,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,OAAO,KAAK,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;AACnH,IAAI,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;;AAE9D,IAAI,IAAI,kBAAA,IAAsB,CAAC,YAAY,EAAE;AAC7C,MAAM,oBAAoB,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC7C;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAU,yBAAyB,CAAC,OAAO,EAA4B;AACvE,IAAI,MAAM,YAAa,GAAE,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,OAAO,KAAK,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;AACnH,IAAI,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;;AAE9D,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM;AACN;;AAEA;AACA,IAAI,MAAM,GAAI,GAAE,aAAa,CAAC,OAAO,CAAC;;AAEtC;AACA;AACA;AACA,IAAI,MAAM,uBAAwB,GAAE,SAAS,EAAE,EAAE,UAAU,EAAE,CAAC,uBAAuB;AACrF,IAAI,MAAM,YAAa,GAAE,0BAA0B,CAAC,GAAG,EAAE,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;AAC9G,QAAQ,YAAY;AACpB,QAAQ,SAAS;;AAEjB,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM;AACN;;AAEA,IAAI,MAAM,EAAE,cAAc,EAAE,WAAW,EAAE,OAAA,EAAU,GAAE,YAAY;;AAEjE;AACA,IAAI,IAAI,WAAA,IAAe,CAAC,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;AAC3D,MAAM,IAAI;AACV,QAAQ,OAAO,CAAC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC;AACtD,QAAQ,WAAA,IAAe,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,+CAA+C,CAAC;AACxG,OAAQ,CAAA,OAAO,KAAK,EAAE;AACtB,QAAQ,WAAY;AACpB,UAAU,MAAM,CAAC,KAAK;AACtB,YAAY,oBAAoB;AAChC,YAAY,wDAAwD;AACpE,YAAY,OAAO,CAAC,KAAK,CAAA,GAAI,KAAK,CAAC,OAAQ,GAAE,eAAe;AAC5D,WAAW;AACX;AACA;;AAEA,IAAI,IAAI,OAAO,EAAE;AACjB;AACA,MAAM,MAAM,UAAA,GAAa,mBAAmB,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC;AACnF,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,IAAI;AACZ,UAAU,OAAO,CAAC,SAAS,CAAC,SAAS,EAAE,UAAU,CAAC;AAClD,UAAU,WAAA,IAAe,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,0CAA0C,CAAC;AACrG,SAAU,CAAA,OAAO,KAAK,EAAE;AACxB,UAAU,WAAY;AACtB,YAAY,MAAM,CAAC,KAAK;AACxB,cAAc,oBAAoB;AAClC,cAAc,mDAAmD;AACjE,cAAc,OAAO,CAAC,KAAK,CAAA,GAAI,KAAK,CAAC,OAAQ,GAAE,eAAe;AAC9D,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAU,oBAAoB,CAAC,MAAM,EAAqB;AAC1D;AACA,IAAI,MAAM,YAAA,GAAe,MAAM,CAAC,IAAI;;AAEpC;AACA,IAAI,IAAI,CAAC,eAAkD,kBAAkB,EAAE;AAC/E,MAAM;AACN;;AAEA,IAAI,WAAA,IAAe,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,sBAAsB,CAAC;;AAE3E;AACA,IAAI,MAAM,eAAgB,GAAE,IAAI;AAChC,IAAI,MAAM,EAAE,yBAAyB,EAAE,0BAA2B,GAAE,QAAS,EAAA,GAAI,eAAe,CAAC,SAAS,EAAE;;AAE5G,IAAI,MAAM,OAAQ,GAAE,IAAI,KAAK,CAAC,YAAY,EAAE;AAC5C,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAuC;AACxE;AACA,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAE,KAAI,SAAS,EAAE;AACnC,UAAU,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;AAC5C;;AAEA,QAAQ,WAAA,IAAe,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,2BAA2B,CAAC;;AAEpF,QAAQ,MAAM,iBAAiB,iBAAiB,EAAE,CAAC,KAAK,EAAE;AAC1D,QAAQ,MAAM,OAAQ,GAAE,IAAI,CAAC,CAAC,CAAE;AAChC,QAAQ,MAAM,QAAS,GAAE,IAAI,CAAC,CAAC,CAAE;;AAEjC,QAAQ,MAAM,iBAAkB,GAAE,wBAAwB,CAAC,OAAO,CAAC;;AAEnE;AACA,QAAQ,MAAM,SAAA,GAAY,CAAC,OAAQ,GAAoB,EAAA,IAAM,OAAO,CAAC,MAAM,EAAE,aAAa;;AAE1F,QAAQ,MAAM,GAAI,GAAE,OAAO,CAAC,GAAA,IAAO,GAAG;AACtC,QAAQ,IAAI,CAAC,yBAAyB,GAAG,GAAG,EAAE,OAAO,CAAE,IAAG,0BAA2B,KAAI,MAAM,EAAE;AACjG,UAAU,yBAAyB,CAAC,OAAO,EAAE,cAAc,EAAE,0BAA0B,CAAC;AACxF;;AAEA;AACA,QAAQ,cAAc,CAAC,wBAAwB,CAAC,EAAE,iBAAiB,EAAE,SAAU,EAAC,CAAC;;AAEjF;AACA;AACA;AACA,QAAQ,MAAM,UAAA,GAAa,CAAC,OAAO,CAAC,MAAO,IAAG,KAAK,EAAE,WAAW,EAAE;AAClE,QAAQ,MAAM,UAAW,GAAE,wBAAwB,CAAC,GAAG,CAAC;;AAExD,QAAQ,MAAM,yBAAA,GAA4B,CAAC,EAAA,UAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA;;AAEA,QAAA,cAAA,CAAA,kBAAA,CAAA,yBAAA,CAAA;;AAEA,QAAA,IAAA,eAAA,CAAA,SAAA,EAAA,CAAA,+BAAA,KAAA,KAAA,EAAA;AACA,UAAA,oBAAA,CAAA;AACA,YAAA,qBAAA,EAAA,cAAA;AACA,YAAA,QAAA;AACA,YAAA,sBAAA,EAAA,eAAA,CAAA,SAAA,EAAA,CAAA,sBAAA,IAAA,KAAA;AACA,WAAA,CAAA;AACA;;AAEA,QAAA,OAAA,kBAAA,CAAA,cAAA,EAAA,MAAA;AACA;AACA;AACA;AACA,UAAA,eAAA,EAAA,CAAA,qBAAA,EAAA,CAAA,iBAAA,GAAA,cAAA,EAAA;;AAEA;AACA,UAAA,IAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,8BAAA,EAAA;AACA,YAAA,OAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA;AACA;;AAEA,UAAA,MAAA,GAAA,GAAA,WAAA,CAAA,OAAA,CAAA,OAAA,CAAA,MAAA,EAAA,EAAA,iBAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,MAAA;AACA,YAAA,OAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA;AACA,WAAA,CAAA;AACA,SAAA,CAAA;AACA,OAAA;AACA,KAAA,CAAA;;AAEA,IAAA,wBAAA,CAAA,OAAA,EAAA,oBAAA,EAAA,IAAA,CAAA;;AAEA,IAAA,MAAA,CAAA,IAAA,GAAA,OAAA;AACA;;AAEA;AACA;AACA;AACA,GAAA,4BAAA,CAAA,OAAA,EAAA;AACA,IAAA,IAAA,mBAAA,CAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA;AACA,MAAA,OAAA,IAAA;AACA;;AAEA,IAAA,MAAA,sBAAA,GAAA,IAAA,CAAA,SAAA,EAAA,CAAA,sBAAA;;AAEA,IAAA,IAAA,CAAA,sBAAA,EAAA;AACA,MAAA,OAAA,KAAA;AACA;;AAEA,IAAA,MAAA,OAAA,GAAA,iBAAA,CAAA,OAAA,CAAA;AACA,IAAA,MAAA,GAAA,GAAA,aAAA,CAAA,OAAA,CAAA;AACA,IAAA,OAAA,sBAAA,CAAA,GAAA,EAAA,OAAA,CAAA;AACA;AACA;;AAEA;AACA,SAAA,oBAAA,CAAA,OAAA,EAAA,QAAA,EAAA;AACA,EAAA,MAAA,IAAA,GAAA,iBAAA,CAAA,OAAA,CAAA;;AAEA,EAAA,MAAA,UAAA,GAAA,QAAA,EAAA,UAAA;AACA,EAAA,MAAA,KAAA,GAAA,uCAAA,CAAA,UAAA,CAAA;;AAEA,EAAA,aAAA;AACA,IAAA;AACA,MAAA,QAAA,EAAA,MAAA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,WAAA,EAAA,UAAA;AACA,QAAA,GAAA,IAAA;AACA,OAAA;AACA,MAAA,IAAA,EAAA,MAAA;AACA,MAAA,KAAA;AACA,KAAA;AACA,IAAA;AACA,MAAA,KAAA,EAAA,UAAA;AACA,MAAA,OAAA;AACA,MAAA,QAAA;AACA,KAAA;AACA,GAAA;AACA;;AAEA,SAAA,iBAAA,CAAA,OAAA,EAAA;AACA,EAAA,IAAA;AACA;AACA,IAAA,MAAA,IAAA,GAAA,OAAA,CAAA,SAAA,CAAA,MAAA,CAAA,IAAA,OAAA,CAAA,IAAA;AACA,IAAA,MAAA,GAAA,GAAA,IAAA,GAAA,CAAA,OAAA,CAAA,IAAA,EAAA,CAAA,EAAA,OAAA,CAAA,QAAA,CAAA,EAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAA,SAAA,GAAA,QAAA,CAAA,GAAA,CAAA,QAAA,EAAA,CAAA;;AAEA,IAAA,MAAA,IAAA,GAAA;AACA,MAAA,GAAA,EAAA,qBAAA,CAAA,SAAA,CAAA;AACA,MAAA,aAAA,EAAA,OAAA,CAAA,MAAA,IAAA,KAAA;AACA,KAAA;;AAEA,IAAA,IAAA,SAAA,CAAA,MAAA,EAAA;AACA,MAAA,IAAA,CAAA,YAAA,CAAA,GAAA,SAAA,CAAA,MAAA;AACA;AACA,IAAA,IAAA,SAAA,CAAA,IAAA,EAAA;AACA,MAAA,IAAA,CAAA,eAAA,CAAA,GAAA,SAAA,CAAA,IAAA;AACA;;AAEA,IAAA,OAAA,IAAA;AACA,GAAA,CAAA,MAAA;AACA,IAAA,OAAA,EAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,yBAAA;AACA,EAAA,GAAA;AACA,EAAA,cAAA;AACA,EAAA,0BAAA;AACA,EAAA;AACA,EAAA,IAAA,cAAA,GAAA,CAAA;AACA,EAAA,MAAA,MAAA,GAAA,EAAA;;AAEA,EAAA,WAAA,IAAA,MAAA,CAAA,GAAA,CAAA,oBAAA,EAAA,qBAAA,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA,EAAA,MAAA,WAAA,GAAA,IAAA,OAAA,EAAA;;AAEA,EAAA,MAAA,WAAA;AACA,IAAA,0BAAA,KAAA;AACA,QAAA;AACA,QAAA,0BAAA,KAAA;AACA,UAAA;AACA,UAAA,oBAAA;;AAEA,EAAA,IAAA;AACA;AACA,IAAA,GAAA,CAAA,EAAA,GAAA,IAAA,KAAA,CAAA,GAAA,CAAA,EAAA,EAAA;AACA,MAAA,KAAA,EAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,KAAA;AACA,QAAA,MAAA,CAAA,KAAA,EAAA,QAAA,EAAA,GAAA,QAAA,CAAA,GAAA,IAAA;;AAEA,QAAA,IAAA,KAAA,KAAA,MAAA,EAAA;AACA,UAAA,WAAA;AACA,YAAA,MAAA,CAAA,GAAA,CAAA,oBAAA,EAAA,CAAA,sDAAA,EAAA,WAAA,CAAA,CAAA,CAAA,CAAA;;AAEA,UAAA,MAAA,QAAA,GAAA,IAAA,KAAA,CAAA,QAAA,EAAA;AACA,YAAA,KAAA,EAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,KAAA;AACA,cAAA,IAAA;AACA,gBAAA,MAAA,KAAA,GAAA,IAAA,CAAA,CAAA,CAAA;AACA,gBAAA,MAAA,gBAAA,GAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA;;AAEA,gBAAA,IAAA,cAAA,GAAA,WAAA,EAAA;AACA,kBAAA,MAAA,CAAA,IAAA,CAAA,gBAAA,CAAA;AACA,kBAAA,cAAA,IAAA,gBAAA,CAAA,UAAA;AACA,iBAAA,MAAA,IAAA,WAAA,EAAA;AACA,kBAAA,MAAA,CAAA,GAAA;AACA,oBAAA,oBAAA;AACA,oBAAA,CAAA,2DAAA,EAAA,WAAA,CAAA,cAAA,CAAA;AACA,mBAAA;AACA;AACA,eAAA,CAAA,OAAA,GAAA,EAAA;AACA,gBAAA,WAAA,IAAA,MAAA,CAAA,KAAA,CAAA,oBAAA,EAAA,6CAAA,CAAA;AACA;;AAEA,cAAA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,CAAA;AACA,aAAA;AACA,WAAA,CAAA;;AAEA,UAAA,WAAA,CAAA,GAAA,CAAA,QAAA,EAAA,QAAA,CAAA;;AAEA,UAAA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,CAAA,KAAA,EAAA,QAAA,EAAA,GAAA,QAAA,CAAA,CAAA;AACA;;AAEA,QAAA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,CAAA;AACA,OAAA;AACA,KAAA,CAAA;;AAEA;AACA;AACA,IAAA,GAAA,CAAA,GAAA,GAAA,IAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA;AACA,MAAA,KAAA,EAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,KAAA;AACA,QAAA,MAAA,GAAA,QAAA,CAAA,GAAA,IAAA;;AAEA,QAAA,MAAA,QAAA,GAAA,WAAA,CAAA,GAAA,CAAA,QAAA,CAAA;AACA,QAAA,IAAA,QAAA,EAAA;AACA,UAAA,WAAA,CAAA,MAAA,CAAA,QAAA,CAAA;;AAEA,UAAA,MAAA,YAAA,GAAA,IAAA,CAAA,KAAA,EAAA;AACA,UAAA,YAAA,CAAA,CAAA,CAAA,GAAA,QAAA;AACA,UAAA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,YAAA,CAAA;AACA;;AAEA,QAAA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,CAAA;AACA,OAAA;AACA,KAAA,CAAA;;AAEA,IAAA,GAAA,CAAA,EAAA,CAAA,KAAA,EAAA,MAAA;AACA,MAAA,IAAA;AACA,QAAA,MAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,QAAA,CAAA,OAAA,CAAA;AACA,QAAA,IAAA,IAAA,EAAA;AACA;AACA,UAAA,MAAA,cAAA,GAAA,MAAA,CAAA,UAAA,CAAA,IAAA,EAAA,OAAA,CAAA;AACA,UAAA,MAAA,aAAA;AACA,YAAA,cAAA,GAAA;AACA,gBAAA,CAAA,EAAA,MAAA,CAAA,IAAA,CAAA,IAAA;AACA,mBAAA,QAAA,CAAA,CAAA,EAAA,WAAA,GAAA,CAAA;AACA,mBAAA,QAAA,CAAA,OAAA,CAAA,CAAA,GAAA;AACA,gBAAA,IAAA;;AAEA,UAAA,cAAA,CAAA,wBAAA,CAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,aAAA,EAAA,EAAA,CAAA;AACA;AACA,OAAA,CAAA,OAAA,KAAA,EAAA;AACA,QAAA,IAAA,WAAA,EAAA;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,oBAAA,EAAA,sCAAA,EAAA,KAAA,CAAA;AACA;AACA;AACA,KAAA,CAAA;AACA,GAAA,CAAA,OAAA,KAAA,EAAA;AACA,IAAA,IAAA,WAAA,EAAA;AACA,MAAA,MAAA,CAAA,KAAA,CAAA,oBAAA,EAAA,wCAAA,EAAA,KAAA,CAAA;AACA;AACA;AACA;;AAEA,SAAA,iBAAA,CAAA,OAAA,EAAA;AACA,EAAA,OAAA;AACA,IAAA,MAAA,EAAA,OAAA,CAAA,MAAA;AACA,IAAA,QAAA,EAAA,OAAA,CAAA,QAAA;AACA,IAAA,IAAA,EAAA,OAAA,CAAA,IAAA;AACA,IAAA,QAAA,EAAA,OAAA,CAAA,IAAA;AACA,IAAA,IAAA,EAAA,OAAA,CAAA,IAAA;AACA,IAAA,OAAA,EAAA,OAAA,CAAA,UAAA,EAAA;AACA,GAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,oBAAA,CAAA;AACA,EAAA,qBAAA;AACA,EAAA,QAAA;AACA,EAAA,sBAAA;AACA;;AAIA,EAAA;AACA,EAAA,qBAAA,CAAA,wBAAA,CAAA;AACA,IAAA,cAAA,EAAA,EAAA,MAAA,EAAA,IAAA,EAAA;AACA,GAAA,CAAA;AACA,EAAA,QAAA,CAAA,IAAA,CAAA,OAAA,EAAA,MAAA;AACA;AACA,IAAA,MAAA,MAAA,GAAA,SAAA,EAAA;AACA,IAAA,MAAA,cAAA,GAAA,qBAAA,CAAA,YAAA,EAAA,CAAA,qBAAA,CAAA,cAAA;;AAEA,IAAA,IAAA,MAAA,IAAA,cAAA,EAAA;AACA,MAAA,WAAA,IAAA,MAAA,CAAA,KAAA,CAAA,CAAA,sCAAA,EAAA,cAAA,CAAA,MAAA,CAAA,CAAA,CAAA;;AAEA,MAAA,MAAA,WAAA,GAAA,IAAA,IAAA,EAAA;AACA,MAAA,WAAA,CAAA,UAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA,MAAA,MAAA,aAAA,GAAA,WAAA,CAAA,WAAA,EAAA;;AAEA,MAAA,MAAA,uBAAA,GAAA,mCAAA,CAAA,GAAA,CAAA,MAAA,CAAA;AACA,MAAA,MAAA,MAAA,GAAA,uBAAA,GAAA,aAAA,CAAA,IAAA,EAAA,MAAA,EAAA,CAAA,EAAA,OAAA,EAAA,CAAA,EAAA,OAAA,EAAA,CAAA,EAAA;AACA,MAAA,MAAA,CAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,GAAA,cAAA,CAAA,MAAA,CAAA,CAAA,EAAA;;AAEA,MAAA,IAAA,uBAAA,EAAA;AACA,QAAA,uBAAA,CAAA,aAAA,CAAA,GAAA,MAAA;AACA,OAAA,MAAA;AACA,QAAA,WAAA,IAAA,MAAA,CAAA,KAAA,CAAA,uCAAA,CAAA;AACA,QAAA,MAAA,kBAAA,GAAA,EAAA,CAAA,aAAA,GAAA,MAAA,EAAA;AACA,QAAA,mCAAA,CAAA,GAAA,CAAA,MAAA,EAAA,kBAAA,CAAA;;AAEA,QAAA,MAAA,4BAAA,GAAA,MAAA;AACA,UAAA,YAAA,CAAA,OAAA,CAAA;AACA,UAAA,yBAAA,EAAA;AACA,UAAA,mCAAA,CAAA,MAAA,CAAA,MAAA,CAAA;;AAEA,UAAA,MAAA,gBAAA,GAAA,MAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,CAAA,GAAA;AACA,YAAA,CAAA,CAAA,SAAA,EAAA,KAAA,CAAA,MAAA;AACA,cAAA,OAAA,EAAA,SAAA;AACA,cAAA,MAAA,EAAA,KAAA,CAAA,MAAA;AACA,cAAA,OAAA,EAAA,KAAA,CAAA,OAAA;AACA,cAAA,OAAA,EAAA,KAAA,CAAA,OAAA;AACA,aAAA,CAAA;AACA,WAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,CAAA;AACA,SAAA;;AAEA,QAAA,MAAA,yBAAA,GAAA,MAAA,CAAA,EAAA,CAAA,OAAA,EAAA,MAAA;AACA,UAAA,WAAA,IAAA,MAAA,CAAA,KAAA,CAAA,uDAAA,CAAA;AACA,UAAA,4BAAA,EAAA;AACA,SAAA,CAAA;AACA,QAAA,MAAA,OAAA,GAAA,UAAA,CAAA,MAAA;AACA,UAAA,WAAA,IAAA,MAAA,CAAA,KAAA,CAAA,4DAAA,CAAA;AACA,UAAA,4BAAA,EAAA;AACA,SAAA,EAAA,sBAAA,CAAA,CAAA,KAAA,EAAA;AACA;AACA;AACA,GAAA,CAAA;AACA;;AAEA,MAAA,mCAAA,GAAA,IAAA;;AAGA,EAAA;;;;"}