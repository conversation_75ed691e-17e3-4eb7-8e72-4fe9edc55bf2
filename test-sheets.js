const { GoogleSpreadsheet } = require('google-spreadsheet');
const { JWT } = require('google-auth-library');
require('dotenv').config();
const fs = require('fs');
const path = require('path');

async function testGoogleSheetsConnection() {
    console.log('🔍 Kiểm tra kết nối Google Sheets...\n');
    
    // 1. Kiểm tra file credentials
    const credentialsPath = path.join(__dirname, 'google-credentials.json');
    console.log('📁 Kiểm tra file credentials:', credentialsPath);
    
    if (!fs.existsSync(credentialsPath)) {
        console.log('❌ KHÔNG TÌM THẤY FILE google-credentials.json');
        console.log('📋 Bạn cần:');
        console.log('   1. Tạo Service Account trên Google Cloud Console');
        console.log('   2. Download JSON key file');
        console.log('   3. <PERSON><PERSON><PERSON> tên thành "google-credentials.json"');
        console.log('   4. Copy vào thư mục gốc của project');
        console.log('\n📖 Xem hướng dẫn chi tiết trong file SETUP-GOOGLE-SHEETS.md');
        return;
    }
    
    console.log('✅ Tìm thấy file credentials');
    
    // 2. Kiểm tra GOOGLE_SHEET_ID
    const sheetId = process.env.GOOGLE_SHEET_ID;
    console.log('📊 Google Sheet ID:', sheetId);
    
    if (!sheetId) {
        console.log('❌ KHÔNG TÌM THẤY GOOGLE_SHEET_ID trong file .env');
        return;
    }
    
    try {
        // 3. Đọc credentials
        const credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));
        console.log('✅ Đọc credentials thành công');
        console.log('📧 Service Account Email:', credentials.client_email);
        
        // 4. Tạo JWT auth
        const serviceAccountAuth = new JWT({
            email: credentials.client_email,
            key: credentials.private_key,
            scopes: ['https://www.googleapis.com/auth/spreadsheets'],
        });
        
        console.log('✅ Tạo JWT auth thành công');
        
        // 5. Kết nối Google Sheet
        console.log('🔗 Đang kết nối Google Sheet...');
        const doc = new GoogleSpreadsheet(sheetId, serviceAccountAuth);
        
        await doc.loadInfo();
        console.log('✅ Kết nối Google Sheet thành công!');
        console.log('📋 Tên Sheet:', doc.title);
        console.log('📄 Số lượng sheets:', doc.sheetCount);
        
        // 6. Kiểm tra các sheets
        console.log('\n📑 Danh sách sheets:');
        doc.sheetsByIndex.forEach((sheet, index) => {
            console.log(`   ${index + 1}. "${sheet.title}" (${sheet.rowCount} rows, ${sheet.columnCount} cols)`);
        });
        
        // 7. Kiểm tra sheet "1 Month" và "1 Year"
        const oneMonthSheet = doc.sheetsByTitle['1 Month'];
        const oneYearSheet = doc.sheetsByTitle['1 Year'];
        
        if (!oneMonthSheet) {
            console.log('⚠️  Không tìm thấy sheet "1 Month" - cần tạo sheet này');
        } else {
            console.log('✅ Tìm thấy sheet "1 Month"');
        }
        
        if (!oneYearSheet) {
            console.log('⚠️  Không tìm thấy sheet "1 Year" - cần tạo sheet này');
        } else {
            console.log('✅ Tìm thấy sheet "1 Year"');
        }
        
        // 8. Test ghi dữ liệu
        if (oneMonthSheet) {
            console.log('\n🧪 Test ghi dữ liệu vào sheet "1 Month"...');
            try {
                await oneMonthSheet.loadHeaderRow();
                console.log('📋 Headers hiện tại:', oneMonthSheet.headerValues);
                
                // Thêm test row
                const testRow = await oneMonthSheet.addRow({
                    'Email': '<EMAIL>',
                    'Date Added': new Date().toLocaleString('vi-VN'),
                    'Duration': '1 Month',
                    'Status': 'Test'
                });
                
                console.log('✅ Test ghi dữ liệu thành công!');
                console.log('🗑️  Xóa test row...');
                await testRow.delete();
                console.log('✅ Xóa test row thành công!');
                
            } catch (error) {
                console.log('❌ Lỗi khi test ghi dữ liệu:', error.message);
            }
        }
        
        console.log('\n🎉 KIỂM TRA HOÀN TẤT!');
        console.log('✅ Google Sheets đã sẵn sàng sử dụng');
        
    } catch (error) {
        console.log('❌ LỖI KẾT NỐI:', error.message);
        
        if (error.message.includes('No access, refresh token')) {
            console.log('💡 Có thể do:');
            console.log('   - Chưa share Sheet với service account email');
            console.log('   - Service account không có quyền truy cập');
        }
        
        if (error.message.includes('Unable to parse')) {
            console.log('💡 File credentials có thể bị lỗi format JSON');
        }
        
        if (error.message.includes('Requested entity was not found')) {
            console.log('💡 Google Sheet ID không đúng hoặc không tồn tại');
        }
    }
}

// Chạy test
testGoogleSheetsConnection().catch(console.error);
