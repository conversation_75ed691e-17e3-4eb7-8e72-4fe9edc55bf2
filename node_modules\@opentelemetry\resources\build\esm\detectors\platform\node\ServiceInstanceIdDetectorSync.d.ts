import { Resource } from '../../../Resource';
import { DetectorSync } from '../../../types';
import { ResourceDetectionConfig } from '../../../config';
/**
 * ServiceInstanceIdDetectorSync detects the resources related to the service instance ID.
 */
declare class ServiceInstanceIdDetectorSync implements DetectorSync {
    detect(_config?: ResourceDetectionConfig): Resource;
}
/**
 * @experimental
 */
export declare const serviceInstanceIdDetectorSync: ServiceInstanceIdDetectorSync;
export {};
//# sourceMappingURL=ServiceInstanceIdDetectorSync.d.ts.map