import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace verifiedaccess_v2 {
    export interface Options extends GlobalOptions {
        version: 'v2';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Chrome Verified Access API
     *
     * API for Verified Access chrome extension to provide credential verification for chrome devices connecting to an enterprise network
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const verifiedaccess = google.verifiedaccess('v2');
     * ```
     */
    export class Verifiedaccess {
        context: APIRequestContext;
        challenge: Resource$Challenge;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Antivirus information on a device.
     */
    export interface Schema$Antivirus {
        /**
         * Output only. The state of the antivirus on the device. Introduced in Chrome M136.
         */
        state?: string | null;
    }
    /**
     * Result message for VerifiedAccess.GenerateChallenge.
     */
    export interface Schema$Challenge {
        /**
         * Generated challenge, the bytes representation of SignedData.
         */
        challenge?: string | null;
    }
    /**
     * Properties of the CrowdStrike agent installed on a device.
     */
    export interface Schema$CrowdStrikeAgent {
        /**
         * Output only. The Agent ID of the Crowdstrike agent.
         */
        agentId?: string | null;
        /**
         * Output only. The Customer ID to which the agent belongs to.
         */
        customerId?: string | null;
    }
    /**
     * The device signals as reported by Chrome. Unless otherwise specified, signals are available on all platforms.
     */
    export interface Schema$DeviceSignals {
        /**
         * Output only. Value of the AllowScreenLock policy on the device. See https://chromeenterprise.google/policies/?policy=AllowScreenLock for more details. Available on ChromeOS only.
         */
        allowScreenLock?: boolean | null;
        /**
         * Output only. Information about Antivirus software on the device. Available on Windows only.
         */
        antivirus?: Schema$Antivirus;
        /**
         * Output only. Current version of the Chrome browser which generated this set of signals. Example value: "107.0.5286.0".
         */
        browserVersion?: string | null;
        /**
         * Output only. Whether Chrome's built-in DNS client is used. The OS DNS client is otherwise used. This value may be controlled by an enterprise policy: https://chromeenterprise.google/policies/#BuiltInDnsClientEnabled.
         */
        builtInDnsClientEnabled?: boolean | null;
        /**
         * Output only. Whether access to the Chrome Remote Desktop application is blocked via a policy.
         */
        chromeRemoteDesktopAppBlocked?: boolean | null;
        /**
         * Output only. Crowdstrike agent properties installed on the device, if any. Available on Windows and MacOS only.
         */
        crowdStrikeAgent?: Schema$CrowdStrikeAgent;
        /**
         * Output only. Affiliation IDs of the organizations that are affiliated with the organization that is currently managing the device. When the sets of device and profile affiliation IDs overlap, it means that the organizations managing the device and user are affiliated. To learn more about user affiliation, visit https://support.google.com/chrome/a/answer/12801245?ref_topic=9027936.
         */
        deviceAffiliationIds?: string[] | null;
        /**
         * Output only. Enrollment domain of the customer which is currently managing the device.
         */
        deviceEnrollmentDomain?: string | null;
        /**
         * Output only. The name of the device's manufacturer.
         */
        deviceManufacturer?: string | null;
        /**
         * Output only. The name of the device's model.
         */
        deviceModel?: string | null;
        /**
         * Output only. The encryption state of the disk. On ChromeOS, the main disk is always ENCRYPTED.
         */
        diskEncryption?: string | null;
        /**
         * Output only. The display name of the device, as defined by the user.
         */
        displayName?: string | null;
        /**
         * Hostname of the device.
         */
        hostname?: string | null;
        /**
         * Output only. International Mobile Equipment Identity (IMEI) of the device. Available on ChromeOS only.
         */
        imei?: string[] | null;
        /**
         * Output only. MAC addresses of the device.
         */
        macAddresses?: string[] | null;
        /**
         * Output only. Mobile Equipment Identifier (MEID) of the device. Available on ChromeOS only.
         */
        meid?: string[] | null;
        /**
         * Output only. The type of the Operating System currently running on the device.
         */
        operatingSystem?: string | null;
        /**
         * Output only. The state of the OS level firewall. On ChromeOS, the value will always be ENABLED on regular devices and UNKNOWN on devices in developer mode. Support for MacOS 15 (Sequoia) and later has been introduced in Chrome M131.
         */
        osFirewall?: string | null;
        /**
         * Output only. The current version of the Operating System. On Windows and linux, the value will also include the security patch information.
         */
        osVersion?: string | null;
        /**
         * Output only. Whether the Password Protection Warning feature is enabled or not. Password protection alerts users when they reuse their protected password on potentially suspicious sites. This setting is controlled by an enterprise policy: https://chromeenterprise.google/policies/#PasswordProtectionWarningTrigger. Note that the policy unset does not have the same effects as having the policy explicitly set to `PASSWORD_PROTECTION_OFF`.
         */
        passwordProtectionWarningTrigger?: string | null;
        /**
         * Output only. Affiliation IDs of the organizations that are affiliated with the organization that is currently managing the Chrome Profile’s user or ChromeOS user.
         */
        profileAffiliationIds?: string[] | null;
        /**
         * Output only. Enrollment domain of the customer which is currently managing the profile.
         */
        profileEnrollmentDomain?: string | null;
        /**
         * Output only. Whether Enterprise-grade (i.e. custom) unsafe URL scanning is enabled or not. This setting may be controlled by an enterprise policy: https://chromeenterprise.google/policies/#EnterpriseRealTimeUrlCheckMode
         */
        realtimeUrlCheckMode?: string | null;
        /**
         * Output only. Safe Browsing Protection Level. That setting may be controlled by an enterprise policy: https://chromeenterprise.google/policies/#SafeBrowsingProtectionLevel.
         */
        safeBrowsingProtectionLevel?: string | null;
        /**
         * Output only. The state of the Screen Lock password protection. On ChromeOS, this value will always be ENABLED as there is not way to disable requiring a password or pin when unlocking the device.
         */
        screenLockSecured?: string | null;
        /**
         * Output only. Whether the device's startup software has its Secure Boot feature enabled. Available on Windows only.
         */
        secureBootMode?: string | null;
        /**
         * Output only. The serial number of the device. On Windows, this represents the BIOS's serial number. Not available on most Linux distributions.
         */
        serialNumber?: string | null;
        /**
         * Output only. Whether the Site Isolation (a.k.a Site Per Process) setting is enabled. That setting may be controlled by an enterprise policy: https://chromeenterprise.google/policies/#SitePerProcess
         */
        siteIsolationEnabled?: boolean | null;
        /**
         * List of the addesses of all OS level DNS servers configured in the device's network settings.
         */
        systemDnsServers?: string[] | null;
        /**
         * Output only. Deprecated. The corresponding policy is now deprecated. Whether Chrome is blocking third-party software injection or not. This setting may be controlled by an enterprise policy: https://chromeenterprise.google/policies/?policy=ThirdPartyBlockingEnabled. Available on Windows only.
         */
        thirdPartyBlockingEnabled?: boolean | null;
        /**
         * Output only. The trigger which generated this set of signals.
         */
        trigger?: string | null;
        /**
         * Output only. Windows domain that the current machine has joined. Available on Windows only.
         */
        windowsMachineDomain?: string | null;
        /**
         * Output only. Windows domain for the current OS user. Available on Windows only.
         */
        windowsUserDomain?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Signed ChallengeResponse.
     */
    export interface Schema$VerifyChallengeResponseRequest {
        /**
         * Required. The generated response to the challenge, the bytes representation of SignedData.
         */
        challengeResponse?: string | null;
        /**
         * Optional. Service can optionally provide identity information about the device or user associated with the key. For an EMK, this value is the enrolled domain. For an EUK, this value is the user's email address. If present, this value will be checked against contents of the response, and verification will fail if there is no match.
         */
        expectedIdentity?: string | null;
    }
    /**
     * Result message for VerifiedAccess.VerifyChallengeResponse. The response returned when successful for Managed profiles on Unmanaged browsers will NOT have devicePermanentId, keyTrustLevel, virtualDeviceId and customerId fields. Managed profiles will INSTEAD have the profileCustomerId, virtualProfileId, profilePermanentId and profileKeyTrustLevel fields.
     */
    export interface Schema$VerifyChallengeResponseResult {
        /**
         * Output only. Attested device ID (ADID).
         */
        attestedDeviceId?: string | null;
        /**
         * Output only. Unique customer id that this device belongs to, as defined by the Google Admin SDK at https://developers.google.com/admin-sdk/directory/v1/guides/manage-customers
         */
        customerId?: string | null;
        /**
         * Output only. Device enrollment id for ChromeOS devices.
         */
        deviceEnrollmentId?: string | null;
        /**
         * Output only. Device permanent id is returned in this field (for the machine response only).
         */
        devicePermanentId?: string | null;
        /**
         * Output only. Deprecated. Device signal in json string representation. Prefer using `device_signals` instead.
         */
        deviceSignal?: string | null;
        /**
         * Output only. Device signals.
         */
        deviceSignals?: Schema$DeviceSignals;
        /**
         * Output only. Device attested key trust level.
         */
        keyTrustLevel?: string | null;
        /**
         * Output only. Unique customer id that this profile belongs to, as defined by the Google Admin SDK at https://developers.google.com/admin-sdk/directory/v1/guides/manage-customers
         */
        profileCustomerId?: string | null;
        /**
         * Output only. Profile attested key trust level.
         */
        profileKeyTrustLevel?: string | null;
        /**
         * Output only. The unique server-side ID of a profile on the device.
         */
        profilePermanentId?: string | null;
        /**
         * Output only. Certificate Signing Request (in the SPKAC format, base64 encoded) is returned in this field. This field will be set only if device has included CSR in its challenge response. (the option to include CSR is now available for both user and machine responses)
         */
        signedPublicKeyAndChallenge?: string | null;
        /**
         * Output only. Virtual device id of the device. The definition of virtual device id is platform-specific.
         */
        virtualDeviceId?: string | null;
        /**
         * Output only. The client-provided ID of a profile on the device.
         */
        virtualProfileId?: string | null;
    }
    export class Resource$Challenge {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Generates a new challenge.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        generate(params: Params$Resource$Challenge$Generate, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        generate(params?: Params$Resource$Challenge$Generate, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Challenge>>;
        generate(params: Params$Resource$Challenge$Generate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        generate(params: Params$Resource$Challenge$Generate, options: MethodOptions | BodyResponseCallback<Schema$Challenge>, callback: BodyResponseCallback<Schema$Challenge>): void;
        generate(params: Params$Resource$Challenge$Generate, callback: BodyResponseCallback<Schema$Challenge>): void;
        generate(callback: BodyResponseCallback<Schema$Challenge>): void;
        /**
         * Verifies the challenge response.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        verify(params: Params$Resource$Challenge$Verify, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        verify(params?: Params$Resource$Challenge$Verify, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$VerifyChallengeResponseResult>>;
        verify(params: Params$Resource$Challenge$Verify, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        verify(params: Params$Resource$Challenge$Verify, options: MethodOptions | BodyResponseCallback<Schema$VerifyChallengeResponseResult>, callback: BodyResponseCallback<Schema$VerifyChallengeResponseResult>): void;
        verify(params: Params$Resource$Challenge$Verify, callback: BodyResponseCallback<Schema$VerifyChallengeResponseResult>): void;
        verify(callback: BodyResponseCallback<Schema$VerifyChallengeResponseResult>): void;
    }
    export interface Params$Resource$Challenge$Generate extends StandardParameters {
        /**
         * Request body metadata
         */
        requestBody?: Schema$Empty;
    }
    export interface Params$Resource$Challenge$Verify extends StandardParameters {
        /**
         * Request body metadata
         */
        requestBody?: Schema$VerifyChallengeResponseRequest;
    }
    export {};
}
