{"version": 3, "file": "extraerrordata.js", "sources": ["../../../src/integrations/extraerrordata.ts"], "sourcesContent": ["import { DEBUG_BUILD } from '../debug-build';\nimport { defineIntegration } from '../integration';\nimport type { Contexts } from '../types-hoist/context';\nimport type { ExtendedError } from '../types-hoist/error';\nimport type { Event, EventHint } from '../types-hoist/event';\nimport type { IntegrationFn } from '../types-hoist/integration';\nimport { isError, isPlainObject } from '../utils/is';\nimport { logger } from '../utils/logger';\nimport { normalize } from '../utils/normalize';\nimport { addNonEnumerableProperty } from '../utils/object';\nimport { truncate } from '../utils/string';\n\nconst INTEGRATION_NAME = 'ExtraErrorData';\n\ninterface ExtraErrorDataOptions {\n  /**\n   * The object depth up to which to capture data on error objects.\n   */\n  depth: number;\n\n  /**\n   * Whether to capture error causes. Defaults to true.\n   *\n   * More information: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error/cause\n   */\n  captureErrorCause: boolean;\n}\n\n/**\n * Extract additional data for from original exceptions.\n */\nconst _extraErrorDataIntegration = ((options: Partial<ExtraErrorDataOptions> = {}) => {\n  const { depth = 3, captureErrorCause = true } = options;\n  return {\n    name: INTEGRATION_NAME,\n    processEvent(event, hint, client) {\n      const { maxValueLength = 250 } = client.getOptions();\n      return _enhanceEventWithErrorData(event, hint, depth, captureErrorCause, maxValueLength);\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const extraErrorDataIntegration = defineIntegration(_extraErrorDataIntegration);\n\nfunction _enhanceEventWithErrorData(\n  event: Event,\n  hint: EventHint = {},\n  depth: number,\n  captureErrorCause: boolean,\n  maxValueLength: number,\n): Event {\n  if (!hint.originalException || !isError(hint.originalException)) {\n    return event;\n  }\n  const exceptionName = (hint.originalException as ExtendedError).name || hint.originalException.constructor.name;\n\n  const errorData = _extractErrorData(hint.originalException as ExtendedError, captureErrorCause, maxValueLength);\n\n  if (errorData) {\n    const contexts: Contexts = {\n      ...event.contexts,\n    };\n\n    const normalizedErrorData = normalize(errorData, depth);\n\n    if (isPlainObject(normalizedErrorData)) {\n      // We mark the error data as \"already normalized\" here, because we don't want other normalization procedures to\n      // potentially truncate the data we just already normalized, with a certain depth setting.\n      addNonEnumerableProperty(normalizedErrorData, '__sentry_skip_normalization__', true);\n      contexts[exceptionName] = normalizedErrorData;\n    }\n\n    return {\n      ...event,\n      contexts,\n    };\n  }\n\n  return event;\n}\n\n/**\n * Extract extra information from the Error object\n */\nfunction _extractErrorData(\n  error: ExtendedError,\n  captureErrorCause: boolean,\n  maxValueLength: number,\n): Record<string, unknown> | null {\n  // We are trying to enhance already existing event, so no harm done if it won't succeed\n  try {\n    const nativeKeys = [\n      'name',\n      'message',\n      'stack',\n      'line',\n      'column',\n      'fileName',\n      'lineNumber',\n      'columnNumber',\n      'toJSON',\n    ];\n\n    const extraErrorInfo: Record<string, unknown> = {};\n\n    // We want only enumerable properties, thus `getOwnPropertyNames` is redundant here, as we filter keys anyway.\n    for (const key of Object.keys(error)) {\n      if (nativeKeys.indexOf(key) !== -1) {\n        continue;\n      }\n      const value = error[key];\n      extraErrorInfo[key] = isError(value) || typeof value === 'string' ? truncate(`${value}`, maxValueLength) : value;\n    }\n\n    // Error.cause is a standard property that is non enumerable, we therefore need to access it separately.\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error/cause\n    if (captureErrorCause && error.cause !== undefined) {\n      extraErrorInfo.cause = isError(error.cause) ? error.cause.toString() : error.cause;\n    }\n\n    // Check if someone attached `toJSON` method to grab even more properties (eg. axios is doing that)\n    if (typeof error.toJSON === 'function') {\n      const serializedError = error.toJSON() as Record<string, unknown>;\n\n      for (const key of Object.keys(serializedError)) {\n        const value = serializedError[key];\n        extraErrorInfo[key] = isError(value) ? value.toString() : value;\n      }\n    }\n\n    return extraErrorInfo;\n  } catch (oO) {\n    DEBUG_BUILD && logger.error('Unable to extract extra data from the Error object:', oO);\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;;;;;;AAYA,MAAM,gBAAA,GAAmB,gBAAgB;;AAgBzC;AACA;AACA;AACA,MAAM,0BAAA,IAA8B,CAAC,OAAO,GAAmC,EAAE,KAAK;AACtF,EAAE,MAAM,EAAE,KAAA,GAAQ,CAAC,EAAE,iBAAA,GAAoB,IAAA,EAAO,GAAE,OAAO;AACzD,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE;AACtC,MAAM,MAAM,EAAE,cAAA,GAAiB,GAAA,EAAM,GAAE,MAAM,CAAC,UAAU,EAAE;AAC1D,MAAM,OAAO,0BAA0B,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,iBAAiB,EAAE,cAAc,CAAC;AAC9F,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;MAEU,yBAA0B,GAAE,iBAAiB,CAAC,0BAA0B;;AAErF,SAAS,0BAA0B;AACnC,EAAE,KAAK;AACP,EAAE,IAAI,GAAc,EAAE;AACtB,EAAE,KAAK;AACP,EAAE,iBAAiB;AACnB,EAAE,cAAc;AAChB,EAAS;AACT,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAkB,IAAG,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE;AACnE,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,MAAM,aAAc,GAAE,CAAC,IAAI,CAAC,iBAAkB,GAAkB,IAAA,IAAQ,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI;;AAEjH,EAAE,MAAM,SAAA,GAAY,iBAAiB,CAAC,IAAI,CAAC,iBAAA,GAAoC,iBAAiB,EAAE,cAAc,CAAC;;AAEjH,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,MAAM,QAAQ,GAAa;AAC/B,MAAM,GAAG,KAAK,CAAC,QAAQ;AACvB,KAAK;;AAEL,IAAI,MAAM,sBAAsB,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC;;AAE3D,IAAI,IAAI,aAAa,CAAC,mBAAmB,CAAC,EAAE;AAC5C;AACA;AACA,MAAM,wBAAwB,CAAC,mBAAmB,EAAE,+BAA+B,EAAE,IAAI,CAAC;AAC1F,MAAM,QAAQ,CAAC,aAAa,CAAA,GAAI,mBAAmB;AACnD;;AAEA,IAAI,OAAO;AACX,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL;;AAEA,EAAE,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA,SAAS,iBAAiB;AAC1B,EAAE,KAAK;AACP,EAAE,iBAAiB;AACnB,EAAE,cAAc;AAChB,EAAkC;AAClC;AACA,EAAE,IAAI;AACN,IAAI,MAAM,aAAa;AACvB,MAAM,MAAM;AACZ,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,MAAM,UAAU;AAChB,MAAM,YAAY;AAClB,MAAM,cAAc;AACpB,MAAM,QAAQ;AACd,KAAK;;AAEL,IAAI,MAAM,cAAc,GAA4B,EAAE;;AAEtD;AACA,IAAI,KAAK,MAAM,GAAI,IAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC1C,MAAM,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,CAAE,KAAI,CAAC,CAAC,EAAE;AAC1C,QAAQ;AACR;AACA,MAAM,MAAM,KAAM,GAAE,KAAK,CAAC,GAAG,CAAC;AAC9B,MAAM,cAAc,CAAC,GAAG,IAAI,OAAO,CAAC,KAAK,CAAA,IAAK,OAAO,UAAU,QAAA,GAAW,QAAQ,CAAC,CAAC,EAAA,KAAA,CAAA,CAAA,EAAA,cAAA,CAAA,GAAA,KAAA;AACA;;AAEA;AACA;AACA,IAAA,IAAA,iBAAA,IAAA,KAAA,CAAA,KAAA,KAAA,SAAA,EAAA;AACA,MAAA,cAAA,CAAA,KAAA,GAAA,OAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,KAAA,CAAA,QAAA,EAAA,GAAA,KAAA,CAAA,KAAA;AACA;;AAEA;AACA,IAAA,IAAA,OAAA,KAAA,CAAA,MAAA,KAAA,UAAA,EAAA;AACA,MAAA,MAAA,eAAA,GAAA,KAAA,CAAA,MAAA,EAAA;;AAEA,MAAA,KAAA,MAAA,GAAA,IAAA,MAAA,CAAA,IAAA,CAAA,eAAA,CAAA,EAAA;AACA,QAAA,MAAA,KAAA,GAAA,eAAA,CAAA,GAAA,CAAA;AACA,QAAA,cAAA,CAAA,GAAA,CAAA,GAAA,OAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,QAAA,EAAA,GAAA,KAAA;AACA;AACA;;AAEA,IAAA,OAAA,cAAA;AACA,GAAA,CAAA,OAAA,EAAA,EAAA;AACA,IAAA,WAAA,IAAA,MAAA,CAAA,KAAA,CAAA,qDAAA,EAAA,EAAA,CAAA;AACA;;AAEA,EAAA,OAAA,IAAA;AACA;;;;"}