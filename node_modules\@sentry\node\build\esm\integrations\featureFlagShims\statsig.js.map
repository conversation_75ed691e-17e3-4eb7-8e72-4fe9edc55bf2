{"version": 3, "file": "statsig.js", "sources": ["../../../../src/integrations/featureFlagShims/statsig.ts"], "sourcesContent": ["import { consoleSandbox, defineIntegration, isBrowser } from '@sentry/core';\n\n/**\n * This is a shim for the Statsig integration.\n * We need this in order to not throw runtime errors when accidentally importing this on the server through a meta framework like Next.js.\n */\nexport const statsigIntegrationShim = defineIntegration((_options?: unknown) => {\n  if (!isBrowser()) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn('The statsigIntegration() can only be used in the browser.');\n    });\n  }\n\n  return {\n    name: 'Statsig',\n  };\n});\n"], "names": [], "mappings": ";;AAEA;AACA;AACA;AACA;AACO,MAAM,yBAAyB,iBAAiB,CAAC,CAAC,QAAQ,KAAe;AAChF,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE;AACpB,IAAI,cAAc,CAAC,MAAM;AACzB;AACA,MAAM,OAAO,CAAC,IAAI,CAAC,2DAA2D,CAAC;AAC/E,KAAK,CAAC;AACN;;AAEA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,SAAS;AACnB,GAAG;AACH,CAAC;;;;"}