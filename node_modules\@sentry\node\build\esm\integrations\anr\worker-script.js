/*! @sentry/node 9.35.0 (8d508ef) | https://github.com/getsentry/sentry-javascript */
import{Session as t}from"node:inspector";import{workerData as n,parentPort as e}from"node:worker_threads";import{posix as r,sep as o}from"node:path";import*as s from"node:http";import*as i from"node:https";import{Readable as c}from"node:stream";import{createGzip as u}from"node:zlib";import*as a from"node:net";import*as f from"node:tls";const h="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,p=globalThis,l="9.35.0";function d(){return m(p),p}function m(t){const n=t.__SENTRY__=t.__SENTRY__||{};return n.version=n.version||l,n[l]=n[l]||{}}function g(t,n,e=p){const r=e.__SENTRY__=e.__SENTRY__||{},o=r[l]=r[l]||{};return o[t]||(o[t]=n())}const y=["debug","info","warn","error","log","assert","trace"],b={};function v(t){if(!("console"in p))return t();const n=p.console,e={},r=Object.keys(b);r.forEach((t=>{const r=b[t];e[t]=n[t],n[t]=r}));try{return t()}finally{r.forEach((t=>{n[t]=e[t]}))}}const _=g("logger",(function(){let t=!1;const n={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return h?y.forEach((e=>{n[e]=(...n)=>{t&&v((()=>{p.console[e](`Sentry Logger [${e}]:`,...n)}))}})):y.forEach((t=>{n[t]=()=>{}})),n})),w=50,S="?",$=/captureMessage|captureException/;function E(t){return t[t.length-1]||{}}const x="<anonymous>";const N=Object.prototype.toString;function C(t,n){return N.call(t)===`[object ${n}]`}function T(t){return C(t,"String")}function k(t){return C(t,"Object")}function j(t){return Boolean(t?.then&&"function"==typeof t.then)}function R(t,n){try{return t instanceof n}catch(t){return!1}}const I=p,O=80;function D(t,n){const e=t,r=[];if(!e?.tagName)return"";if(I.HTMLElement&&e instanceof HTMLElement&&e.dataset){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}r.push(e.tagName.toLowerCase());const o=n?.length?n.filter((t=>e.getAttribute(t))).map((t=>[t,e.getAttribute(t)])):null;if(o?.length)o.forEach((t=>{r.push(`[${t[0]}="${t[1]}"]`)}));else{e.id&&r.push(`#${e.id}`);const t=e.className;if(t&&T(t)){const n=t.split(/\s+/);for(const t of n)r.push(`.${t}`)}}const s=["aria-label","type","name","title","alt"];for(const t of s){const n=e.getAttribute(t);n&&r.push(`[${t}="${n}"]`)}return r.join("")}function A(t,n=0){return"string"!=typeof t||0===n||t.length<=n?t:`${t.slice(0,n)}...`}function P(t){if(function(t){switch(N.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return R(t,Error)}}(t))return{message:t.message,name:t.name,stack:t.stack,...M(t)};if(n=t,"undefined"!=typeof Event&&R(n,Event)){const n={type:t.type,target:U(t.target),currentTarget:U(t.currentTarget),...M(t)};return"undefined"!=typeof CustomEvent&&R(t,CustomEvent)&&(n.detail=t.detail),n}return t;var n}function U(t){try{return n=t,"undefined"!=typeof Element&&R(n,Element)?function(t,n={}){if(!t)return"<unknown>";try{let e=t;const r=5,o=[];let s=0,i=0;const c=" > ",u=c.length;let a;const f=Array.isArray(n)?n:n.keyAttrs,h=!Array.isArray(n)&&n.maxStringLength||O;for(;e&&s++<r&&(a=D(e,f),!("html"===a||s>1&&i+o.length*u+a.length>=h));)o.push(a),i+=a.length,e=e.parentNode;return o.reverse().join(c)}catch(t){return"<unknown>"}}(t):Object.prototype.toString.call(t)}catch(t){return"<unknown>"}var n}function M(t){if("object"==typeof t&&null!==t){const n={};for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(n[e]=t[e]);return n}return{}}function L(t=function(){const t=p;return t.crypto||t.msCrypto}()){let n=()=>16*Math.random();try{if(t?.randomUUID)return t.randomUUID().replace(/-/g,"");t?.getRandomValues&&(n=()=>{const n=new Uint8Array(1);return t.getRandomValues(n),n[0]})}catch(t){}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(t=>(t^(15&n())>>t/4).toString(16)))}const B=1e3;function G(){return Date.now()/B}const J=function(){const{performance:t}=p;if(!t?.now)return G;const n=Date.now()-t.now(),e=null==t.timeOrigin?n:t.timeOrigin;return()=>(e+t.now())/B}();function z(t){const n=J(),e={sid:L(),init:!0,timestamp:n,started:n,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(t){return{sid:`${t.sid}`,init:t.init,started:new Date(1e3*t.started).toISOString(),timestamp:new Date(1e3*t.timestamp).toISOString(),status:t.status,errors:t.errors,did:"number"==typeof t.did||"string"==typeof t.did?`${t.did}`:void 0,duration:t.duration,abnormal_mechanism:t.abnormal_mechanism,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}}}(e)};return t&&H(e,t),e}function H(t,n={}){if(n.user&&(!t.ipAddress&&n.user.ip_address&&(t.ipAddress=n.user.ip_address),t.did||n.did||(t.did=n.user.id||n.user.email||n.user.username)),t.timestamp=n.timestamp||J(),n.abnormal_mechanism&&(t.abnormal_mechanism=n.abnormal_mechanism),n.ignoreDuration&&(t.ignoreDuration=n.ignoreDuration),n.sid&&(t.sid=32===n.sid.length?n.sid:L()),void 0!==n.init&&(t.init=n.init),!t.did&&n.did&&(t.did=`${n.did}`),"number"==typeof n.started&&(t.started=n.started),t.ignoreDuration)t.duration=void 0;else if("number"==typeof n.duration)t.duration=n.duration;else{const n=t.timestamp-t.started;t.duration=n>=0?n:0}n.release&&(t.release=n.release),n.environment&&(t.environment=n.environment),!t.ipAddress&&n.ipAddress&&(t.ipAddress=n.ipAddress),!t.userAgent&&n.userAgent&&(t.userAgent=n.userAgent),"number"==typeof n.errors&&(t.errors=n.errors),n.status&&(t.status=n.status)}function F(t,n,e=2){if(!n||"object"!=typeof n||e<=0)return n;if(t&&0===Object.keys(n).length)return t;const r={...t};for(const t in n)Object.prototype.hasOwnProperty.call(n,t)&&(r[t]=F(r[t],n[t],e-1));return r}function W(){return L()}function Y(){return L().substring(16)}const K="_sentrySpan";function V(t,n){n?function(t,n,e){try{Object.defineProperty(t,n,{value:e,writable:!0,configurable:!0})}catch(e){h&&_.log(`Failed to add non-enumerable property "${n}" to object`,t)}}(t,K,n):delete t[K]}function Z(t){return t[K]}class q{constructor(){this.t=!1,this.o=[],this.i=[],this.u=[],this.h=[],this.p={},this.l={},this.m={},this.v={},this._={},this.S={traceId:W(),sampleRand:Math.random()}}clone(){const t=new q;return t.u=[...this.u],t.l={...this.l},t.m={...this.m},t.v={...this.v},this.v.flags&&(t.v.flags={values:[...this.v.flags.values]}),t.p=this.p,t.N=this.N,t.C=this.C,t.T=this.T,t.k=this.k,t.i=[...this.i],t.h=[...this.h],t._={...this._},t.S={...this.S},t.j=this.j,t.R=this.R,V(t,Z(this)),t}setClient(t){this.j=t}setLastEventId(t){this.R=t}getClient(){return this.j}lastEventId(){return this.R}addScopeListener(t){this.o.push(t)}addEventProcessor(t){return this.i.push(t),this}setUser(t){return this.p=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this.C&&H(this.C,{user:t}),this.I(),this}getUser(){return this.p}setTags(t){return this.l={...this.l,...t},this.I(),this}setTag(t,n){return this.l={...this.l,[t]:n},this.I(),this}setExtras(t){return this.m={...this.m,...t},this.I(),this}setExtra(t,n){return this.m={...this.m,[t]:n},this.I(),this}setFingerprint(t){return this.k=t,this.I(),this}setLevel(t){return this.N=t,this.I(),this}setTransactionName(t){return this.T=t,this.I(),this}setContext(t,n){return null===n?delete this.v[t]:this.v[t]=n,this.I(),this}setSession(t){return t?this.C=t:delete this.C,this.I(),this}getSession(){return this.C}update(t){if(!t)return this;const n="function"==typeof t?t(this):t,e=n instanceof q?n.getScopeData():k(n)?t:void 0,{tags:r,extra:o,user:s,contexts:i,level:c,fingerprint:u=[],propagationContext:a}=e||{};return this.l={...this.l,...r},this.m={...this.m,...o},this.v={...this.v,...i},s&&Object.keys(s).length&&(this.p=s),c&&(this.N=c),u.length&&(this.k=u),a&&(this.S=a),this}clear(){return this.u=[],this.l={},this.m={},this.p={},this.v={},this.N=void 0,this.T=void 0,this.k=void 0,this.C=void 0,V(this,void 0),this.h=[],this.setPropagationContext({traceId:W(),sampleRand:Math.random()}),this.I(),this}addBreadcrumb(t,n){const e="number"==typeof n?n:100;if(e<=0)return this;const r={timestamp:G(),...t,message:t.message?A(t.message,2048):t.message};return this.u.push(r),this.u.length>e&&(this.u=this.u.slice(-e),this.j?.recordDroppedEvent("buffer_overflow","log_item")),this.I(),this}getLastBreadcrumb(){return this.u[this.u.length-1]}clearBreadcrumbs(){return this.u=[],this.I(),this}addAttachment(t){return this.h.push(t),this}clearAttachments(){return this.h=[],this}getScopeData(){return{breadcrumbs:this.u,attachments:this.h,contexts:this.v,tags:this.l,extra:this.m,user:this.p,level:this.N,fingerprint:this.k||[],eventProcessors:this.i,propagationContext:this.S,sdkProcessingMetadata:this._,transactionName:this.T,span:Z(this)}}setSDKProcessingMetadata(t){return this._=F(this._,t,2),this}setPropagationContext(t){return this.S=t,this}getPropagationContext(){return this.S}captureException(t,n){const e=n?.event_id||L();if(!this.j)return _.warn("No client configured on scope - will not capture exception!"),e;const r=new Error("Sentry syntheticException");return this.j.captureException(t,{originalException:t,syntheticException:r,...n,event_id:e},this),e}captureMessage(t,n,e){const r=e?.event_id||L();if(!this.j)return _.warn("No client configured on scope - will not capture message!"),r;const o=new Error(t);return this.j.captureMessage(t,n,{originalException:t,syntheticException:o,...e,event_id:r},this),r}captureEvent(t,n){const e=n?.event_id||L();return this.j?(this.j.captureEvent(t,{...n,event_id:e},this),e):(_.warn("No client configured on scope - will not capture event!"),e)}I(){this.t||(this.t=!0,this.o.forEach((t=>{t(this)})),this.t=!1)}}class Q{constructor(t,n){let e,r;e=t||new q,r=n||new q,this.O=[{scope:e}],this.D=r}withScope(t){const n=this.A();let e;try{e=t(n)}catch(t){throw this.P(),t}return j(e)?e.then((t=>(this.P(),t)),(t=>{throw this.P(),t})):(this.P(),e)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this.D}getStackTop(){return this.O[this.O.length-1]}A(){const t=this.getScope().clone();return this.O.push({client:this.getClient(),scope:t}),t}P(){return!(this.O.length<=1)&&!!this.O.pop()}}function X(){const t=m(d());return t.stack=t.stack||new Q(g("defaultCurrentScope",(()=>new q)),g("defaultIsolationScope",(()=>new q)))}function tt(t){return X().withScope(t)}function nt(t,n){const e=X();return e.withScope((()=>(e.getStackTop().scope=t,n(t))))}function et(t){return X().withScope((()=>t(X().getIsolationScope())))}function rt(t){const n=m(t);return n.acs?n.acs:{withIsolationScope:et,withScope:tt,withSetScope:nt,withSetIsolationScope:(t,n)=>et(n),getCurrentScope:()=>X().getScope(),getIsolationScope:()=>X().getIsolationScope()}}function ot(){return rt(d()).getCurrentScope().getClient()}const st="sentry.source",it="sentry.sample_rate",ct="sentry.previous_trace_sample_rate",ut="sentry.op",at="sentry.origin",ft=0,ht=1,pt="_sentryScope",lt="_sentryIsolationScope";function dt(t){return{scope:t[pt],isolationScope:t[lt]}}const mt="sentry-",gt=/^sentry-/;function yt(t){const n=function(t){if(!t||!T(t)&&!Array.isArray(t))return;if(Array.isArray(t))return t.reduce(((t,n)=>{const e=bt(n);return Object.entries(e).forEach((([n,e])=>{t[n]=e})),t}),{});return bt(t)}(t);if(!n)return;const e=Object.entries(n).reduce(((t,[n,e])=>{if(n.match(gt)){t[n.slice(mt.length)]=e}return t}),{});return Object.keys(e).length>0?e:void 0}function bt(t){return t.split(",").map((t=>t.split("=").map((t=>{try{return decodeURIComponent(t.trim())}catch{return}})))).reduce(((t,[n,e])=>(n&&e&&(t[n]=e),t)),{})}const vt=1;function _t(t){const{spanId:n,traceId:e,isRemote:r}=t.spanContext(),o=r?n:Et(t).parent_span_id,s=dt(t).scope;return{parent_span_id:o,span_id:r?s?.getPropagationContext().propagationSpanId||Y():n,trace_id:e}}function wt(t){return t&&t.length>0?t.map((({context:{spanId:t,traceId:n,traceFlags:e,...r},attributes:o})=>({span_id:t,trace_id:n,sampled:e===vt,attributes:o,...r}))):void 0}function St(t){return"number"==typeof t?$t(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?$t(t.getTime()):J()}function $t(t){return t>9999999999?t/1e3:t}function Et(t){if(function(t){return"function"==typeof t.getSpanJSON}(t))return t.getSpanJSON();const{spanId:n,traceId:e}=t.spanContext();if(function(t){const n=t;return!!(n.attributes&&n.startTime&&n.name&&n.endTime&&n.status)}(t)){const{attributes:r,startTime:o,name:s,endTime:i,status:c,links:u}=t;return{span_id:n,trace_id:e,data:r,description:s,parent_span_id:"parentSpanId"in t?t.parentSpanId:"parentSpanContext"in t?t.parentSpanContext?.spanId:void 0,start_timestamp:St(o),timestamp:St(i)||void 0,status:xt(c),op:r[ut],origin:r[at],links:wt(u)}}return{span_id:n,trace_id:e,start_timestamp:0,data:{}}}function xt(t){if(t&&t.code!==ft)return t.code===ht?"ok":t.message||"unknown_error"}const Nt="_sentryRootSpan";function Ct(t){return t[Nt]||t}const Tt="production",kt=/^o(\d+)\./;function jt(t,n=!1){const{host:e,path:r,pass:o,port:s,projectId:i,protocol:c,publicKey:u}=t;return`${c}://${u}${n&&o?`:${o}`:""}@${e}${s?`:${s}`:""}/${r?`${r}/`:r}${i}`}const Rt="_frozenDsc";function It(t,n){const e=n.getOptions(),{publicKey:r,host:o}=n.getDsn()||{};let s;e.orgId?s=String(e.orgId):o&&(s=function(t){const n=t.match(kt);return n?.[1]}(o));const i={environment:e.environment||Tt,release:e.release,public_key:r,trace_id:t,org_id:s};return n.emit("createDsc",i),i}function Ot(t){const n=ot();if(!n)return{};const e=Ct(t),r=Et(e),o=r.data,s=e.spanContext().traceState,i=s?.get("sentry.sample_rate")??o[it]??o[ct];function c(t){return"number"!=typeof i&&"string"!=typeof i||(t.sample_rate=`${i}`),t}const u=e[Rt];if(u)return c(u);const a=s?.get("sentry.dsc"),f=a&&yt(a);if(f)return c(f);const h=It(t.spanContext().traceId,n),p=o[st],l=r.description;return"url"!==p&&l&&(h.transaction=l),function(t){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;const n=t||ot()?.getOptions();return!(!n||null==n.tracesSampleRate&&!n.tracesSampler)}()&&(h.sampled=String(function(t){const{traceFlags:n}=t.spanContext();return n===vt}(e)),h.sample_rand=s?.get("sentry.sample_rand")??dt(e).scope?.getPropagationContext().sampleRand.toString()),c(h),n.emit("createDsc",h,e),h}function Dt(t,n=100,e=1/0){try{return At("",t,n,e)}catch(t){return{ERROR:`**non-serializable** (${t})`}}}function At(t,n,e=1/0,r=1/0,o=function(){const t=new WeakSet;function n(n){return!!t.has(n)||(t.add(n),!1)}function e(n){t.delete(n)}return[n,e]}()){const[s,i]=o;if(null==n||["boolean","string"].includes(typeof n)||"number"==typeof n&&Number.isFinite(n))return n;const c=function(t,n){try{if("domain"===t&&n&&"object"==typeof n&&n.U)return"[Domain]";if("domainEmitter"===t)return"[DomainEmitter]";if("undefined"!=typeof global&&n===global)return"[Global]";if("undefined"!=typeof window&&n===window)return"[Window]";if("undefined"!=typeof document&&n===document)return"[Document]";if("object"==typeof(e=n)&&null!==e&&(e.__isVue||e.M))return"[VueViewModel]";if(function(t){return k(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t}(n))return"[SyntheticEvent]";if("number"==typeof n&&!Number.isFinite(n))return`[${n}]`;if("function"==typeof n)return`[Function: ${function(t){try{return t&&"function"==typeof t&&t.name||x}catch(t){return x}}(n)}]`;if("symbol"==typeof n)return`[${String(n)}]`;if("bigint"==typeof n)return`[BigInt: ${String(n)}]`;const r=function(t){const n=Object.getPrototypeOf(t);return n?.constructor?n.constructor.name:"null prototype"}(n);return/^HTML(\w*)Element$/.test(r)?`[HTMLElement: ${r}]`:`[object ${r}]`}catch(t){return`**non-serializable** (${t})`}var e}(t,n);if(!c.startsWith("[object "))return c;if(n.__sentry_skip_normalization__)return n;const u="number"==typeof n.__sentry_override_normalization_depth__?n.__sentry_override_normalization_depth__:e;if(0===u)return c.replace("object ","");if(s(n))return"[Circular ~]";const a=n;if(a&&"function"==typeof a.toJSON)try{return At("",a.toJSON(),u-1,r,o)}catch(t){}const f=Array.isArray(n)?[]:{};let h=0;const p=P(n);for(const t in p){if(!Object.prototype.hasOwnProperty.call(p,t))continue;if(h>=r){f[t]="[MaxProperties ~]";break}const n=p[t];f[t]=At(t,n,u-1,r,o),h++}return i(n),f}function Pt(t,n){const e=n.replace(/\\/g,"/").replace(/[|\\{}()[\]^$+*?.]/g,"\\$&");let r=t;try{r=decodeURI(t)}catch(t){}return r.replace(/\\/g,"/").replace(/webpack:\/?/g,"").replace(new RegExp(`(file://)?/*${e}/*`,"ig"),"app:///")}function Ut(t,n=[]){return[t,n]}function Mt(t,n){const e=t[1];for(const t of e){if(n(t,t[0].type))return!0}return!1}function Lt(t){const n=m(p);return n.encodePolyfill?n.encodePolyfill(t):(new TextEncoder).encode(t)}function Bt(t){const[n,e]=t;let r=JSON.stringify(n);function o(t){"string"==typeof r?r="string"==typeof t?r+t:[Lt(r),t]:r.push("string"==typeof t?Lt(t):t)}for(const t of e){const[n,e]=t;if(o(`\n${JSON.stringify(n)}\n`),"string"==typeof e||e instanceof Uint8Array)o(e);else{let t;try{t=JSON.stringify(e)}catch(n){t=JSON.stringify(Dt(e))}o(t)}}return"string"==typeof r?r:function(t){const n=t.reduce(((t,n)=>t+n.length),0),e=new Uint8Array(n);let r=0;for(const n of t)e.set(n,r),r+=n.length;return e}(r)}const Gt={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function Jt(t){if(!t?.sdk)return;const{name:n,version:e}=t.sdk;return{name:n,version:e}}function zt(t,n,e,r){const o=Jt(e),s=t.type&&"replay_event"!==t.type?t.type:"event";!function(t,n){n&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||n.name,t.sdk.version=t.sdk.version||n.version,t.sdk.integrations=[...t.sdk.integrations||[],...n.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...n.packages||[]])}(t,e?.sdk);const i=function(t,n,e,r){const o=t.sdkProcessingMetadata?.dynamicSamplingContext;return{event_id:t.event_id,sent_at:(new Date).toISOString(),...n&&{sdk:n},...!!e&&r&&{dsn:jt(r)},...o&&{trace:o}}}(t,o,r,n);delete t.sdkProcessingMetadata;return Ut(i,[[{type:s},t]])}const Ht="__SENTRY_SUPPRESS_TRACING__";function Ft(t){const n=rt(d());return n.suppressTracing?n.suppressTracing(t):function(...t){const n=rt(d());if(2===t.length){const[e,r]=t;return e?n.withSetScope(e,r):n.withScope(r)}return n.withScope(t[0])}((n=>{n.setSDKProcessingMetadata({[Ht]:!0});const e=t();return n.setSDKProcessingMetadata({[Ht]:void 0}),e}))}var Wt;function Yt(t){return new Kt((n=>{n(t)}))}!function(t){t[t.PENDING=0]="PENDING";t[t.RESOLVED=1]="RESOLVED";t[t.REJECTED=2]="REJECTED"}(Wt||(Wt={}));class Kt{constructor(t){this.L=Wt.PENDING,this.B=[],this.G(t)}then(t,n){return new Kt(((e,r)=>{this.B.push([!1,n=>{if(t)try{e(t(n))}catch(t){r(t)}else e(n)},t=>{if(n)try{e(n(t))}catch(t){r(t)}else r(t)}]),this.J()}))}catch(t){return this.then((t=>t),t)}finally(t){return new Kt(((n,e)=>{let r,o;return this.then((n=>{o=!1,r=n,t&&t()}),(n=>{o=!0,r=n,t&&t()})).then((()=>{o?e(r):n(r)}))}))}J(){if(this.L===Wt.PENDING)return;const t=this.B.slice();this.B=[],t.forEach((t=>{t[0]||(this.L===Wt.RESOLVED&&t[1](this.H),this.L===Wt.REJECTED&&t[2](this.H),t[0]=!0)}))}G(t){const n=(t,n)=>{this.L===Wt.PENDING&&(j(n)?n.then(e,r):(this.L=t,this.H=n,this.J()))},e=t=>{n(Wt.RESOLVED,t)},r=t=>{n(Wt.REJECTED,t)};try{t(e,r)}catch(t){r(t)}}}function Vt(t,n){const{fingerprint:e,span:r,breadcrumbs:o,sdkProcessingMetadata:s}=n;!function(t,n){const{extra:e,tags:r,user:o,contexts:s,level:i,transactionName:c}=n;Object.keys(e).length&&(t.extra={...e,...t.extra});Object.keys(r).length&&(t.tags={...r,...t.tags});Object.keys(o).length&&(t.user={...o,...t.user});Object.keys(s).length&&(t.contexts={...s,...t.contexts});i&&(t.level=i);c&&"transaction"!==t.type&&(t.transaction=c)}(t,n),r&&function(t,n){t.contexts={trace:_t(n),...t.contexts},t.sdkProcessingMetadata={dynamicSamplingContext:Ot(n),...t.sdkProcessingMetadata};const e=Ct(n),r=Et(e).description;r&&!t.transaction&&"transaction"===t.type&&(t.transaction=r)}(t,r),function(t,n){t.fingerprint=t.fingerprint?Array.isArray(t.fingerprint)?t.fingerprint:[t.fingerprint]:[],n&&(t.fingerprint=t.fingerprint.concat(n));t.fingerprint.length||delete t.fingerprint}(t,e),function(t,n){const e=[...t.breadcrumbs||[],...n];t.breadcrumbs=e.length?e:void 0}(t,o),function(t,n){t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...n}}(t,s)}const Zt="7";const qt=Symbol.for("SentryBufferFullError");function Qt(t){const n=[];function e(t){return n.splice(n.indexOf(t),1)[0]||Promise.resolve(void 0)}return{$:n,add:function(r){if(!(void 0===t||n.length<t))return o=qt,new Kt(((t,n)=>{n(o)}));var o;const s=r();return-1===n.indexOf(s)&&n.push(s),s.then((()=>e(s))).then(null,(()=>e(s).then(null,(()=>{})))),s},drain:function(t){return new Kt(((e,r)=>{let o=n.length;if(!o)return e(!0);const s=setTimeout((()=>{t&&t>0&&e(!1)}),t);n.forEach((t=>{Yt(t).then((()=>{--o||(clearTimeout(s),e(!0))}),r)}))}))}}}const Xt=6e4;function tn(t,{statusCode:n,headers:e},r=Date.now()){const o={...t},s=e?.["x-sentry-rate-limits"],i=e?.["retry-after"];if(s)for(const t of s.trim().split(",")){const[n,e,,,s]=t.split(":",5),i=parseInt(n,10),c=1e3*(isNaN(i)?60:i);if(e)for(const t of e.split(";"))"metric_bucket"===t&&s&&!s.split(";").includes("custom")||(o[t]=r+c);else o.all=r+c}else i?o.all=r+function(t,n=Date.now()){const e=parseInt(`${t}`,10);if(!isNaN(e))return 1e3*e;const r=Date.parse(`${t}`);return isNaN(r)?Xt:r-n}(i,r):429===n&&(o.all=r+6e4);return o}const nn=64;function en(t,n,e=Qt(t.bufferSize||nn)){let r={};return{send:function(t){const o=[];if(Mt(t,((t,n)=>{const e=function(t){return Gt[t]}(n);(function(t,n,e=Date.now()){return function(t,n){return t[n]||t.all||0}(t,n)>e})(r,e)||o.push(t)})),0===o.length)return Yt({});const s=Ut(t[0],o),i=t=>{Mt(s,((t,n)=>{}))};return e.add((()=>n({body:Bt(s)}).then((t=>(void 0!==t.statusCode&&(t.statusCode<200||t.statusCode>=300)&&h&&_.warn(`Sentry responded with status code ${t.statusCode} to sent event.`),r=tn(r,t),t)),(t=>{throw i(),h&&_.error("Encountered error running transport request:",t),t})))).then((t=>t),(t=>{if(t===qt)return h&&_.error("Skipped sending event because buffer is full."),i(),Yt({});throw t}))},flush:t=>e.drain(t)}}const rn=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function on(t){const n=function(t){const n=t.length>1024?`<truncated>${t.slice(-1024)}`:t,e=rn.exec(n);return e?e.slice(1):[]}(t),e=n[0]||"";let r=n[1];return e||r?(r&&(r=r.slice(0,r.length-1)),e+r):"."}function sn(t,n=!1){return!(n||t&&!t.startsWith("/")&&!t.match(/^[A-Z]:/)&&!t.startsWith(".")&&!t.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&void 0!==t&&!t.includes("node_modules/")}const cn=Symbol("AgentBaseInternalState");class un extends s.Agent{constructor(t){super(t),this[cn]={}}isSecureEndpoint(t){if(t){if("boolean"==typeof t.secureEndpoint)return t.secureEndpoint;if("string"==typeof t.protocol)return"https:"===t.protocol}const{stack:n}=new Error;return"string"==typeof n&&n.split("\n").some((t=>-1!==t.indexOf("(https.js:")||-1!==t.indexOf("node:https:")))}createSocket(t,n,e){const r={...n,secureEndpoint:this.isSecureEndpoint(n)};Promise.resolve().then((()=>this.connect(t,r))).then((o=>{if(o instanceof s.Agent)return o.addRequest(t,r);this[cn].currentSocket=o,super.createSocket(t,n,e)}),e)}createConnection(){const t=this[cn].currentSocket;if(this[cn].currentSocket=void 0,!t)throw new Error("No socket was returned in the `connect()` function");return t}get defaultPort(){return this[cn].defaultPort??("https:"===this.protocol?443:80)}set defaultPort(t){this[cn]&&(this[cn].defaultPort=t)}get protocol(){return this[cn].protocol??(this.isSecureEndpoint()?"https:":"http:")}set protocol(t){this[cn]&&(this[cn].protocol=t)}}function an(...t){_.log("[https-proxy-agent:parse-proxy-response]",...t)}function fn(t){return new Promise(((n,e)=>{let r=0;const o=[];function s(){const c=t.read();c?function(c){o.push(c),r+=c.length;const u=Buffer.concat(o,r),a=u.indexOf("\r\n\r\n");if(-1===a)return an("have not received end of HTTP headers yet..."),void s();const f=u.subarray(0,a).toString("ascii").split("\r\n"),h=f.shift();if(!h)return t.destroy(),e(new Error("No header received from proxy CONNECT response"));const p=h.split(" "),l=+(p[1]||0),d=p.slice(2).join(" "),m={};for(const n of f){if(!n)continue;const r=n.indexOf(":");if(-1===r)return t.destroy(),e(new Error(`Invalid header from proxy CONNECT response: "${n}"`));const o=n.slice(0,r).toLowerCase(),s=n.slice(r+1).trimStart(),i=m[o];"string"==typeof i?m[o]=[i,s]:Array.isArray(i)?i.push(s):m[o]=s}an("got proxy server response: %o %o",h,m),i(),n({connect:{statusCode:l,statusText:d,headers:m},buffered:u})}(c):t.once("readable",s)}function i(){t.removeListener("end",c),t.removeListener("error",u),t.removeListener("readable",s)}function c(){i(),an("onend"),e(new Error("Proxy connection ended before receiving CONNECT response"))}function u(t){i(),an("onerror %o",t),e(t)}t.on("error",u),t.on("end",c),s()}))}function hn(...t){_.log("[https-proxy-agent]",...t)}class pn extends un{static __initStatic(){this.protocols=["http","https"]}constructor(t,n){super(n),this.options={},this.proxy="string"==typeof t?new URL(t):t,this.proxyHeaders=n?.headers??{},hn("Creating new HttpsProxyAgent instance: %o",this.proxy.href);const e=(this.proxy.hostname||this.proxy.host).replace(/^\[|\]$/g,""),r=this.proxy.port?parseInt(this.proxy.port,10):"https:"===this.proxy.protocol?443:80;this.connectOpts={ALPNProtocols:["http/1.1"],...n?dn(n,"headers"):null,host:e,port:r}}async connect(t,n){const{proxy:e}=this;if(!n.host)throw new TypeError('No "host" provided');let r;if("https:"===e.protocol){hn("Creating `tls.Socket`: %o",this.connectOpts);const t=this.connectOpts.servername||this.connectOpts.host;r=f.connect({...this.connectOpts,servername:t&&a.isIP(t)?void 0:t})}else hn("Creating `net.Socket`: %o",this.connectOpts),r=a.connect(this.connectOpts);const o="function"==typeof this.proxyHeaders?this.proxyHeaders():{...this.proxyHeaders},s=a.isIPv6(n.host)?`[${n.host}]`:n.host;let i=`CONNECT ${s}:${n.port} HTTP/1.1\r\n`;if(e.username||e.password){const t=`${decodeURIComponent(e.username)}:${decodeURIComponent(e.password)}`;o["Proxy-Authorization"]=`Basic ${Buffer.from(t).toString("base64")}`}o.Host=`${s}:${n.port}`,o["Proxy-Connection"]||(o["Proxy-Connection"]=this.keepAlive?"Keep-Alive":"close");for(const t of Object.keys(o))i+=`${t}: ${o[t]}\r\n`;const c=fn(r);r.write(`${i}\r\n`);const{connect:u,buffered:h}=await c;if(t.emit("proxyConnect",u),this.emit("proxyConnect",u,t),200===u.statusCode){if(t.once("socket",ln),n.secureEndpoint){hn("Upgrading socket connection to TLS");const t=n.servername||n.host;return f.connect({...dn(n,"host","path","port"),socket:r,servername:a.isIP(t)?void 0:t})}return r}r.destroy();const p=new a.Socket({writable:!1});return p.readable=!0,t.once("socket",(t=>{hn("Replaying proxy buffer for failed request"),t.push(h),t.push(null)})),p}}function ln(t){t.resume()}function dn(t,...n){const e={};let r;for(r in t)n.includes(r)||(e[r]=t[r]);return e}pn.__initStatic();const mn=32768;function gn(t){return t.replace(/^[A-Z]:/,"").replace(/\\/g,"/")}const yn=n;let bn,vn=0,_n={};function wn(t){yn.debug&&console.log(`[ANR Worker] ${t}`)}var Sn,$n,En;const xn=function(t){let n;try{n=new URL(t.url)}catch(n){return v((()=>{console.warn("[@sentry/node]: Invalid dsn or tunnel option, will not send any events. The tunnel option must be a full URL when used.")})),en(t,(()=>Promise.resolve({})))}const e="https:"===n.protocol,r=function(t,n){const{no_proxy:e}=process.env,r=e?.split(",").some((n=>t.host.endsWith(n)||t.hostname.endsWith(n)));return r?void 0:n}(n,t.proxy||(e?process.env.https_proxy:void 0)||process.env.http_proxy),o=e?i:s,a=void 0!==t.keepAlive&&t.keepAlive,f=r?new pn(r):new o.Agent({keepAlive:a,maxSockets:30,timeout:2e3}),h=function(t,n,e){const{hostname:r,pathname:o,port:s,protocol:i,search:a}=new URL(t.url);return function(f){return new Promise(((h,p)=>{Ft((()=>{let l=function(t){return new c({read(){this.push(t),this.push(null)}})}(f.body);const d={...t.headers};f.body.length>mn&&(d["content-encoding"]="gzip",l=l.pipe(u()));const m=n.request({method:"POST",agent:e,headers:d,hostname:r,path:`${o}${a}`,port:s,protocol:i,ca:t.caCerts},(t=>{t.on("data",(()=>{})),t.on("end",(()=>{})),t.setEncoding("utf8");const n=t.headers["retry-after"]??null,e=t.headers["x-sentry-rate-limits"]??null;h({statusCode:t.statusCode,headers:{"retry-after":n,"x-sentry-rate-limits":Array.isArray(e)?e[0]||null:e}})}));m.on("error",p),l.pipe(m)}))}))}}(t,t.httpModule??o,f);return en(t,h)}({url:(Sn=yn.dsn,$n=yn.tunnel,En=yn.sdkMetadata.sdk,$n||`${function(t){return`${function(t){const n=t.protocol?`${t.protocol}:`:"",e=t.port?`:${t.port}`:"";return`${n}//${t.host}${e}${t.path?`/${t.path}`:""}/api/`}(t)}${t.projectId}/envelope/`}(Sn)}?${function(t,n){const e={sentry_version:Zt};return t.publicKey&&(e.sentry_key=t.publicKey),n&&(e.sentry_client=`${n.name}/${n.version}`),new URLSearchParams(e).toString()}(Sn,En)}`)});async function Nn(){if(bn){wn("Sending abnormal session"),H(bn,{status:"abnormal",abnormal_mechanism:"anr_foreground",release:yn.release,environment:yn.environment});const t=function(t,n,e,r){const o=Jt(e);return Ut({sent_at:(new Date).toISOString(),...o&&{sdk:o},...!!r&&n&&{dsn:jt(n)}},["aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()]])}(bn,yn.dsn,yn.sdkMetadata,yn.tunnel);wn(JSON.stringify(t)),await xn.send(t);try{e?.postMessage("session-ended")}catch(t){}}}function Cn(t){if(!t)return;const n=function(t){if(!t.length)return[];const n=Array.from(t);return/sentryWrapped/.test(E(n).function||"")&&n.pop(),n.reverse(),$.test(E(n).function||"")&&(n.pop(),$.test(E(n).function||"")&&n.pop()),n.slice(0,w).map((t=>({...t,filename:t.filename||E(n).filename,function:t.function||S})))}(t);if(yn.appRootPath)for(const t of n)t.filename&&(t.filename=Pt(t.filename,yn.appRootPath));return n}async function Tn(t,n){if(vn>=yn.maxAnrEvents)return;vn+=1,await Nn(),wn("Sending event");const e={event_id:L(),contexts:yn.contexts,release:yn.release,environment:yn.environment,dist:yn.dist,platform:"node",level:"error",exception:{values:[{type:"ApplicationNotResponding",value:`Application Not Responding for at least ${yn.anrThreshold} ms`,stacktrace:{frames:Cn(t)},mechanism:{type:"ANR"}}]},tags:yn.staticTags};n&&function(t,n){if(Vt(t,n),!t.contexts?.trace){const{traceId:e,parentSpanId:r,propagationSpanId:o}=n.propagationContext;t.contexts={trace:{trace_id:e,span_id:o||Y(),parent_span_id:r},...t.contexts}}}(e,n),function(t){if(0===Object.keys(_n).length)return;const n=yn.appRootPath?{}:_n;if(yn.appRootPath)for(const[t,e]of Object.entries(_n))n[Pt(t,yn.appRootPath)]=e;const e=new Map;for(const r of t.exception?.values||[])for(const t of r.stacktrace?.frames||[]){const r=t.abs_path||t.filename;r&&n[r]&&e.set(r,n[r])}if(e.size>0){const n=[];for(const[t,r]of e.entries())n.push({type:"sourcemap",code_file:t,debug_id:r});t.debug_meta={images:n}}}(e);const r=zt(e,yn.dsn,yn.sdkMetadata,yn.tunnel);wn(JSON.stringify(r)),await xn.send(r),await xn.flush(2e3),vn>=yn.maxAnrEvents&&setTimeout((()=>{process.exit(0)}),5e3)}let kn;if(wn("Started"),yn.captureStackTrace){wn("Connecting to debugger");const n=new t;n.connectToMainThread(),wn("Connected to debugger");const e=new Map;n.on("Debugger.scriptParsed",(t=>{e.set(t.params.scriptId,t.params.url)})),n.on("Debugger.paused",(t=>{if("other"===t.params.reason)try{wn("Debugger paused");const s=[...t.params.callFrames],i=yn.appRootPath?function(t=(process.argv[1]?on(process.argv[1]):process.cwd()),n="\\"===o){const e=n?gn(t):t;return t=>{if(!t)return;const o=n?gn(t):t;let{dir:s,base:i,ext:c}=r.parse(o);".js"!==c&&".mjs"!==c&&".cjs"!==c||(i=i.slice(0,-1*c.length));const u=decodeURIComponent(i);s||(s=".");const a=s.lastIndexOf("/node_modules");if(a>-1)return`${s.slice(a+14).replace(/\//g,".")}:${u}`;if(s.startsWith(e)){const t=s.slice(e.length+1).replace(/\//g,".");return t?`${t}:${u}`:u}return u}}(yn.appRootPath):()=>{},c=s.map((t=>function(t,n,e){const r=n?n.replace(/^file:\/\//,""):void 0,o=t.location.columnNumber?t.location.columnNumber+1:void 0,s=t.location.lineNumber?t.location.lineNumber+1:void 0;return{filename:r,module:e(r),function:t.functionName||S,colno:o,lineno:s,in_app:r?sn(r):void 0}}(t,e.get(t.location.scriptId),i))),u=setTimeout((()=>{Tn(c).then(null,(()=>{wn("Sending ANR event failed.")}))}),5e3);n.post("Runtime.evaluate",{expression:"global.__SENTRY_GET_SCOPES__();",silent:!0,returnByValue:!0},((t,e)=>{t&&wn(`Error executing script: '${t.message}'`),clearTimeout(u);const r=e?.result?e.result.value:void 0;n.post("Debugger.resume"),n.post("Debugger.disable"),Tn(c,r).then(null,(()=>{wn("Sending ANR event failed.")}))}))}catch(t){throw n.post("Debugger.resume"),n.post("Debugger.disable"),t}})),kn=()=>{try{n.post("Debugger.enable",(()=>{n.post("Debugger.pause")}))}catch(t){}}}const{poll:jn}=function(t,n,e,r){const o=t();let s=!1,i=!0;return setInterval((()=>{const t=o.getTimeMs();!1===s&&t>n+e&&(s=!0,i&&r()),t<n+e&&(s=!1)}),20),{poll:()=>{o.reset()},enabled:t=>{i=t}}}((function(){let t=process.hrtime();return{getTimeMs:()=>{const[n,e]=process.hrtime(t);return Math.floor(1e3*n+e/1e6)},reset:()=>{t=process.hrtime()}}}),yn.pollInterval,yn.anrThreshold,(function(){wn("Watchdog timeout"),kn?(wn("Pausing debugger to capture stack trace"),kn()):(wn("Capturing event without a stack trace"),Tn().then(null,(()=>{wn("Sending ANR event failed on watchdog timeout.")})))}));e?.on("message",(t=>{t.session&&(bn=z(t.session)),t.debugImages&&(_n=t.debugImages),jn()}));
