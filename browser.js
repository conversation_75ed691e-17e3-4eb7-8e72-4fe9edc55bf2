const puppeteer = require('puppeteer');
const path = require('path');

let browser;
let page;

const USER_DATA_DIR = path.resolve(__dirname, 'user_data');

async function initializeBrowser() {
    if (browser && browser.isConnected()) {
        console.log('Trình duyệt đã được kết nối, sử dụng lại instance hiện có.');
        return { browser, page };
    }

    console.log('Đang khởi chạy một instance trình duyệt mới...');
    try {
        browser = await puppeteer.launch({
            headless: false,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox'
            ],
            defaultViewport: {
                width: 1366,
                height: 768
            },
            userDataDir: USER_DATA_DIR
        });
        page = await browser.newPage();

        // Chỉ set user agent đơn giản
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        console.log('Trình duyệt được cấu hình đơn giản như trình duyệt thường.');

        return { browser, page };
    } catch (error) {
        console.error('Không thể khởi chạy trình duyệt:', error);
        throw error;
    }
}

process.on('SIGINT', async () => {
    console.log('Đã nhận SIGINT (Ctrl+C). Giữ trình duyệt mở.');
});

process.on('SIGTERM', async () => {
    console.log('Đã nhận SIGTERM. Giữ trình duyệt mở.');
});

module.exports = { initializeBrowser };