const puppeteer = require('puppeteer');
const path = require('path');

let browser;
let page;

const USER_DATA_DIR = path.resolve(__dirname, 'user_data');

async function initializeBrowser() {
    // Đóng trình duyệt cũ nếu có
    if (browser && browser.isConnected()) {
        console.log('Đóng trình duyệt cũ...');
        try {
            await browser.close();
            await sleep(2000); // Chờ để đảm bảo file được giải phóng
        } catch (error) {
            console.log('Lỗi khi đóng trình duyệt cũ:', error.message);
        }
    }

    console.log('Đang khởi chạy một instance trình duyệt mới...');
    try {
        browser = await puppeteer.launch({
            headless: false,
            userDataDir: USER_DATA_DIR,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage'
            ]
        });
        page = await browser.newPage();

        console.log('Tr<PERSON>nh duyệt Puppeteer cơ bản đã khởi động.');

        return { browser, page };
    } catch (error) {
        console.error('Không thể khởi chạy trình duyệt:', error);
        throw error;
    }
}

// Hàm sleep helper
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Hàm đóng trình duyệt an toàn
async function closeBrowser() {
    if (browser && browser.isConnected()) {
        console.log('Đang đóng trình duyệt...');
        try {
            await browser.close();
            console.log('Trình duyệt đã được đóng.');
        } catch (error) {
            console.error('Lỗi khi đóng trình duyệt:', error.message);
        }
    }
}

process.on('SIGINT', async () => {
    console.log('Đã nhận SIGINT (Ctrl+C). Đóng trình duyệt...');
    await closeBrowser();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('Đã nhận SIGTERM. Đóng trình duyệt...');
    await closeBrowser();
    process.exit(0);
});

process.on('exit', async () => {
    console.log('Ứng dụng đang thoát...');
    await closeBrowser();
});

module.exports = { initializeBrowser, closeBrowser };