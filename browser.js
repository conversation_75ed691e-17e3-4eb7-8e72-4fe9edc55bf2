const puppeteer = require('puppeteer');
const path = require('path');

let browser;
let page;

const USER_DATA_DIR = path.resolve(__dirname, 'user_data');

async function initializeBrowser() {
    if (browser && browser.isConnected()) {
        console.log('Trình duyệt đã được kết nối, sử dụng lại instance hiện có.');
        return { browser, page };
    }

    console.log('Đang khởi chạy một instance trình duyệt mới...');
    try {
        // Thử tìm Edge trước, nếu không có thì dùng Chrome
        let executablePath = null;
        const edgePaths = [
            'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe',
            'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe'
        ];

        const fs = require('fs');
        for (const path of edgePaths) {
            if (fs.existsSync(path)) {
                executablePath = path;
                console.log('Tì<PERSON> thấy Microsoft Edge tại:', path);
                break;
            }
        }

        const launchOptions = {
            headless: false,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox'
            ],
            defaultViewport: {
                width: 1366,
                height: 768
            },
            userDataDir: USER_DATA_DIR
        };

        if (executablePath) {
            launchOptions.executablePath = executablePath;
        }

        browser = await puppeteer.launch(launchOptions);
        page = await browser.newPage();

        // Set user agent phù hợp
        if (executablePath) {
            await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0');
            console.log('Sử dụng Microsoft Edge');
        } else {
            await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
            console.log('Sử dụng Chrome mặc định');
        }

        console.log('Trình duyệt đã được khởi tạo thành công.');

        return { browser, page };
    } catch (error) {
        console.error('Không thể khởi chạy trình duyệt:', error);
        throw error;
    }
}

// Hàm sleep helper
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Hàm đóng trình duyệt an toàn
async function closeBrowser() {
    if (browser && browser.isConnected()) {
        console.log('Đang đóng trình duyệt...');
        try {
            await browser.close();
            console.log('Trình duyệt đã được đóng.');
        } catch (error) {
            console.error('Lỗi khi đóng trình duyệt:', error.message);
        }
    }
}

process.on('SIGINT', async () => {
    console.log('Đã nhận SIGINT (Ctrl+C). Đóng trình duyệt...');
    await closeBrowser();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('Đã nhận SIGTERM. Đóng trình duyệt...');
    await closeBrowser();
    process.exit(0);
});

process.on('exit', async () => {
    console.log('Ứng dụng đang thoát...');
    await closeBrowser();
});

module.exports = { initializeBrowser, closeBrowser };