const puppeteer = require('puppeteer');
const path = require('path');

let browser;
let page;

const USER_DATA_DIR = path.resolve(__dirname, 'user_data');

async function initializeBrowser() {
    if (browser && browser.isConnected()) {
        console.log('Trình duyệt đã được kết nối, sử dụng lại instance hiện có.');
        return { browser, page };
    }

    console.log('Đang khởi chạy một instance trình duyệt mới...');
    try {
        browser = await puppeteer.launch({
            headless: false,
            // slowMo: 50,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--exclude-switches=enable-automation',
                '--disable-extensions-except',
                '--disable-plugins-discovery',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-dev-shm-usage',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-infobars',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-field-trial-config',
                '--disable-back-forward-cache',
                '--disable-ipc-flooding-protection',
                '--no-proxy-server',
                '--disable-proxy-certificate-handler',
                '--disable-background-networking',
                '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                '--accept-lang=en-US,en;q=0.9',
                '--disable-blink-features=AutomationControlled',
                '--disable-client-side-phishing-detection',
                '--disable-sync',
                '--disable-default-apps',
                '--hide-scrollbars',
                '--mute-audio',
                '--no-zygote',
                '--disable-accelerated-2d-canvas',
                '--disable-accelerated-jpeg-decoding',
                '--disable-accelerated-mjpeg-decode',
                '--disable-accelerated-video-decode',
                '--disable-gpu-sandbox',
                '--disable-software-rasterizer'
            ],
            defaultViewport: { // Set desktop viewport
                width: 1366,
                height: 768,
                deviceScaleFactor: 1
            },
            userDataDir: USER_DATA_DIR
        });
        page = await browser.newPage();

        // Set realistic user agent
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        // Advanced anti-detection measures
        await page.evaluateOnNewDocument(() => {
            // Remove webdriver property completely
            delete navigator.__proto__.webdriver;

            // Override webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // Mock chrome object
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {
                    isInstalled: false,
                    InstallState: {
                        DISABLED: 'disabled',
                        INSTALLED: 'installed',
                        NOT_INSTALLED: 'not_installed'
                    },
                    RunningState: {
                        CANNOT_RUN: 'cannot_run',
                        READY_TO_RUN: 'ready_to_run',
                        RUNNING: 'running'
                    }
                }
            };

            // Override plugins with realistic data
            Object.defineProperty(navigator, 'plugins', {
                get: () => [
                    {
                        0: {
                            type: "application/x-google-chrome-pdf",
                            suffixes: "pdf",
                            description: "Portable Document Format"
                        },
                        description: "Portable Document Format",
                        filename: "internal-pdf-viewer",
                        length: 1,
                        name: "Chrome PDF Plugin"
                    },
                    {
                        0: {
                            type: "application/x-nacl",
                            suffixes: "",
                            description: "Native Client Executable"
                        },
                        description: "Native Client Executable",
                        filename: "internal-nacl-plugin",
                        length: 1,
                        name: "Native Client"
                    }
                ],
            });

            // Override languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en', 'vi'],
            });

            // Mock permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );

            // Add realistic screen properties
            Object.defineProperty(screen, 'availTop', { get: () => 0 });
            Object.defineProperty(screen, 'availLeft', { get: () => 0 });
            Object.defineProperty(screen, 'availHeight', { get: () => 1040 });
            Object.defineProperty(screen, 'availWidth', { get: () => 1920 });
            Object.defineProperty(screen, 'colorDepth', { get: () => 24 });
            Object.defineProperty(screen, 'pixelDepth', { get: () => 24 });

            // Mock battery API
            Object.defineProperty(navigator, 'getBattery', {
                get: () => () => Promise.resolve({
                    charging: true,
                    chargingTime: 0,
                    dischargingTime: Infinity,
                    level: 1
                })
            });

            // Hide automation indicators
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false,
            });

            // Override toString methods
            window.navigator.chrome = window.chrome;
            Object.defineProperty(navigator, 'plugins', {
                get: function() { return this.value || []; },
                set: function(v) { this.value = v; }
            });
        });
        // Set extra headers to look more like real browser
        await page.setExtraHTTPHeaders({
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
            'Cache-Control': 'max-age=0'
        });

        // Randomize viewport size slightly
        const randomWidth = 1366 + Math.floor(Math.random() * 100);
        const randomHeight = 768 + Math.floor(Math.random() * 100);
        await page.setViewport({ width: randomWidth, height: randomHeight });

        console.log('Trình duyệt được cấu hình như trình duyệt thật với anti-detection.');

        return { browser, page };
    } catch (error) {
        console.error('Không thể khởi chạy trình duyệt:', error);
        throw error;
    }
}

process.on('SIGINT', async () => {
    console.log('Đã nhận SIGINT (Ctrl+C). Giữ trình duyệt mở.');
});

process.on('SIGTERM', async () => {
    console.log('Đã nhận SIGTERM. Giữ trình duyệt mở.');
});

module.exports = { initializeBrowser };