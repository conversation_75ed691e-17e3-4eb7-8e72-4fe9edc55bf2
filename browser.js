const puppeteer = require('puppeteer');
const path = require('path');

let browser;
let page;

const USER_DATA_DIR = path.resolve(__dirname, 'user_data');

async function initializeBrowser() {
    if (browser && browser.isConnected()) {
        console.log('Trình duyệt đã được kết nố<PERSON>, sử dụng lại instance hiện có.');
        return { browser, page };
    }

    console.log('Đang khởi chạy một instance trình duyệt mới...');
    try {
        browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-features=VizDisplayCompositor'
            ],
            defaultViewport: {
                width: 1366,
                height: 768
            },
            userDataDir: USER_DATA_DIR
        });
        page = await browser.newPage();

        // Set user agent và xóa webdriver property
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        // Xóa dấu hiệu automation cơ bản
        await page.evaluateOnNewDocument(() => {
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        });

        console.log('Sử dụng Chrome với cấu hình cơ bản để tránh phát hiện.');

        console.log('Trình duyệt đã được khởi tạo thành công.');

        return { browser, page };
    } catch (error) {
        console.error('Không thể khởi chạy trình duyệt:', error);
        throw error;
    }
}

// Hàm sleep helper
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Hàm đóng trình duyệt an toàn
async function closeBrowser() {
    if (browser && browser.isConnected()) {
        console.log('Đang đóng trình duyệt...');
        try {
            await browser.close();
            console.log('Trình duyệt đã được đóng.');
        } catch (error) {
            console.error('Lỗi khi đóng trình duyệt:', error.message);
        }
    }
}

process.on('SIGINT', async () => {
    console.log('Đã nhận SIGINT (Ctrl+C). Đóng trình duyệt...');
    await closeBrowser();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('Đã nhận SIGTERM. Đóng trình duyệt...');
    await closeBrowser();
    process.exit(0);
});

process.on('exit', async () => {
    console.log('Ứng dụng đang thoát...');
    await closeBrowser();
});

module.exports = { initializeBrowser, closeBrowser };