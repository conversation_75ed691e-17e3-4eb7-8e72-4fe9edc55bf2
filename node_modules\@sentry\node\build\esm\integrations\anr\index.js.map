{"version": 3, "file": "index.js", "sources": ["../../../../src/integrations/anr/index.ts"], "sourcesContent": ["import { types } from 'node:util';\nimport { Worker } from 'node:worker_threads';\nimport type { Contexts, Event, EventHint, Integration, IntegrationFn, ScopeData } from '@sentry/core';\nimport {\n  defineIntegration,\n  getClient,\n  getCurrentScope,\n  getFilenameToDebugIdMap,\n  getGlobalScope,\n  getIsolationScope,\n  GLOBAL_OBJ,\n  logger,\n  mergeScopeData,\n} from '@sentry/core';\nimport { NODE_VERSION } from '../../nodeVersion';\nimport type { NodeClient } from '../../sdk/client';\nimport { isDebuggerEnabled } from '../../utils/debug';\nimport type { AnrIntegrationOptions, WorkerStartData } from './common';\n\nconst { isPromise } = types;\n\n// This string is a placeholder that gets overwritten with the worker code.\nexport const base64WorkerScript = '###AnrWorkerScript###';\n\nconst DEFAULT_INTERVAL = 50;\nconst DEFAULT_HANG_THRESHOLD = 5000;\n\nfunction log(message: string, ...args: unknown[]): void {\n  logger.log(`[ANR] ${message}`, ...args);\n}\n\nfunction globalWithScopeFetchFn(): typeof GLOBAL_OBJ & { __SENTRY_GET_SCOPES__?: () => ScopeData } {\n  return GLOBAL_OBJ;\n}\n\n/** Fetches merged scope data */\nfunction getScopeData(): ScopeData {\n  const scope = getGlobalScope().getScopeData();\n  mergeScopeData(scope, getIsolationScope().getScopeData());\n  mergeScopeData(scope, getCurrentScope().getScopeData());\n\n  // We remove attachments because they likely won't serialize well as json\n  scope.attachments = [];\n  // We can't serialize event processor functions\n  scope.eventProcessors = [];\n\n  return scope;\n}\n\n/**\n * Gets contexts by calling all event processors. This shouldn't be called until all integrations are setup\n */\nasync function getContexts(client: NodeClient): Promise<Contexts> {\n  let event: Event | null = { message: 'ANR' };\n  const eventHint: EventHint = {};\n\n  for (const processor of client.getEventProcessors()) {\n    if (event === null) break;\n    event = await processor(event, eventHint);\n  }\n\n  return event?.contexts || {};\n}\n\nconst INTEGRATION_NAME = 'Anr';\n\ntype AnrInternal = { startWorker: () => void; stopWorker: () => void };\n\nconst _anrIntegration = ((options: Partial<AnrIntegrationOptions> = {}) => {\n  if (NODE_VERSION.major < 16 || (NODE_VERSION.major === 16 && NODE_VERSION.minor < 17)) {\n    throw new Error('ANR detection requires Node 16.17.0 or later');\n  }\n\n  let worker: Promise<() => void> | undefined;\n  let client: NodeClient | undefined;\n\n  // Hookup the scope fetch function to the global object so that it can be called from the worker thread via the\n  // debugger when it pauses\n  const gbl = globalWithScopeFetchFn();\n  gbl.__SENTRY_GET_SCOPES__ = getScopeData;\n\n  return {\n    name: INTEGRATION_NAME,\n    startWorker: () => {\n      if (worker) {\n        return;\n      }\n\n      if (client) {\n        worker = _startWorker(client, options);\n      }\n    },\n    stopWorker: () => {\n      if (worker) {\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        worker.then(stop => {\n          stop();\n          worker = undefined;\n        });\n      }\n    },\n    async setup(initClient: NodeClient) {\n      client = initClient;\n\n      if (options.captureStackTrace && (await isDebuggerEnabled())) {\n        logger.warn('ANR captureStackTrace has been disabled because the debugger was already enabled');\n        options.captureStackTrace = false;\n      }\n\n      // setImmediate is used to ensure that all other integrations have had their setup called first.\n      // This allows us to call into all integrations to fetch the full context\n      setImmediate(() => this.startWorker());\n    },\n  } as Integration & AnrInternal;\n}) satisfies IntegrationFn;\n\ntype AnrReturn = (options?: Partial<AnrIntegrationOptions>) => Integration & AnrInternal;\n\nexport const anrIntegration = defineIntegration(_anrIntegration) as AnrReturn;\n\n/**\n * Starts the ANR worker thread\n *\n * @returns A function to stop the worker\n */\nasync function _startWorker(\n  client: NodeClient,\n  integrationOptions: Partial<AnrIntegrationOptions>,\n): Promise<() => void> {\n  const dsn = client.getDsn();\n\n  if (!dsn) {\n    return () => {\n      //\n    };\n  }\n\n  const contexts = await getContexts(client);\n\n  // These will not be accurate if sent later from the worker thread\n  delete contexts.app?.app_memory;\n  delete contexts.device?.free_memory;\n\n  const initOptions = client.getOptions();\n\n  const sdkMetadata = client.getSdkMetadata() || {};\n  if (sdkMetadata.sdk) {\n    sdkMetadata.sdk.integrations = initOptions.integrations.map(i => i.name);\n  }\n\n  const options: WorkerStartData = {\n    debug: logger.isEnabled(),\n    dsn,\n    tunnel: initOptions.tunnel,\n    environment: initOptions.environment || 'production',\n    release: initOptions.release,\n    dist: initOptions.dist,\n    sdkMetadata,\n    appRootPath: integrationOptions.appRootPath,\n    pollInterval: integrationOptions.pollInterval || DEFAULT_INTERVAL,\n    anrThreshold: integrationOptions.anrThreshold || DEFAULT_HANG_THRESHOLD,\n    captureStackTrace: !!integrationOptions.captureStackTrace,\n    maxAnrEvents: integrationOptions.maxAnrEvents || 1,\n    staticTags: integrationOptions.staticTags || {},\n    contexts,\n  };\n\n  if (options.captureStackTrace) {\n    const inspector = await import('node:inspector');\n    if (!inspector.url()) {\n      inspector.open(0);\n    }\n  }\n\n  const worker = new Worker(new URL(`data:application/javascript;base64,${base64WorkerScript}`), {\n    workerData: options,\n    // We don't want any Node args to be passed to the worker\n    execArgv: [],\n    env: { ...process.env, NODE_OPTIONS: undefined },\n  });\n\n  process.on('exit', () => {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    worker.terminate();\n  });\n\n  const timer = setInterval(() => {\n    try {\n      const currentSession = getIsolationScope().getSession();\n      // We need to copy the session object and remove the toJSON method so it can be sent to the worker\n      // serialized without making it a SerializedSession\n      const session = currentSession ? { ...currentSession, toJSON: undefined } : undefined;\n      // message the worker to tell it the main event loop is still running\n      worker.postMessage({ session, debugImages: getFilenameToDebugIdMap(initOptions.stackParser) });\n    } catch (_) {\n      //\n    }\n  }, options.pollInterval);\n  // Timer should not block exit\n  timer.unref();\n\n  worker.on('message', (msg: string) => {\n    if (msg === 'session-ended') {\n      log('ANR event sent from ANR worker. Clearing session in this thread.');\n      getIsolationScope().setSession(undefined);\n    }\n  });\n\n  worker.once('error', (err: Error) => {\n    clearInterval(timer);\n    log('ANR worker error', err);\n  });\n\n  worker.once('exit', (code: number) => {\n    clearInterval(timer);\n    log('ANR worker exit', code);\n  });\n\n  // Ensure this thread can't block app exit\n  worker.unref();\n\n  return () => {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    worker.terminate();\n    clearInterval(timer);\n  };\n}\n\nexport function disableAnrDetectionForCallback<T>(callback: () => T): T;\nexport function disableAnrDetectionForCallback<T>(callback: () => Promise<T>): Promise<T>;\n/**\n * Disables ANR detection for the duration of the callback\n */\nexport function disableAnrDetectionForCallback<T>(callback: () => T | Promise<T>): T | Promise<T> {\n  const integration = getClient()?.getIntegrationByName(INTEGRATION_NAME) as AnrInternal | undefined;\n\n  if (!integration) {\n    return callback();\n  }\n\n  integration.stopWorker();\n\n  const result = callback();\n  if (isPromise(result)) {\n    return result.finally(() => integration.startWorker());\n  }\n\n  integration.startWorker();\n  return result;\n}\n"], "names": [], "mappings": ";;;;;;AAmBA,MAAM,EAAE,SAAA,EAAY,GAAE,KAAK;;AAE3B;AACO,MAAM,kBAAmB,GAAE;;AAElC,MAAM,gBAAA,GAAmB,EAAE;AAC3B,MAAM,sBAAA,GAAyB,IAAI;;AAEnC,SAAS,GAAG,CAAC,OAAO,EAAU,GAAG,IAAI,EAAmB;AACxD,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA,EAAA,GAAA,IAAA,CAAA;AACA;;AAEA,SAAA,sBAAA,GAAA;AACA,EAAA,OAAA,UAAA;AACA;;AAEA;AACA,SAAA,YAAA,GAAA;AACA,EAAA,MAAA,KAAA,GAAA,cAAA,EAAA,CAAA,YAAA,EAAA;AACA,EAAA,cAAA,CAAA,KAAA,EAAA,iBAAA,EAAA,CAAA,YAAA,EAAA,CAAA;AACA,EAAA,cAAA,CAAA,KAAA,EAAA,eAAA,EAAA,CAAA,YAAA,EAAA,CAAA;;AAEA;AACA,EAAA,KAAA,CAAA,WAAA,GAAA,EAAA;AACA;AACA,EAAA,KAAA,CAAA,eAAA,GAAA,EAAA;;AAEA,EAAA,OAAA,KAAA;AACA;;AAEA;AACA;AACA;AACA,eAAA,WAAA,CAAA,MAAA,EAAA;AACA,EAAA,IAAA,KAAA,GAAA,EAAA,OAAA,EAAA,KAAA,EAAA;AACA,EAAA,MAAA,SAAA,GAAA,EAAA;;AAEA,EAAA,KAAA,MAAA,SAAA,IAAA,MAAA,CAAA,kBAAA,EAAA,EAAA;AACA,IAAA,IAAA,KAAA,KAAA,IAAA,EAAA;AACA,IAAA,KAAA,GAAA,MAAA,SAAA,CAAA,KAAA,EAAA,SAAA,CAAA;AACA;;AAEA,EAAA,OAAA,KAAA,EAAA,QAAA,IAAA,EAAA;AACA;;AAEA,MAAA,gBAAA,GAAA,KAAA;;AAIA,MAAA,eAAA,IAAA,CAAA,OAAA,GAAA,EAAA,KAAA;AACA,EAAA,IAAA,YAAA,CAAA,KAAA,GAAA,EAAA,KAAA,YAAA,CAAA,KAAA,KAAA,EAAA,IAAA,YAAA,CAAA,KAAA,GAAA,EAAA,CAAA,EAAA;AACA,IAAA,MAAA,IAAA,KAAA,CAAA,8CAAA,CAAA;AACA;;AAEA,EAAA,IAAA,MAAA;AACA,EAAA,IAAA,MAAA;;AAEA;AACA;AACA,EAAA,MAAA,GAAA,GAAA,sBAAA,EAAA;AACA,EAAA,GAAA,CAAA,qBAAA,GAAA,YAAA;;AAEA,EAAA,OAAA;AACA,IAAA,IAAA,EAAA,gBAAA;AACA,IAAA,WAAA,EAAA,MAAA;AACA,MAAA,IAAA,MAAA,EAAA;AACA,QAAA;AACA;;AAEA,MAAA,IAAA,MAAA,EAAA;AACA,QAAA,MAAA,GAAA,YAAA,CAAA,MAAA,EAAA,OAAA,CAAA;AACA;AACA,KAAA;AACA,IAAA,UAAA,EAAA,MAAA;AACA,MAAA,IAAA,MAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,IAAA,IAAA;AACA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,GAAA,SAAA;AACA,SAAA,CAAA;AACA;AACA,KAAA;AACA,IAAA,MAAA,KAAA,CAAA,UAAA,EAAA;AACA,MAAA,MAAA,GAAA,UAAA;;AAEA,MAAA,IAAA,OAAA,CAAA,iBAAA,KAAA,MAAA,iBAAA,EAAA,CAAA,EAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,kFAAA,CAAA;AACA,QAAA,OAAA,CAAA,iBAAA,GAAA,KAAA;AACA;;AAEA;AACA;AACA,MAAA,YAAA,CAAA,MAAA,IAAA,CAAA,WAAA,EAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA,CAAA,CAAA;;AAIA,MAAA,cAAA,GAAA,iBAAA,CAAA,eAAA,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA,eAAA,YAAA;AACA,EAAA,MAAA;AACA,EAAA,kBAAA;AACA,EAAA;AACA,EAAA,MAAA,GAAA,GAAA,MAAA,CAAA,MAAA,EAAA;;AAEA,EAAA,IAAA,CAAA,GAAA,EAAA;AACA,IAAA,OAAA,MAAA;AACA;AACA,KAAA;AACA;;AAEA,EAAA,MAAA,QAAA,GAAA,MAAA,WAAA,CAAA,MAAA,CAAA;;AAEA;AACA,EAAA,OAAA,QAAA,CAAA,GAAA,EAAA,UAAA;AACA,EAAA,OAAA,QAAA,CAAA,MAAA,EAAA,WAAA;;AAEA,EAAA,MAAA,WAAA,GAAA,MAAA,CAAA,UAAA,EAAA;;AAEA,EAAA,MAAA,WAAA,GAAA,MAAA,CAAA,cAAA,EAAA,IAAA,EAAA;AACA,EAAA,IAAA,WAAA,CAAA,GAAA,EAAA;AACA,IAAA,WAAA,CAAA,GAAA,CAAA,YAAA,GAAA,WAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA;AACA;;AAEA,EAAA,MAAA,OAAA,GAAA;AACA,IAAA,KAAA,EAAA,MAAA,CAAA,SAAA,EAAA;AACA,IAAA,GAAA;AACA,IAAA,MAAA,EAAA,WAAA,CAAA,MAAA;AACA,IAAA,WAAA,EAAA,WAAA,CAAA,WAAA,IAAA,YAAA;AACA,IAAA,OAAA,EAAA,WAAA,CAAA,OAAA;AACA,IAAA,IAAA,EAAA,WAAA,CAAA,IAAA;AACA,IAAA,WAAA;AACA,IAAA,WAAA,EAAA,kBAAA,CAAA,WAAA;AACA,IAAA,YAAA,EAAA,kBAAA,CAAA,YAAA,IAAA,gBAAA;AACA,IAAA,YAAA,EAAA,kBAAA,CAAA,YAAA,IAAA,sBAAA;AACA,IAAA,iBAAA,EAAA,CAAA,CAAA,kBAAA,CAAA,iBAAA;AACA,IAAA,YAAA,EAAA,kBAAA,CAAA,YAAA,IAAA,CAAA;AACA,IAAA,UAAA,EAAA,kBAAA,CAAA,UAAA,IAAA,EAAA;AACA,IAAA,QAAA;AACA,GAAA;;AAEA,EAAA,IAAA,OAAA,CAAA,iBAAA,EAAA;AACA,IAAA,MAAA,SAAA,GAAA,MAAA,OAAA,gBAAA,CAAA;AACA,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,EAAA,EAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA;AACA;;AAEA,EAAA,MAAA,MAAA,GAAA,IAAA,MAAA,CAAA,IAAA,GAAA,CAAA,CAAA,mCAAA,EAAA,kBAAA,CAAA,CAAA,CAAA,EAAA;AACA,IAAA,UAAA,EAAA,OAAA;AACA;AACA,IAAA,QAAA,EAAA,EAAA;AACA,IAAA,GAAA,EAAA,EAAA,GAAA,OAAA,CAAA,GAAA,EAAA,YAAA,EAAA,SAAA,EAAA;AACA,GAAA,CAAA;;AAEA,EAAA,OAAA,CAAA,EAAA,CAAA,MAAA,EAAA,MAAA;AACA;AACA,IAAA,MAAA,CAAA,SAAA,EAAA;AACA,GAAA,CAAA;;AAEA,EAAA,MAAA,KAAA,GAAA,WAAA,CAAA,MAAA;AACA,IAAA,IAAA;AACA,MAAA,MAAA,cAAA,GAAA,iBAAA,EAAA,CAAA,UAAA,EAAA;AACA;AACA;AACA,MAAA,MAAA,OAAA,GAAA,cAAA,GAAA,EAAA,GAAA,cAAA,EAAA,MAAA,EAAA,SAAA,EAAA,GAAA,SAAA;AACA;AACA,MAAA,MAAA,CAAA,WAAA,CAAA,EAAA,OAAA,EAAA,WAAA,EAAA,uBAAA,CAAA,WAAA,CAAA,WAAA,CAAA,EAAA,CAAA;AACA,KAAA,CAAA,OAAA,CAAA,EAAA;AACA;AACA;AACA,GAAA,EAAA,OAAA,CAAA,YAAA,CAAA;AACA;AACA,EAAA,KAAA,CAAA,KAAA,EAAA;;AAEA,EAAA,MAAA,CAAA,EAAA,CAAA,SAAA,EAAA,CAAA,GAAA,KAAA;AACA,IAAA,IAAA,GAAA,KAAA,eAAA,EAAA;AACA,MAAA,GAAA,CAAA,kEAAA,CAAA;AACA,MAAA,iBAAA,EAAA,CAAA,UAAA,CAAA,SAAA,CAAA;AACA;AACA,GAAA,CAAA;;AAEA,EAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA,CAAA,GAAA,KAAA;AACA,IAAA,aAAA,CAAA,KAAA,CAAA;AACA,IAAA,GAAA,CAAA,kBAAA,EAAA,GAAA,CAAA;AACA,GAAA,CAAA;;AAEA,EAAA,MAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,IAAA,KAAA;AACA,IAAA,aAAA,CAAA,KAAA,CAAA;AACA,IAAA,GAAA,CAAA,iBAAA,EAAA,IAAA,CAAA;AACA,GAAA,CAAA;;AAEA;AACA,EAAA,MAAA,CAAA,KAAA,EAAA;;AAEA,EAAA,OAAA,MAAA;AACA;AACA,IAAA,MAAA,CAAA,SAAA,EAAA;AACA,IAAA,aAAA,CAAA,KAAA,CAAA;AACA,GAAA;AACA;;AAIA;AACA;AACA;AACA,SAAA,8BAAA,CAAA,QAAA,EAAA;AACA,EAAA,MAAA,WAAA,GAAA,SAAA,EAAA,EAAA,oBAAA,CAAA,gBAAA,CAAA;;AAEA,EAAA,IAAA,CAAA,WAAA,EAAA;AACA,IAAA,OAAA,QAAA,EAAA;AACA;;AAEA,EAAA,WAAA,CAAA,UAAA,EAAA;;AAEA,EAAA,MAAA,MAAA,GAAA,QAAA,EAAA;AACA,EAAA,IAAA,SAAA,CAAA,MAAA,CAAA,EAAA;AACA,IAAA,OAAA,MAAA,CAAA,OAAA,CAAA,MAAA,WAAA,CAAA,WAAA,EAAA,CAAA;AACA;;AAEA,EAAA,WAAA,CAAA,WAAA,EAAA;AACA,EAAA,OAAA,MAAA;AACA;;;;"}