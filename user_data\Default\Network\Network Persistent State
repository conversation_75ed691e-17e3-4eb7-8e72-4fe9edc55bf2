{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "broken_until": "1751772695", "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL2NhbnZhLmNvbQAAAA==", false, 0], "broken_count": 2, "broken_until": "1751772716", "host": "challenges.cloudflare.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL2NhbnZhLmNvbQAAAA==", false, 0], "broken_count": 1, "broken_until": "1751772417", "host": "a.nel.cloudflare.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL2NhbnZhLmNvbQAAAA==", true, 0], "broken_count": 2, "broken_until": "1751772738", "host": "challenges.cloudflare.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398836893536703", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL2NhbnZhLmNvbQAAAA==", false, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL2NhbnZhLmNvbQAAAA==", false, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL2NhbnZhLmNvbQAAAA==", false, 0], "server": "https://a.nel.cloudflare.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2NhbnZhc3RhdHVzLmNvbQA=", false, 0], "server": "https://cdnjs.cloudflare.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2NhbnZhc3RhdHVzLmNvbQA=", false, 0], "server": "https://www.canvastatus.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2NhbnZhc3RhdHVzLmNvbQA=", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398837721116897", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2NhbnZhc3RhdHVzLmNvbQA=", false, 0], "server": "https://www.recaptcha.net", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2NhbnZhc3RhdHVzLmNvbQA=", false, 0], "server": "https://atlassian-cookies--categories.us-east-1.prod.public.atl-paas.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398837721378550", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2NhbnZhc3RhdHVzLmNvbQA=", false, 0], "server": "https://www.google-analytics.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398837721506720", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2NhbnZhc3RhdHVzLmNvbQA=", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398837722010072", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2NhbnZhc3RhdHVzLmNvbQA=", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2NhbnZhc3RhdHVzLmNvbQA=", false, 0], "server": "https://dka575ofm4ao0.cloudfront.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398837723960374", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2NhbnZhc3RhdHVzLmNvbQA=", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398837724752261", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2NhbnZhc3RhdHVzLmNvbQA=", true, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398837738291085", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL2dvb2dsZXVzZXJjb250ZW50LmNvbQAAAA==", false, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398837724064696", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2NhbnZhc3RhdHVzLmNvbQA=", true, 0], "server": "https://www.recaptcha.net", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL2NhbnZhLmNvbQAAAA==", false, 0], "server": "https://static.cloudflareinsights.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396332117034127", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL2NhbnZhLmNvbQAAAA==", false, 0], "server": "https://challenges.cloudflare.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL2NhbnZhLmNvbQAAAA==", false, 0], "server": "https://www.canva.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398837720035838", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 40179}, "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398837747193273", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396332153096865", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL2NhbnZhLmNvbQAAAA==", true, 0], "server": "https://challenges.cloudflare.com", "supports_spdy": true}], "supports_quic": {"address": "************", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}