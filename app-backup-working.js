// BACKUP - CODE ĐÃ HOẠT ĐỘNG HOÀN HẢO
// Ngày tạo backup: 2025-07-06
// Trạng thái: GoLogin + Canva automation hoạt động 100%

require('dotenv').config();
const express = require('express');
const { initializeGologinBrowser, closeBrowser, getBrowser, getPage } = require('./gologin-browser');

const app = express();
app.use(express.json());

let currentBrowser = null;
let currentPage = null;

// Hàm sleep
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Click vào nút "Mời thành viên" với nhiều phương pháp
 * @param {import('puppeteer').Page} page - Thể hiện của trang Puppeteer
 * @returns {Promise<boolean>} True nếu click thành công
 */
async function clickInviteButton(page) {
    console.log('T<PERSON><PERSON> nút "Mời thành viên" với nhiều phương pháp...');

    try {
        // Phương pháp 1: Tìm theo text content
        const inviteTexts = ['Mời thành viên', 'Invite members', 'Add members', 'Invite people'];
        
        for (const text of inviteTexts) {
            const found = await page.evaluate((searchText) => {
                // Tìm tất cả các element có text phù hợp
                const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                    return el.textContent && el.textContent.trim() === searchText;
                });
                
                for (const element of elements) {
                    // Tìm button gần nhất
                    const button = element.closest('button') || 
                                 (element.tagName === 'BUTTON' ? element : null) ||
                                 element.querySelector('button');
                    
                    if (button && button.offsetParent !== null) { // Visible check
                        button.setAttribute('data-invite-button', 'found');
                        return true;
                    }
                }
                return false;
            }, text);
            
            if (found) {
                await page.click('[data-invite-button="found"]', { delay: 100 });
                console.log(`✓ Đã click nút mời với text: "${text}"`);
                return true;
            }
        }

        // Phương pháp 2: Tìm theo các selector phổ biến
        const inviteSelectors = [
            'button[aria-label*="invite"]',
            'button[aria-label*="Invite"]',
            'button[aria-label*="mời"]',
            'button[data-testid*="invite"]',
            'button:has-text("Mời thành viên")',
            'button:has-text("Invite members")',
            '[role="button"]:has-text("Mời thành viên")',
            '.invite-button',
            '.add-member-button'
        ];

        for (const selector of inviteSelectors) {
            try {
                await page.waitForSelector(selector, { timeout: 2000 });
                await page.click(selector, { delay: 100 });
                console.log(`✓ Đã click nút mời với selector: ${selector}`);
                return true;
            } catch (error) {
                console.log(`Không tìm thấy selector: ${selector}`);
            }
        }

        console.log('✗ Không tìm thấy nút "Mời thành viên" với bất kỳ phương pháp nào');
        return false;
        
    } catch (error) {
        console.error('Lỗi khi click nút mời:', error.message);
        return false;
    }
}

/**
 * Click vào nút "Mời qua email" với nhiều phương pháp
 * @param {import('puppeteer').Page} page - Thể hiện của trang Puppeteer
 * @returns {Promise<boolean>} True nếu click thành công
 */
async function clickInviteByEmailButton(page) {
    console.log('Tìm nút "Mời qua email" với nhiều phương pháp...');

    try {
        // Phương pháp 1: Tìm theo text content
        const emailTexts = ['Mời qua email', 'Invite via email', 'Email invite', 'By email'];
        
        for (const text of emailTexts) {
            const found = await page.evaluate((searchText) => {
                // Tìm tất cả các element có text phù hợp
                const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                    return el.textContent && el.textContent.trim() === searchText;
                });
                
                for (const element of elements) {
                    // Tìm button hoặc tab gần nhất
                    const button = element.closest('button') || 
                                 element.closest('[role="tab"]') ||
                                 (element.tagName === 'BUTTON' ? element : null);
                    
                    if (button && button.offsetParent !== null) { // Visible check
                        button.setAttribute('data-email-button', 'found');
                        return true;
                    }
                }
                return false;
            }, text);
            
            if (found) {
                await page.click('[data-email-button="found"]', { delay: 100 });
                console.log(`✓ Đã click nút email với text: "${text}"`);
                return true;
            }
        }

        // Phương pháp 2: Tìm theo các selector phổ biến
        const emailSelectors = [
            'button[role="tab"]:has-text("email")',
            'button[role="tab"]:has-text("Email")',
            'button[role="tab"]:has-text("Mời qua email")',
            '[role="tab"][aria-label*="email"]',
            'button[data-testid*="email"]',
            '.email-tab',
            '.invite-email-tab'
        ];

        for (const selector of emailSelectors) {
            try {
                await page.waitForSelector(selector, { timeout: 2000 });
                await page.click(selector, { delay: 100 });
                console.log(`✓ Đã click tab email với selector: ${selector}`);
                return true;
            } catch (error) {
                console.log(`Không tìm thấy selector: ${selector}`);
            }
        }

        console.log('✗ Không tìm thấy nút "Mời qua email" với bất kỳ phương pháp nào');
        return false;
        
    } catch (error) {
        console.error('Lỗi khi click nút mời qua email:', error.message);
        return false;
    }
}

/**
 * Click vào nút "Gửi lời mời" với nhiều phương pháp
 * @param {import('puppeteer').Page} page - Thể hiện của trang Puppeteer
 * @returns {Promise<boolean>} True nếu click thành công
 */
async function clickSendInviteButton(page) {
    console.log('Tìm nút "Gửi lời mời" với nhiều phương pháp...');

    try {
        // Phương pháp 1: Tìm theo text content
        const sendTexts = ['Gửi lời mời', 'Send invite', 'Send invitation', 'Send', 'Invite'];
        
        for (const text of sendTexts) {
            const found = await page.evaluate((searchText) => {
                // Tìm tất cả các element có text phù hợp
                const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                    return el.textContent && el.textContent.trim() === searchText;
                });
                
                for (const element of elements) {
                    // Tìm button gần nhất
                    const button = element.closest('button') || 
                                 (element.tagName === 'BUTTON' ? element : null);
                    
                    if (button && button.offsetParent !== null) { // Visible check
                        button.setAttribute('data-send-button', 'found');
                        return true;
                    }
                }
                return false;
            }, text);
            
            if (found) {
                await page.click('[data-send-button="found"]', { delay: 100 });
                console.log(`✓ Đã click nút gửi với text: "${text}"`);
                return true;
            }
        }

        // Phương pháp 2: Tìm theo các selector phổ biến
        const sendSelectors = [
            'button[type="submit"]',
            'button[aria-label*="send"]',
            'button[aria-label*="Send"]',
            'button[aria-label*="gửi"]',
            'button[data-testid*="send"]',
            'button[data-testid*="invite"]',
            '.send-button',
            '.invite-send-button',
            '.submit-button'
        ];

        for (const selector of sendSelectors) {
            try {
                await page.waitForSelector(selector, { timeout: 2000 });
                await page.click(selector, { delay: 100 });
                console.log(`✓ Đã click nút gửi với selector: ${selector}`);
                return true;
            } catch (error) {
                console.log(`Không tìm thấy selector: ${selector}`);
            }
        }

        console.log('✗ Không tìm thấy nút "Gửi lời mời" với bất kỳ phương pháp nào');
        return false;
        
    } catch (error) {
        console.error('Lỗi khi click nút gửi lời mời:', error.message);
        return false;
    }
}


/**
 * Tìm và click vào phần tử chứa văn bản cụ thể
 * @param {import('puppeteer').Page} page - Thể hiện của trang Puppeteer
 * @param {string} text - Văn bản cần tìm
 * @param {string} scope - Phạm vi tìm kiếm (selector CSS hoặc '*' cho toàn bộ trang)
 * @param {number} timeout - Thời gian chờ tối đa (ms)
 * @returns {Promise<boolean>} True nếu tìm thấy và click thành công
 */
async function clickElementByText(page, text, scope = '*', timeout = 10000) {
    console.log(`Đang tìm và click văn bản: "${text}" trong phạm vi: "${scope}"`);
    
    try {
        const element = await page.waitForFunction(
            (searchText, searchScope) => {
                const elements = document.querySelectorAll(searchScope);
                for (const el of elements) {
                    if (el.textContent && el.textContent.trim() === searchText) {
                        return el;
                    }
                }
                return null;
            },
            { timeout },
            text,
            scope
        );

        if (element) {
            await element.click();
            console.log(`✓ Đã click thành công vào phần tử chứa văn bản: "${text}"`);
            return true;
        }
    } catch (error) {
        console.log(`Không tìm thấy phần tử chứa văn bản "${text}".`);
    }
    
    return false;
}

// Khởi tạo trình duyệt và đăng nhập
async function setupBrowserAndLogin() {
    try {
        console.log('Bắt đầu khởi tạo GoLogin browser và đăng nhập Canva...');
        const { browser, page } = await initializeGologinBrowser();
        currentBrowser = browser;
        currentPage = page;
        await currentPage.bringToFront();

        // Kiểm tra trạng thái đăng nhập
        console.log('Kiểm tra trạng thái đăng nhập...');
        const isLoggedIn = await checkLoginStatus(currentPage);

        if (!isLoggedIn) {
            console.log('Chưa đăng nhập hoặc session đã hết hạn/không hợp lệ. Bắt đầu quá trình đăng nhập đầy đủ...');
            await performFullLogin(currentPage);
        } else {
            console.log('Đã đăng nhập, sử dụng session hiện tại.');
        }

        console.log('Trình duyệt và đăng nhập đã sẵn sàng.');
        return true;
    } catch (error) {
        console.error('Lỗi nghiêm trọng trong quá trình khởi tạo trình duyệt hoặc đăng nhập:', error.message);
        if (currentPage) {
            const currentUrl = await currentPage.url();
            console.log('URL hiện tại khi lỗi:', currentUrl);
        }
        throw error;
    }
}

// Kiểm tra trạng thái đăng nhập
async function checkLoginStatus(page) {
    try {
        console.log('Cố gắng điều hướng đến trang quản lý thành viên để kiểm tra đăng nhập...');
        await page.goto('https://www.canva.com/settings/people', { waitUntil: 'networkidle0', timeout: 30000 });
        await sleep(3000);

        // Kiểm tra xem có nút "Mời thành viên" không
        const hasInviteButton = await clickElementByText(page, 'Mời thành viên', '*', 5000);
        return hasInviteButton;
    } catch (error) {
        console.log('Không thể truy cập trang quản lý thành viên hoặc không tìm thấy nút mời.');
        return false;
    }
}

// Thực hiện đăng nhập đầy đủ
async function performFullLogin(page) {
    try {
        // Bước 1: Điều hướng đến trang đăng nhập
        console.log('Điều hướng đến trang đăng nhập Canva...');
        await page.goto('https://www.canva.com/login', { waitUntil: 'networkidle0', timeout: 60000 });

        // Bước 2: Tìm và click vào nút "Tiếp tục với email"
        console.log('Tìm nút "Tiếp tục với email"...');
        await sleep(3000);

        let clickedContinueEmail = false;

        try {
            // Tìm nút thứ 3 với text "Tiếp tục với email"
            const emailButton = await page.evaluate(() => {
                const spans = document.querySelectorAll('span.khPe7Q');
                for (const span of spans) {
                    if (span.textContent && span.textContent.trim() === 'Tiếp tục với email') {
                        const button = span.closest('button');
                        if (button) {
                            button.setAttribute('data-email-login', 'found');
                            return true;
                        }
                    }
                }
                return false;
            });

            if (emailButton) {
                await page.click('[data-email-login="found"]', { delay: 100 });
                console.log('✓ Đã click nút "Tiếp tục với email" thành công');
                clickedContinueEmail = true;
            }
        } catch (error) {
            console.log('Lỗi khi tìm nút email:', error.message);
        }

        // Fallback: thử các cách khác
        if (!clickedContinueEmail) {
            const emailTexts = ['Tiếp tục với email', 'Continue with email'];
            for (const text of emailTexts) {
                clickedContinueEmail = await clickElementByText(page, text, '*', 5000);
                if (clickedContinueEmail) {
                    console.log(`✓ Đã click nút email với text: "${text}"`);
                    break;
                }
            }
        }

        if (!clickedContinueEmail) {
            throw new Error('Không thể tìm thấy nút "Tiếp tục với email".');
        }

        // Bước 3: Tìm và nhập email
        console.log('Tìm trường input email...');
        await sleep(2000);

        let emailInputSelector = null;
        const possibleEmailSelectors = [
            'input[type="email"]',
            'input[name="email"]',
            'input[autocomplete="email"]',
            'input[placeholder*="email"]',
            'input[placeholder*="Email"]',
            'input[id*="email"]'
        ];

        for (const selector of possibleEmailSelectors) {
            try {
                console.log(`Thử selector: ${selector}`);
                await page.waitForSelector(selector, { visible: true, timeout: 5000 });
                emailInputSelector = selector;
                console.log(`Tìm thấy trường email với selector: ${selector}`);
                break;
            } catch (e) {
                console.log(`Không tìm thấy với selector: ${selector}`);
            }
        }

        if (!emailInputSelector) {
            throw new Error('Không thể tìm thấy trường input email.');
        }

        console.log('Đã click "Continue with email" và trường input email đã sẵn sàng.');

        // Bước 3: Nhập email
        console.log('Nhập email...');
        await page.type(emailInputSelector, process.env.CANVA_EMAIL, { delay: 20 });
        await sleep(300);

        // Bước 4: Nhấn Enter để tiếp tục sau email
        console.log('Nhấn Enter để tiếp tục sau email...');
        await page.keyboard.press('Enter');

        // Chờ trang chuyển hoặc trường password xuất hiện
        try {
            await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 30000 });
            console.log('Đã chuyển sang trang nhập mật khẩu.');
        } catch (navError) {
            console.log('Không có navigation, có thể trường password đã xuất hiện trên cùng trang.');
            await sleep(2000);
        }

        // Bước 5: Chờ và nhập mật khẩu
        console.log('Chờ input mật khẩu xuất hiện và nhập mật khẩu...');
        await sleep(3000);

        // Thử nhiều selector khác nhau cho trường password
        let passwordInputSelector = null;
        const possiblePasswordSelectors = [
            'input[type="password"]',
            'input[name="password"]',
            'input[autocomplete="current-password"]',
            'input[placeholder*="password"]',
            'input[placeholder*="Password"]',
            'input[placeholder="Enter password"]',
            'input.bCVoGQ',
            'input[id*=":r"]',
            'input[placeholder*="mật khẩu"]',
            'input[autocomplete="password"]',
            'input[id*="password"]'
        ];

        console.log('Đang tìm trường password...');
        for (const selector of possiblePasswordSelectors) {
            try {
                console.log(`Thử selector: ${selector}`);
                await page.waitForSelector(selector, { visible: true, timeout: 8000 });
                passwordInputSelector = selector;
                console.log(`✓ Tìm thấy trường password với selector: ${selector}`);
                break;
            } catch (e) {
                console.log(`✗ Không tìm thấy với selector: ${selector}`);
            }
        }

        if (!passwordInputSelector) {
            throw new Error('Không thể tìm thấy trường input password với bất kỳ phương pháp nào.');
        }

        await page.type(passwordInputSelector, process.env.CANVA_PASSWORD, { delay: 20 });
        await sleep(300);

        // Bước 6: Nhấn Enter để đăng nhập
        console.log('Nhấn Enter để đăng nhập...');
        await page.keyboard.press('Enter');

        // Chờ đăng nhập thành công
        try {
            await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 60000 });
            console.log('Đăng nhập thành công!');
        } catch (loginError) {
            console.log('Chờ thêm thời gian để đăng nhập hoàn tất...');
            await sleep(5000);
        }

        // Bước 7: Điều hướng đến trang quản lý thành viên
        console.log('Điều hướng đến trang quản lý thành viên Canva (settings/people)...');
        await page.goto('https://www.canva.com/settings/people', { waitUntil: 'networkidle0', timeout: 60000 });
        await sleep(5000);

        // Sử dụng hàm click cụ thể cho nút "Mời thành viên"
        let isInviteButtonReady = await clickInviteButton(page);

        // Nếu không tìm thấy bằng selector cụ thể, thử các text khác
        if (!isInviteButtonReady) {
            const possibleInviteTexts = ['Invite members', 'Add members', 'Invite people'];
            for (const inviteText of possibleInviteTexts) {
                console.log(`Thử tìm nút với text: "${inviteText}"`);
                isInviteButtonReady = await clickElementByText(page, inviteText, '*', 5000);
                if (isInviteButtonReady) {
                    console.log(`✓ Tìm thấy nút mời với text: "${inviteText}"`);
                    break;
                }
            }
        }

        // Đóng popup nếu đã mở
        if (isInviteButtonReady) {
            await page.keyboard.press('Escape');
            await sleep(1000);
        }

        if (!isInviteButtonReady) {
            console.log('Không tìm thấy nút mời thành viên, nhưng vẫn tiếp tục...');
        } else {
            console.log('Đã đến trang quản lý thành viên và tìm thấy nút mời.');
        }

    } catch (error) {
        console.error('Lỗi trong quá trình đăng nhập:', error.message);
        throw error;
    }
}

// API endpoint để thêm thành viên
app.post('/add-member', async (req, res) => {
    try {
        const { email } = req.body;

        if (!email) {
            return res.status(400).json({
                success: false,
                message: 'Email là bắt buộc'
            });
        }

        console.log(`Đang cố gắng thêm thành viên: ${email}`);

        if (!currentPage || !currentBrowser) {
            return res.status(500).json({
                success: false,
                message: 'Trình duyệt chưa được khởi tạo'
            });
        }

        // Điều hướng về trang quản lý thành viên
        console.log('Điều hướng về trang quản lý thành viên...');
        await currentPage.goto('https://www.canva.com/settings/people', { waitUntil: 'networkidle0', timeout: 30000 });
        await sleep(2000);

        // Tìm và click nút mời thành viên
        console.log('Tìm và click nút mời thành viên...');
        const inviteClicked = await clickInviteButton(currentPage);

        if (!inviteClicked) {
            return res.status(500).json({
                success: false,
                message: 'Không thể tìm thấy nút mời thành viên'
            });
        }

        await sleep(1000);

        // Click "Mời qua email"
        console.log('Click "Mời qua email"...');
        const emailTabClicked = await clickInviteByEmailButton(currentPage);

        if (!emailTabClicked) {
            return res.status(500).json({
                success: false,
                message: 'Không thể tìm thấy tab "Mời qua email"'
            });
        }

        await sleep(1000);

        // Nhập email
        console.log(`Nhập email: ${email}`);
        const emailEntered = await enterEmailInInviteForm(currentPage, email);

        if (!emailEntered) {
            return res.status(500).json({
                success: false,
                message: 'Không thể nhập email vào form'
            });
        }

        await sleep(1000);

        // Click nút "Gửi lời mời"
        console.log('Click nút "Gửi lời mời"...');
        const inviteSent = await clickSendInviteButton(currentPage);

        if (!inviteSent) {
            return res.status(500).json({
                success: false,
                message: 'Không thể gửi lời mời'
            });
        }

        await sleep(2000);

        console.log(`Đã gửi lời mời thành công đến ${email}`);

        res.json({
            success: true,
            message: `Đã gửi lời mời thành công đến ${email}`
        });

        console.log('Chuẩn bị sẵn sàng cho request tiếp theo...');

    } catch (error) {
        console.error('Lỗi khi thêm thành viên:', error.message);
        res.status(500).json({
            success: false,
            message: `Lỗi: ${error.message}`
        });
    }
});

// Hàm nhập email vào form mời
async function enterEmailInInviteForm(page, email) {
    console.log(`Tìm input field để nhập email: ${email}`);

    const emailInputSelectors = [
        'input[type="email"]',
        'input[placeholder*="email"]',
        'input[placeholder*="Email"]',
        'input[name="email"]',
        'input[aria-label*="email"]',
        'textarea[placeholder*="email"]',
        'input[data-testid*="email"]'
    ];

    for (const selector of emailInputSelectors) {
        try {
            await page.waitForSelector(selector, { visible: true, timeout: 3000 });
            await page.click(selector);
            await page.keyboard.down('Control');
            await page.keyboard.press('KeyA');
            await page.keyboard.up('Control');
            await page.type(selector, email, { delay: 50 });
            console.log('✓ Đã nhập email thành công');
            return true;
        } catch (error) {
            console.log(`Không tìm thấy input với selector: ${selector}`);
        }
    }

    return false;
}

// Khởi tạo ứng dụng
async function initializeApp() {
    try {
        console.log('Bắt đầu khởi tạo trình duyệt và đăng nhập Canva...');
        await setupBrowserAndLogin();
        console.log('Ứng dụng đã sẵn sàng!');
    } catch (error) {
        console.error('Ứng dụng không thể khởi động do lỗi đăng nhập trình duyệt ban đầu:', error.message);
        process.exit(1);
    }
}

// Khởi động server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server đang lắng nghe tại http://localhost:${PORT}`);
    initializeApp();
});

// Xử lý tắt ứng dụng
process.on('SIGINT', async () => {
    console.log('Đang tắt ứng dụng...');
    await closeBrowser();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('Đang tắt ứng dụng...');
    await closeBrowser();
    process.exit(0);
});
