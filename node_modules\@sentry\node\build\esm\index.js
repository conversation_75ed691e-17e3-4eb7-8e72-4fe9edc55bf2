import * as exports from './logs/exports.js';
export { exports as logger };
export { httpIntegration } from './integrations/http/index.js';
export { nativeNodeFetchIntegration } from './integrations/node-fetch/index.js';
export { fsIntegration } from './integrations/fs.js';
export { nodeContextIntegration } from './integrations/context.js';
export { contextLinesIntegration } from './integrations/contextlines.js';
export { localVariablesIntegration } from './integrations/local-variables/index.js';
export { modulesIntegration } from './integrations/modules.js';
export { onUncaughtExceptionIntegration } from './integrations/onuncaughtexception.js';
export { onUnhandledRejectionIntegration } from './integrations/onunhandledrejection.js';
export { anrIntegration, disableAnrDetectionForCallback } from './integrations/anr/index.js';
export { expressErrorHandler, expressIntegration, setupExpressErrorHandler } from './integrations/tracing/express.js';
export { fastifyIntegration, setupFastifyErrorHandler } from './integrations/tracing/fastify/index.js';
export { graphqlIntegration } from './integrations/tracing/graphql.js';
export { kafkaIntegration } from './integrations/tracing/kafka.js';
export { lruMemoizerIntegration } from './integrations/tracing/lrumemoizer.js';
export { mongoIntegration } from './integrations/tracing/mongo.js';
export { mongooseIntegration } from './integrations/tracing/mongoose.js';
export { mysqlIntegration } from './integrations/tracing/mysql.js';
export { mysql2Integration } from './integrations/tracing/mysql2.js';
export { redisIntegration } from './integrations/tracing/redis.js';
export { postgresIntegration } from './integrations/tracing/postgres.js';
export { postgresJsIntegration } from './integrations/tracing/postgresjs.js';
export { prismaIntegration } from './integrations/tracing/prisma.js';
export { hapiIntegration, setupHapiErrorHandler } from './integrations/tracing/hapi/index.js';
export { koaIntegration, setupKoaErrorHandler } from './integrations/tracing/koa.js';
export { connectIntegration, setupConnectErrorHandler } from './integrations/tracing/connect.js';
export { spotlightIntegration } from './integrations/spotlight.js';
export { knexIntegration } from './integrations/tracing/knex.js';
export { tediousIntegration } from './integrations/tracing/tedious.js';
export { genericPoolIntegration } from './integrations/tracing/genericPool.js';
export { dataloaderIntegration } from './integrations/tracing/dataloader.js';
export { amqplibIntegration } from './integrations/tracing/amqplib.js';
export { vercelAIIntegration } from './integrations/tracing/vercelai/index.js';
export { childProcessIntegration } from './integrations/childProcess.js';
export { createSentryWinstonTransport } from './integrations/winston.js';
export { buildLaunchDarklyFlagUsedHandlerShim as buildLaunchDarklyFlagUsedHandler, launchDarklyIntegrationShim as launchDarklyIntegration } from './integrations/featureFlagShims/launchDarkly.js';
export { OpenFeatureIntegrationHookShim as OpenFeatureIntegrationHook, openFeatureIntegrationShim as openFeatureIntegration } from './integrations/featureFlagShims/openFeature.js';
export { statsigIntegrationShim as statsigIntegration } from './integrations/featureFlagShims/statsig.js';
export { unleashIntegrationShim as unleashIntegration } from './integrations/featureFlagShims/unleash.js';
export { SentryContextManager } from './otel/contextManager.js';
export { generateInstrumentOnce } from './otel/instrument.js';
export { getDefaultIntegrations, getDefaultIntegrationsWithoutPerformance, init, initWithoutDefaultIntegrations, validateOpenTelemetrySetup } from './sdk/index.js';
export { initOpenTelemetry, preloadOpenTelemetry } from './sdk/initOtel.js';
export { getAutoPerformanceIntegrations } from './integrations/tracing/index.js';
export { defaultStackParser, getSentryRelease } from './sdk/api.js';
export { createGetModuleFromFilename } from './utils/module.js';
export { makeNodeTransport } from './transports/http.js';
export { NodeClient } from './sdk/client.js';
export { cron } from './cron/index.js';
export { NODE_VERSION } from './nodeVersion.js';
export { setOpenTelemetryContextAsyncContextStrategy as setNodeAsyncContextStrategy } from '@sentry/opentelemetry';
export { SDK_VERSION, SEMANTIC_ATTRIBUTE_SENTRY_OP, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, Scope, addBreadcrumb, addEventProcessor, addIntegration, captureCheckIn, captureConsoleIntegration, captureEvent, captureException, captureFeedback, captureMessage, captureSession, close, consoleIntegration, consoleLoggingIntegration, continueTrace, createTransport, dedupeIntegration, endSession, eventFiltersIntegration, extraErrorDataIntegration, featureFlagsIntegration, flush, functionToStringIntegration, getActiveSpan, getClient, getCurrentScope, getGlobalScope, getIsolationScope, getRootSpan, getSpanDescendants, getSpanStatusFromHttpCode, getTraceData, getTraceMetaTags, inboundFiltersIntegration, instrumentSupabaseClient, isEnabled, isInitialized, lastEventId, linkedErrorsIntegration, parameterize, profiler, requestDataIntegration, rewriteFramesIntegration, setContext, setCurrentClient, setExtra, setExtras, setHttpStatus, setMeasurement, setTag, setTags, setUser, spanToBaggageHeader, spanToJSON, spanToTraceHeader, startInactiveSpan, startNewTrace, startSession, startSpan, startSpanManual, supabaseIntegration, suppressTracing, trpcMiddleware, updateSpanName, withActiveSpan, withIsolationScope, withMonitor, withScope, wrapMcpServerWithSentry, zodErrorsIntegration } from '@sentry/core';
//# sourceMappingURL=index.js.map
