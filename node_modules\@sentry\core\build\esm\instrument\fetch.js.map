{"version": 3, "file": "fetch.js", "sources": ["../../../src/instrument/fetch.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport type { HandlerDataFetch } from '../types-hoist/instrument';\nimport type { WebFetchHeaders } from '../types-hoist/webfetchapi';\nimport { isError, isRequest } from '../utils/is';\nimport { addNonEnumerableProperty, fill } from '../utils/object';\nimport { supportsNativeFetch } from '../utils/supports';\nimport { timestampInSeconds } from '../utils/time';\nimport { GLOBAL_OBJ } from '../utils/worldwide';\nimport { addHandler, maybeInstrument, triggerHandlers } from './handlers';\n\ntype FetchResource = string | { toString(): string } | { url: string };\n\n/**\n * Add an instrumentation handler for when a fetch request happens.\n * The handler function is called once when the request starts and once when it ends,\n * which can be identified by checking if it has an `endTimestamp`.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addFetchInstrumentationHandler(\n  handler: (data: HandlerDataFetch) => void,\n  skipNativeFetchCheck?: boolean,\n): void {\n  const type = 'fetch';\n  addHandler(type, handler);\n  maybeInstrument(type, () => instrumentFetch(undefined, skipNativeFetchCheck));\n}\n\n/**\n * Add an instrumentation handler for long-lived fetch requests, like consuming server-sent events (SSE) via fetch.\n * The handler will resolve the request body and emit the actual `endTimestamp`, so that the\n * span can be updated accordingly.\n *\n * Only used internally\n * @hidden\n */\nexport function addFetchEndInstrumentationHandler(handler: (data: HandlerDataFetch) => void): void {\n  const type = 'fetch-body-resolved';\n  addHandler(type, handler);\n  maybeInstrument(type, () => instrumentFetch(streamHandler));\n}\n\nfunction instrumentFetch(onFetchResolved?: (response: Response) => void, skipNativeFetchCheck: boolean = false): void {\n  if (skipNativeFetchCheck && !supportsNativeFetch()) {\n    return;\n  }\n\n  fill(GLOBAL_OBJ, 'fetch', function (originalFetch: () => void): () => void {\n    return function (...args: any[]): void {\n      // We capture the error right here and not in the Promise error callback because Safari (and probably other\n      // browsers too) will wipe the stack trace up to this point, only leaving us with this file which is useless.\n\n      // NOTE: If you are a Sentry user, and you are seeing this stack frame,\n      //       it means the error, that was caused by your fetch call did not\n      //       have a stack trace, so the SDK backfilled the stack trace so\n      //       you can see which fetch call failed.\n      const virtualError = new Error();\n\n      const { method, url } = parseFetchArgs(args);\n      const handlerData: HandlerDataFetch = {\n        args,\n        fetchData: {\n          method,\n          url,\n        },\n        startTimestamp: timestampInSeconds() * 1000,\n        // // Adding the error to be able to fingerprint the failed fetch event in HttpClient instrumentation\n        virtualError,\n        headers: getHeadersFromFetchArgs(args),\n      };\n\n      // if there is no callback, fetch is instrumented directly\n      if (!onFetchResolved) {\n        triggerHandlers('fetch', {\n          ...handlerData,\n        });\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      return originalFetch.apply(GLOBAL_OBJ, args).then(\n        async (response: Response) => {\n          if (onFetchResolved) {\n            onFetchResolved(response);\n          } else {\n            triggerHandlers('fetch', {\n              ...handlerData,\n              endTimestamp: timestampInSeconds() * 1000,\n              response,\n            });\n          }\n\n          return response;\n        },\n        (error: Error) => {\n          triggerHandlers('fetch', {\n            ...handlerData,\n            endTimestamp: timestampInSeconds() * 1000,\n            error,\n          });\n\n          if (isError(error) && error.stack === undefined) {\n            // NOTE: If you are a Sentry user, and you are seeing this stack frame,\n            //       it means the error, that was caused by your fetch call did not\n            //       have a stack trace, so the SDK backfilled the stack trace so\n            //       you can see which fetch call failed.\n            error.stack = virtualError.stack;\n            addNonEnumerableProperty(error, 'framesToPop', 1);\n          }\n\n          // We enhance the not-so-helpful \"Failed to fetch\" error messages with the host\n          // Possible messages we handle here:\n          // * \"Failed to fetch\" (chromium)\n          // * \"Load failed\" (webkit)\n          // * \"NetworkError when attempting to fetch resource.\" (firefox)\n          if (\n            error instanceof TypeError &&\n            (error.message === 'Failed to fetch' ||\n              error.message === 'Load failed' ||\n              error.message === 'NetworkError when attempting to fetch resource.')\n          ) {\n            try {\n              const url = new URL(handlerData.fetchData.url);\n              error.message = `${error.message} (${url.host})`;\n            } catch {\n              // ignore it if errors happen here\n            }\n          }\n\n          // NOTE: If you are a Sentry user, and you are seeing this stack frame,\n          //       it means the sentry.javascript SDK caught an error invoking your application code.\n          //       This is expected behavior and NOT indicative of a bug with sentry.javascript.\n          throw error;\n        },\n      );\n    };\n  });\n}\n\nasync function resolveResponse(res: Response | undefined, onFinishedResolving: () => void): Promise<void> {\n  if (res?.body) {\n    const body = res.body;\n    const responseReader = body.getReader();\n\n    // Define a maximum duration after which we just cancel\n    const maxFetchDurationTimeout = setTimeout(\n      () => {\n        body.cancel().then(null, () => {\n          // noop\n        });\n      },\n      90 * 1000, // 90s\n    );\n\n    let readingActive = true;\n    while (readingActive) {\n      let chunkTimeout;\n      try {\n        // abort reading if read op takes more than 5s\n        chunkTimeout = setTimeout(() => {\n          body.cancel().then(null, () => {\n            // noop on error\n          });\n        }, 5000);\n\n        // This .read() call will reject/throw when we abort due to timeouts through `body.cancel()`\n        const { done } = await responseReader.read();\n\n        clearTimeout(chunkTimeout);\n\n        if (done) {\n          onFinishedResolving();\n          readingActive = false;\n        }\n      } catch (error) {\n        readingActive = false;\n      } finally {\n        clearTimeout(chunkTimeout);\n      }\n    }\n\n    clearTimeout(maxFetchDurationTimeout);\n\n    responseReader.releaseLock();\n    body.cancel().then(null, () => {\n      // noop on error\n    });\n  }\n}\n\nfunction streamHandler(response: Response): void {\n  // clone response for awaiting stream\n  let clonedResponseForResolving: Response;\n  try {\n    clonedResponseForResolving = response.clone();\n  } catch {\n    return;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  resolveResponse(clonedResponseForResolving, () => {\n    triggerHandlers('fetch-body-resolved', {\n      endTimestamp: timestampInSeconds() * 1000,\n      response,\n    });\n  });\n}\n\nfunction hasProp<T extends string>(obj: unknown, prop: T): obj is Record<string, string> {\n  return !!obj && typeof obj === 'object' && !!(obj as Record<string, string>)[prop];\n}\n\nfunction getUrlFromResource(resource: FetchResource): string {\n  if (typeof resource === 'string') {\n    return resource;\n  }\n\n  if (!resource) {\n    return '';\n  }\n\n  if (hasProp(resource, 'url')) {\n    return resource.url;\n  }\n\n  if (resource.toString) {\n    return resource.toString();\n  }\n\n  return '';\n}\n\n/**\n * Parses the fetch arguments to find the used Http method and the url of the request.\n * Exported for tests only.\n */\nexport function parseFetchArgs(fetchArgs: unknown[]): { method: string; url: string } {\n  if (fetchArgs.length === 0) {\n    return { method: 'GET', url: '' };\n  }\n\n  if (fetchArgs.length === 2) {\n    const [url, options] = fetchArgs as [FetchResource, object];\n\n    return {\n      url: getUrlFromResource(url),\n      method: hasProp(options, 'method') ? String(options.method).toUpperCase() : 'GET',\n    };\n  }\n\n  const arg = fetchArgs[0];\n  return {\n    url: getUrlFromResource(arg as FetchResource),\n    method: hasProp(arg, 'method') ? String(arg.method).toUpperCase() : 'GET',\n  };\n}\n\nfunction getHeadersFromFetchArgs(fetchArgs: unknown[]): WebFetchHeaders | undefined {\n  const [requestArgument, optionsArgument] = fetchArgs;\n\n  try {\n    if (\n      typeof optionsArgument === 'object' &&\n      optionsArgument !== null &&\n      'headers' in optionsArgument &&\n      optionsArgument.headers\n    ) {\n      return new Headers(optionsArgument.headers as any);\n    }\n\n    if (isRequest(requestArgument)) {\n      return new Headers(requestArgument.headers);\n    }\n  } catch {\n    // noop\n  }\n\n  return;\n}\n"], "names": [], "mappings": ";;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,8BAA8B;AAC9C,EAAE,OAAO;AACT,EAAE,oBAAoB;AACtB,EAAQ;AACR,EAAE,MAAM,IAAK,GAAE,OAAO;AACtB,EAAE,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;AAC3B,EAAE,eAAe,CAAC,IAAI,EAAE,MAAM,eAAe,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iCAAiC,CAAC,OAAO,EAA0C;AACnG,EAAE,MAAM,IAAK,GAAE,qBAAqB;AACpC,EAAE,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;AAC3B,EAAE,eAAe,CAAC,IAAI,EAAE,MAAM,eAAe,CAAC,aAAa,CAAC,CAAC;AAC7D;;AAEA,SAAS,eAAe,CAAC,eAAe,EAAiC,oBAAoB,GAAY,KAAK,EAAQ;AACtH,EAAE,IAAI,oBAAqB,IAAG,CAAC,mBAAmB,EAAE,EAAE;AACtD,IAAI;AACJ;;AAEA,EAAE,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,aAAa,EAA0B;AAC7E,IAAI,OAAO,UAAU,GAAG,IAAI,EAAe;AAC3C;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM,MAAM,YAAa,GAAE,IAAI,KAAK,EAAE;;AAEtC,MAAM,MAAM,EAAE,MAAM,EAAE,GAAA,KAAQ,cAAc,CAAC,IAAI,CAAC;AAClD,MAAM,MAAM,WAAW,GAAqB;AAC5C,QAAQ,IAAI;AACZ,QAAQ,SAAS,EAAE;AACnB,UAAU,MAAM;AAChB,UAAU,GAAG;AACb,SAAS;AACT,QAAQ,cAAc,EAAE,kBAAkB,EAAC,GAAI,IAAI;AACnD;AACA,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE,uBAAuB,CAAC,IAAI,CAAC;AAC9C,OAAO;;AAEP;AACA,MAAM,IAAI,CAAC,eAAe,EAAE;AAC5B,QAAQ,eAAe,CAAC,OAAO,EAAE;AACjC,UAAU,GAAG,WAAW;AACxB,SAAS,CAAC;AACV;;AAEA;AACA,MAAM,OAAO,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,IAAI;AACvD,QAAQ,OAAO,QAAQ,KAAe;AACtC,UAAU,IAAI,eAAe,EAAE;AAC/B,YAAY,eAAe,CAAC,QAAQ,CAAC;AACrC,iBAAiB;AACjB,YAAY,eAAe,CAAC,OAAO,EAAE;AACrC,cAAc,GAAG,WAAW;AAC5B,cAAc,YAAY,EAAE,kBAAkB,EAAC,GAAI,IAAI;AACvD,cAAc,QAAQ;AACtB,aAAa,CAAC;AACd;;AAEA,UAAU,OAAO,QAAQ;AACzB,SAAS;AACT,QAAQ,CAAC,KAAK,KAAY;AAC1B,UAAU,eAAe,CAAC,OAAO,EAAE;AACnC,YAAY,GAAG,WAAW;AAC1B,YAAY,YAAY,EAAE,kBAAkB,EAAC,GAAI,IAAI;AACrD,YAAY,KAAK;AACjB,WAAW,CAAC;;AAEZ,UAAU,IAAI,OAAO,CAAC,KAAK,CAAA,IAAK,KAAK,CAAC,KAAA,KAAU,SAAS,EAAE;AAC3D;AACA;AACA;AACA;AACA,YAAY,KAAK,CAAC,KAAA,GAAQ,YAAY,CAAC,KAAK;AAC5C,YAAY,wBAAwB,CAAC,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;AAC7D;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV,YAAY,KAAA,YAAiB,SAAU;AACvC,aAAa,KAAK,CAAC,OAAA,KAAY,iBAAkB;AACjD,cAAc,KAAK,CAAC,OAAQ,KAAI,aAAc;AAC9C,cAAc,KAAK,CAAC,OAAQ,KAAI,iDAAiD;AACjF,YAAY;AACZ,YAAY,IAAI;AAChB,cAAc,MAAM,GAAI,GAAE,IAAI,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC;AAC5D,cAAc,KAAK,CAAC,OAAQ,GAAE,CAAC,EAAA,KAAA,CAAA,OAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA,aAAA,CAAA,MAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAA,MAAA,KAAA;AACA,SAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA,CAAA;AACA;;AAEA,eAAA,eAAA,CAAA,GAAA,EAAA,mBAAA,EAAA;AACA,EAAA,IAAA,GAAA,EAAA,IAAA,EAAA;AACA,IAAA,MAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,IAAA,MAAA,cAAA,GAAA,IAAA,CAAA,SAAA,EAAA;;AAEA;AACA,IAAA,MAAA,uBAAA,GAAA,UAAA;AACA,MAAA,MAAA;AACA,QAAA,IAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA,MAAA;AACA;AACA,SAAA,CAAA;AACA,OAAA;AACA,MAAA,EAAA,GAAA,IAAA;AACA,KAAA;;AAEA,IAAA,IAAA,aAAA,GAAA,IAAA;AACA,IAAA,OAAA,aAAA,EAAA;AACA,MAAA,IAAA,YAAA;AACA,MAAA,IAAA;AACA;AACA,QAAA,YAAA,GAAA,UAAA,CAAA,MAAA;AACA,UAAA,IAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA,MAAA;AACA;AACA,WAAA,CAAA;AACA,SAAA,EAAA,IAAA,CAAA;;AAEA;AACA,QAAA,MAAA,EAAA,IAAA,EAAA,GAAA,MAAA,cAAA,CAAA,IAAA,EAAA;;AAEA,QAAA,YAAA,CAAA,YAAA,CAAA;;AAEA,QAAA,IAAA,IAAA,EAAA;AACA,UAAA,mBAAA,EAAA;AACA,UAAA,aAAA,GAAA,KAAA;AACA;AACA,OAAA,CAAA,OAAA,KAAA,EAAA;AACA,QAAA,aAAA,GAAA,KAAA;AACA,OAAA,SAAA;AACA,QAAA,YAAA,CAAA,YAAA,CAAA;AACA;AACA;;AAEA,IAAA,YAAA,CAAA,uBAAA,CAAA;;AAEA,IAAA,cAAA,CAAA,WAAA,EAAA;AACA,IAAA,IAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA,MAAA;AACA;AACA,KAAA,CAAA;AACA;AACA;;AAEA,SAAA,aAAA,CAAA,QAAA,EAAA;AACA;AACA,EAAA,IAAA,0BAAA;AACA,EAAA,IAAA;AACA,IAAA,0BAAA,GAAA,QAAA,CAAA,KAAA,EAAA;AACA,GAAA,CAAA,MAAA;AACA,IAAA;AACA;;AAEA;AACA,EAAA,eAAA,CAAA,0BAAA,EAAA,MAAA;AACA,IAAA,eAAA,CAAA,qBAAA,EAAA;AACA,MAAA,YAAA,EAAA,kBAAA,EAAA,GAAA,IAAA;AACA,MAAA,QAAA;AACA,KAAA,CAAA;AACA,GAAA,CAAA;AACA;;AAEA,SAAA,OAAA,CAAA,GAAA,EAAA,IAAA,EAAA;AACA,EAAA,OAAA,CAAA,CAAA,GAAA,IAAA,OAAA,GAAA,KAAA,QAAA,IAAA,CAAA,CAAA,CAAA,GAAA,GAAA,IAAA,CAAA;AACA;;AAEA,SAAA,kBAAA,CAAA,QAAA,EAAA;AACA,EAAA,IAAA,OAAA,QAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA,QAAA;AACA;;AAEA,EAAA,IAAA,CAAA,QAAA,EAAA;AACA,IAAA,OAAA,EAAA;AACA;;AAEA,EAAA,IAAA,OAAA,CAAA,QAAA,EAAA,KAAA,CAAA,EAAA;AACA,IAAA,OAAA,QAAA,CAAA,GAAA;AACA;;AAEA,EAAA,IAAA,QAAA,CAAA,QAAA,EAAA;AACA,IAAA,OAAA,QAAA,CAAA,QAAA,EAAA;AACA;;AAEA,EAAA,OAAA,EAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAA,cAAA,CAAA,SAAA,EAAA;AACA,EAAA,IAAA,SAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,IAAA,OAAA,EAAA,MAAA,EAAA,KAAA,EAAA,GAAA,EAAA,EAAA,EAAA;AACA;;AAEA,EAAA,IAAA,SAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,IAAA,MAAA,CAAA,GAAA,EAAA,OAAA,CAAA,GAAA,SAAA;;AAEA,IAAA,OAAA;AACA,MAAA,GAAA,EAAA,kBAAA,CAAA,GAAA,CAAA;AACA,MAAA,MAAA,EAAA,OAAA,CAAA,OAAA,EAAA,QAAA,CAAA,GAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,WAAA,EAAA,GAAA,KAAA;AACA,KAAA;AACA;;AAEA,EAAA,MAAA,GAAA,GAAA,SAAA,CAAA,CAAA,CAAA;AACA,EAAA,OAAA;AACA,IAAA,GAAA,EAAA,kBAAA,CAAA,GAAA,EAAA;AACA,IAAA,MAAA,EAAA,OAAA,CAAA,GAAA,EAAA,QAAA,CAAA,GAAA,MAAA,CAAA,GAAA,CAAA,MAAA,CAAA,CAAA,WAAA,EAAA,GAAA,KAAA;AACA,GAAA;AACA;;AAEA,SAAA,uBAAA,CAAA,SAAA,EAAA;AACA,EAAA,MAAA,CAAA,eAAA,EAAA,eAAA,CAAA,GAAA,SAAA;;AAEA,EAAA,IAAA;AACA,IAAA;AACA,MAAA,OAAA,eAAA,KAAA,QAAA;AACA,MAAA,eAAA,KAAA,IAAA;AACA,MAAA,SAAA,IAAA,eAAA;AACA,MAAA,eAAA,CAAA;AACA,MAAA;AACA,MAAA,OAAA,IAAA,OAAA,CAAA,eAAA,CAAA,OAAA,EAAA;AACA;;AAEA,IAAA,IAAA,SAAA,CAAA,eAAA,CAAA,EAAA;AACA,MAAA,OAAA,IAAA,OAAA,CAAA,eAAA,CAAA,OAAA,CAAA;AACA;AACA,GAAA,CAAA,MAAA;AACA;AACA;;AAEA,EAAA;AACA;;;;"}