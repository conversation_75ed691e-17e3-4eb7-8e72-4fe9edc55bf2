{"version": 3, "file": "launchDarkly.js", "sources": ["../../../../src/integrations/featureFlagShims/launchDarkly.ts"], "sourcesContent": ["import { consoleSandbox, defineIntegration, isBrowser } from '@sentry/core';\n\n/**\n * This is a shim for the LaunchDarkly integration.\n * We need this in order to not throw runtime errors when accidentally importing this on the server through a meta framework like Next.js.\n */\nexport const launchDarklyIntegrationShim = defineIntegration((_options?: unknown) => {\n  if (!isBrowser()) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn('The launchDarklyIntegration() can only be used in the browser.');\n    });\n  }\n\n  return {\n    name: 'LaunchDarkly',\n  };\n});\n\n/**\n * This is a shim for the LaunchDarkly flag used handler.\n */\nexport function buildLaunchDarklyFlagUsedHandlerShim(): unknown {\n  if (!isBrowser()) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn('The buildLaunchDarklyFlagUsedHandler() can only be used in the browser.');\n    });\n  }\n\n  return {\n    name: 'sentry-flag-auditor',\n    type: 'flag-used',\n    synchronous: true,\n    method: () => null,\n  };\n}\n"], "names": ["defineIntegration", "<PERSON><PERSON><PERSON><PERSON>", "consoleSandbox"], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACO,MAAM,8BAA8BA,sBAAiB,CAAC,CAAC,QAAQ,KAAe;AACrF,EAAE,IAAI,CAACC,cAAS,EAAE,EAAE;AACpB,IAAIC,mBAAc,CAAC,MAAM;AACzB;AACA,MAAM,OAAO,CAAC,IAAI,CAAC,gEAAgE,CAAC;AACpF,KAAK,CAAC;AACN;;AAEA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,cAAc;AACxB,GAAG;AACH,CAAC;;AAED;AACA;AACA;AACO,SAAS,oCAAoC,GAAY;AAChE,EAAE,IAAI,CAACD,cAAS,EAAE,EAAE;AACpB,IAAIC,mBAAc,CAAC,MAAM;AACzB;AACA,MAAM,OAAO,CAAC,IAAI,CAAC,yEAAyE,CAAC;AAC7F,KAAK,CAAC;AACN;;AAEA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,WAAW,EAAE,IAAI;AACrB,IAAI,MAAM,EAAE,MAAM,IAAI;AACtB,GAAG;AACH;;;;;"}