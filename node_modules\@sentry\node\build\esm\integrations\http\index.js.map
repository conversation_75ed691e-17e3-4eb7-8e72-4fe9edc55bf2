{"version": 3, "file": "index.js", "sources": ["../../../../src/integrations/http/index.ts"], "sourcesContent": ["import type { ClientRequest, IncomingMessage, RequestOptions, ServerResponse } from 'node:http';\nimport { diag } from '@opentelemetry/api';\nimport type { HttpInstrumentationConfig } from '@opentelemetry/instrumentation-http';\nimport { HttpInstrumentation } from '@opentelemetry/instrumentation-http';\nimport type { Span } from '@sentry/core';\nimport { defineIntegration, getClient, hasSpansEnabled } from '@sentry/core';\nimport { NODE_VERSION } from '../../nodeVersion';\nimport { generateInstrumentOnce } from '../../otel/instrument';\nimport type { NodeClient } from '../../sdk/client';\nimport type { HTTPModuleRequestIncomingMessage } from '../../transports/http-module';\nimport type { NodeClientOptions } from '../../types';\nimport { addOriginToSpan } from '../../utils/addOriginToSpan';\nimport { getRequestUrl } from '../../utils/getRequestUrl';\nimport type { SentryHttpInstrumentationOptions } from './SentryHttpInstrumentation';\nimport { SentryHttpInstrumentation } from './SentryHttpInstrumentation';\n\nconst INTEGRATION_NAME = 'Http';\n\nconst INSTRUMENTATION_NAME = '@opentelemetry_sentry-patched/instrumentation-http';\n\ninterface HttpOptions {\n  /**\n   * Whether breadcrumbs should be recorded for outgoing requests.\n   * Defaults to true\n   */\n  breadcrumbs?: boolean;\n\n  /**\n   * If set to false, do not emit any spans.\n   * This will ensure that the default HttpInstrumentation from OpenTelemetry is not setup,\n   * only the Sentry-specific instrumentation for request isolation is applied.\n   *\n   * If `skipOpenTelemetrySetup: true` is configured, this defaults to `false`, otherwise it defaults to `true`.\n   */\n  spans?: boolean;\n\n  /**\n   * Whether the integration should create [Sessions](https://docs.sentry.io/product/releases/health/#sessions) for incoming requests to track the health and crash-free rate of your releases in Sentry.\n   * Read more about Release Health: https://docs.sentry.io/product/releases/health/\n   *\n   * Defaults to `true`.\n   */\n  trackIncomingRequestsAsSessions?: boolean;\n\n  /**\n   * Number of milliseconds until sessions tracked with `trackIncomingRequestsAsSessions` will be flushed as a session aggregate.\n   *\n   * Defaults to `60000` (60s).\n   */\n  sessionFlushingDelayMS?: number;\n\n  /**\n   * Do not capture spans or breadcrumbs for outgoing HTTP requests to URLs where the given callback returns `true`.\n   * This controls both span & breadcrumb creation - spans will be non recording if tracing is disabled.\n   *\n   * The `url` param contains the entire URL, including query string (if any), protocol, host, etc. of the outgoing request.\n   * For example: `'https://someService.com/users/details?id=123'`\n   *\n   * The `request` param contains the original {@type RequestOptions} object used to make the outgoing request.\n   * You can use it to filter on additional properties like method, headers, etc.\n   */\n  ignoreOutgoingRequests?: (url: string, request: RequestOptions) => boolean;\n\n  /**\n   * Do not capture spans for incoming HTTP requests to URLs where the given callback returns `true`.\n   * Spans will be non recording if tracing is disabled.\n   *\n   * The `urlPath` param consists of the URL path and query string (if any) of the incoming request.\n   * For example: `'/users/details?id=123'`\n   *\n   * The `request` param contains the original {@type IncomingMessage} object of the incoming request.\n   * You can use it to filter on additional properties like method, headers, etc.\n   */\n  ignoreIncomingRequests?: (urlPath: string, request: IncomingMessage) => boolean;\n\n  /**\n   * Do not capture spans for incoming HTTP requests with the given status codes.\n   * By default, spans with 404 status code are ignored.\n   * Expects an array of status codes or a range of status codes, e.g. [[300,399], 404] would ignore 3xx and 404 status codes.\n   *\n   * @default `[404]`\n   */\n  dropSpansForIncomingRequestStatusCodes?: (number | [number, number])[];\n\n  /**\n   * Do not capture the request body for incoming HTTP requests to URLs where the given callback returns `true`.\n   * This can be useful for long running requests where the body is not needed and we want to avoid capturing it.\n   *\n   * @param url Contains the entire URL, including query string (if any), protocol, host, etc. of the incoming request.\n   * @param request Contains the {@type RequestOptions} object used to make the incoming request.\n   */\n  ignoreIncomingRequestBody?: (url: string, request: RequestOptions) => boolean;\n\n  /**\n   * Controls the maximum size of incoming HTTP request bodies attached to events.\n   *\n   * Available options:\n   * - 'none': No request bodies will be attached\n   * - 'small': Request bodies up to 1,000 bytes will be attached\n   * - 'medium': Request bodies up to 10,000 bytes will be attached (default)\n   * - 'always': Request bodies will always be attached\n   *\n   * Note that even with 'always' setting, bodies exceeding 1MB will never be attached\n   * for performance and security reasons.\n   *\n   * @default 'medium'\n   */\n  maxIncomingRequestBodySize?: 'none' | 'small' | 'medium' | 'always';\n\n  /**\n   * If true, do not generate spans for incoming requests at all.\n   * This is used by Remix to avoid generating spans for incoming requests, as it generates its own spans.\n   */\n  disableIncomingRequestSpans?: boolean;\n\n  /**\n   * Additional instrumentation options that are passed to the underlying HttpInstrumentation.\n   */\n  instrumentation?: {\n    requestHook?: (span: Span, req: ClientRequest | HTTPModuleRequestIncomingMessage) => void;\n    responseHook?: (span: Span, response: HTTPModuleRequestIncomingMessage | ServerResponse) => void;\n    applyCustomAttributesOnSpan?: (\n      span: Span,\n      request: ClientRequest | HTTPModuleRequestIncomingMessage,\n      response: HTTPModuleRequestIncomingMessage | ServerResponse,\n    ) => void;\n\n    /**\n     * You can pass any configuration through to the underlying instrumentation.\n     * Note that there are no semver guarantees for this!\n     */\n    _experimentalConfig?: ConstructorParameters<typeof HttpInstrumentation>[0];\n  };\n}\n\nconst instrumentSentryHttp = generateInstrumentOnce<SentryHttpInstrumentationOptions>(\n  `${INTEGRATION_NAME}.sentry`,\n  options => {\n    return new SentryHttpInstrumentation(options);\n  },\n);\n\nexport const instrumentOtelHttp = generateInstrumentOnce<HttpInstrumentationConfig>(INTEGRATION_NAME, config => {\n  const instrumentation = new HttpInstrumentation(config);\n\n  // We want to update the logger namespace so we can better identify what is happening here\n  try {\n    instrumentation['_diag'] = diag.createComponentLogger({\n      namespace: INSTRUMENTATION_NAME,\n    });\n    // @ts-expect-error We are writing a read-only property here...\n    instrumentation.instrumentationName = INSTRUMENTATION_NAME;\n  } catch {\n    // ignore errors here...\n  }\n\n  return instrumentation;\n});\n\n/** Exported only for tests. */\nexport function _shouldInstrumentSpans(options: HttpOptions, clientOptions: Partial<NodeClientOptions> = {}): boolean {\n  // If `spans` is passed in, it takes precedence\n  // Else, we by default emit spans, unless `skipOpenTelemetrySetup` is set to `true` or spans are not enabled\n  if (typeof options.spans === 'boolean') {\n    return options.spans;\n  }\n\n  if (clientOptions.skipOpenTelemetrySetup) {\n    return false;\n  }\n\n  // IMPORTANT: We only disable span instrumentation when spans are not enabled _and_ we are on Node 22+,\n  // as otherwise the necessary diagnostics channel is not available yet\n  if (!hasSpansEnabled(clientOptions) && NODE_VERSION.major >= 22) {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * The http integration instruments Node's internal http and https modules.\n * It creates breadcrumbs and spans for outgoing HTTP requests which will be attached to the currently active span.\n */\nexport const httpIntegration = defineIntegration((options: HttpOptions = {}) => {\n  const dropSpansForIncomingRequestStatusCodes = options.dropSpansForIncomingRequestStatusCodes ?? [404];\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      const instrumentSpans = _shouldInstrumentSpans(options, getClient<NodeClient>()?.getOptions());\n\n      // This is Sentry-specific instrumentation for request isolation and breadcrumbs\n      instrumentSentryHttp({\n        ...options,\n        // If spans are not instrumented, it means the HttpInstrumentation has not been added\n        // In that case, we want to handle incoming trace extraction ourselves\n        extractIncomingTraceFromHeader: !instrumentSpans,\n        // If spans are not instrumented, it means the HttpInstrumentation has not been added\n        // In that case, we want to handle trace propagation ourselves\n        propagateTraceInOutgoingRequests: !instrumentSpans,\n      });\n\n      // This is the \"regular\" OTEL instrumentation that emits spans\n      if (instrumentSpans) {\n        const instrumentationConfig = getConfigWithDefaults(options);\n        instrumentOtelHttp(instrumentationConfig);\n      }\n    },\n    processEvent(event) {\n      // Drop transaction if it has a status code that should be ignored\n      if (event.type === 'transaction') {\n        const statusCode = event.contexts?.trace?.data?.['http.response.status_code'];\n        if (\n          typeof statusCode === 'number' &&\n          dropSpansForIncomingRequestStatusCodes.some(code => {\n            if (typeof code === 'number') {\n              return code === statusCode;\n            }\n\n            const [min, max] = code;\n            return statusCode >= min && statusCode <= max;\n          })\n        ) {\n          return null;\n        }\n      }\n\n      return event;\n    },\n  };\n});\n\n/**\n * Determines if @param req is a ClientRequest, meaning the request was created within the express app\n * and it's an outgoing request.\n * Checking for properties instead of using `instanceOf` to avoid importing the request classes.\n */\nfunction _isClientRequest(req: ClientRequest | HTTPModuleRequestIncomingMessage): req is ClientRequest {\n  return 'outputData' in req && 'outputSize' in req && !('client' in req) && !('statusCode' in req);\n}\n\n/**\n * Detects if an incoming request is a prefetch request.\n */\nfunction isKnownPrefetchRequest(req: HTTPModuleRequestIncomingMessage): boolean {\n  // Currently only handles Next.js prefetch requests but may check other frameworks in the future.\n  return req.headers['next-router-prefetch'] === '1';\n}\n\nfunction getConfigWithDefaults(options: Partial<HttpOptions> = {}): HttpInstrumentationConfig {\n  const instrumentationConfig = {\n    ...options.instrumentation?._experimentalConfig,\n\n    disableIncomingRequestInstrumentation: options.disableIncomingRequestSpans,\n\n    ignoreOutgoingRequestHook: request => {\n      const url = getRequestUrl(request);\n\n      if (!url) {\n        return false;\n      }\n\n      const _ignoreOutgoingRequests = options.ignoreOutgoingRequests;\n      if (_ignoreOutgoingRequests?.(url, request)) {\n        return true;\n      }\n\n      return false;\n    },\n\n    ignoreIncomingRequestHook: request => {\n      // request.url is the only property that holds any information about the url\n      // it only consists of the URL path and query string (if any)\n      const urlPath = request.url;\n\n      const method = request.method?.toUpperCase();\n      // We do not capture OPTIONS/HEAD requests as transactions\n      if (method === 'OPTIONS' || method === 'HEAD') {\n        return true;\n      }\n\n      const _ignoreIncomingRequests = options.ignoreIncomingRequests;\n      if (urlPath && _ignoreIncomingRequests?.(urlPath, request)) {\n        return true;\n      }\n\n      return false;\n    },\n\n    requireParentforOutgoingSpans: false,\n    requireParentforIncomingSpans: false,\n    requestHook: (span, req) => {\n      addOriginToSpan(span, 'auto.http.otel.http');\n      if (!_isClientRequest(req) && isKnownPrefetchRequest(req)) {\n        span.setAttribute('sentry.http.prefetch', true);\n      }\n\n      options.instrumentation?.requestHook?.(span, req);\n    },\n    responseHook: (span, res) => {\n      options.instrumentation?.responseHook?.(span, res);\n    },\n    applyCustomAttributesOnSpan: (\n      span: Span,\n      request: ClientRequest | HTTPModuleRequestIncomingMessage,\n      response: HTTPModuleRequestIncomingMessage | ServerResponse,\n    ) => {\n      options.instrumentation?.applyCustomAttributesOnSpan?.(span, request, response);\n    },\n  } satisfies HttpInstrumentationConfig;\n\n  return instrumentationConfig;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAgBA,MAAM,gBAAA,GAAmB,MAAM;;AAE/B,MAAM,oBAAA,GAAuB,oDAAoD;;AAqHjF,MAAM,oBAAA,GAAuB,sBAAsB;AACnD,EAAE,CAAC,EAAA,gBAAA,CAAA,OAAA,CAAA;AACA,EAAA,OAAA,IAAA;AACA,IAAA,OAAA,IAAA,yBAAA,CAAA,OAAA,CAAA;AACA,GAAA;AACA,CAAA;;AAEA,MAAA,kBAAA,GAAA,sBAAA,CAAA,gBAAA,EAAA,MAAA,IAAA;AACA,EAAA,MAAA,eAAA,GAAA,IAAA,mBAAA,CAAA,MAAA,CAAA;;AAEA;AACA,EAAA,IAAA;AACA,IAAA,eAAA,CAAA,OAAA,CAAA,GAAA,IAAA,CAAA,qBAAA,CAAA;AACA,MAAA,SAAA,EAAA,oBAAA;AACA,KAAA,CAAA;AACA;AACA,IAAA,eAAA,CAAA,mBAAA,GAAA,oBAAA;AACA,GAAA,CAAA,MAAA;AACA;AACA;;AAEA,EAAA,OAAA,eAAA;AACA,CAAA;;AAEA;AACA,SAAA,sBAAA,CAAA,OAAA,EAAA,aAAA,GAAA,EAAA,EAAA;AACA;AACA;AACA,EAAA,IAAA,OAAA,OAAA,CAAA,KAAA,KAAA,SAAA,EAAA;AACA,IAAA,OAAA,OAAA,CAAA,KAAA;AACA;;AAEA,EAAA,IAAA,aAAA,CAAA,sBAAA,EAAA;AACA,IAAA,OAAA,KAAA;AACA;;AAEA;AACA;AACA,EAAA,IAAA,CAAA,eAAA,CAAA,aAAA,CAAA,IAAA,YAAA,CAAA,KAAA,IAAA,EAAA,EAAA;AACA,IAAA,OAAA,KAAA;AACA;;AAEA,EAAA,OAAA,IAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAA,eAAA,GAAA,iBAAA,CAAA,CAAA,OAAA,GAAA,EAAA,KAAA;AACA,EAAA,MAAA,sCAAA,GAAA,OAAA,CAAA,sCAAA,IAAA,CAAA,GAAA,CAAA;;AAEA,EAAA,OAAA;AACA,IAAA,IAAA,EAAA,gBAAA;AACA,IAAA,SAAA,GAAA;AACA,MAAA,MAAA,eAAA,GAAA,sBAAA,CAAA,OAAA,EAAA,SAAA,EAAA,EAAA,UAAA,EAAA,CAAA;;AAEA;AACA,MAAA,oBAAA,CAAA;AACA,QAAA,GAAA,OAAA;AACA;AACA;AACA,QAAA,8BAAA,EAAA,CAAA,eAAA;AACA;AACA;AACA,QAAA,gCAAA,EAAA,CAAA,eAAA;AACA,OAAA,CAAA;;AAEA;AACA,MAAA,IAAA,eAAA,EAAA;AACA,QAAA,MAAA,qBAAA,GAAA,qBAAA,CAAA,OAAA,CAAA;AACA,QAAA,kBAAA,CAAA,qBAAA,CAAA;AACA;AACA,KAAA;AACA,IAAA,YAAA,CAAA,KAAA,EAAA;AACA;AACA,MAAA,IAAA,KAAA,CAAA,IAAA,KAAA,aAAA,EAAA;AACA,QAAA,MAAA,UAAA,GAAA,KAAA,CAAA,QAAA,EAAA,KAAA,EAAA,IAAA,GAAA,2BAAA,CAAA;AACA,QAAA;AACA,UAAA,OAAA,UAAA,KAAA,QAAA;AACA,UAAA,sCAAA,CAAA,IAAA,CAAA,IAAA,IAAA;AACA,YAAA,IAAA,OAAA,IAAA,KAAA,QAAA,EAAA;AACA,cAAA,OAAA,IAAA,KAAA,UAAA;AACA;;AAEA,YAAA,MAAA,CAAA,GAAA,EAAA,GAAA,CAAA,GAAA,IAAA;AACA,YAAA,OAAA,UAAA,IAAA,GAAA,IAAA,UAAA,IAAA,GAAA;AACA,WAAA;AACA,UAAA;AACA,UAAA,OAAA,IAAA;AACA;AACA;;AAEA,MAAA,OAAA,KAAA;AACA,KAAA;AACA,GAAA;AACA,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAA,gBAAA,CAAA,GAAA,EAAA;AACA,EAAA,OAAA,YAAA,IAAA,GAAA,IAAA,YAAA,IAAA,GAAA,IAAA,EAAA,QAAA,IAAA,GAAA,CAAA,IAAA,EAAA,YAAA,IAAA,GAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA,SAAA,sBAAA,CAAA,GAAA,EAAA;AACA;AACA,EAAA,OAAA,GAAA,CAAA,OAAA,CAAA,sBAAA,CAAA,KAAA,GAAA;AACA;;AAEA,SAAA,qBAAA,CAAA,OAAA,GAAA,EAAA,EAAA;AACA,EAAA,MAAA,qBAAA,GAAA;AACA,IAAA,GAAA,OAAA,CAAA,eAAA,EAAA,mBAAA;;AAEA,IAAA,qCAAA,EAAA,OAAA,CAAA,2BAAA;;AAEA,IAAA,yBAAA,EAAA,OAAA,IAAA;AACA,MAAA,MAAA,GAAA,GAAA,aAAA,CAAA,OAAA,CAAA;;AAEA,MAAA,IAAA,CAAA,GAAA,EAAA;AACA,QAAA,OAAA,KAAA;AACA;;AAEA,MAAA,MAAA,uBAAA,GAAA,OAAA,CAAA,sBAAA;AACA,MAAA,IAAA,uBAAA,GAAA,GAAA,EAAA,OAAA,CAAA,EAAA;AACA,QAAA,OAAA,IAAA;AACA;;AAEA,MAAA,OAAA,KAAA;AACA,KAAA;;AAEA,IAAA,yBAAA,EAAA,OAAA,IAAA;AACA;AACA;AACA,MAAA,MAAA,OAAA,GAAA,OAAA,CAAA,GAAA;;AAEA,MAAA,MAAA,MAAA,GAAA,OAAA,CAAA,MAAA,EAAA,WAAA,EAAA;AACA;AACA,MAAA,IAAA,MAAA,KAAA,SAAA,IAAA,MAAA,KAAA,MAAA,EAAA;AACA,QAAA,OAAA,IAAA;AACA;;AAEA,MAAA,MAAA,uBAAA,GAAA,OAAA,CAAA,sBAAA;AACA,MAAA,IAAA,OAAA,IAAA,uBAAA,GAAA,OAAA,EAAA,OAAA,CAAA,EAAA;AACA,QAAA,OAAA,IAAA;AACA;;AAEA,MAAA,OAAA,KAAA;AACA,KAAA;;AAEA,IAAA,6BAAA,EAAA,KAAA;AACA,IAAA,6BAAA,EAAA,KAAA;AACA,IAAA,WAAA,EAAA,CAAA,IAAA,EAAA,GAAA,KAAA;AACA,MAAA,eAAA,CAAA,IAAA,EAAA,qBAAA,CAAA;AACA,MAAA,IAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,IAAA,sBAAA,CAAA,GAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,YAAA,CAAA,sBAAA,EAAA,IAAA,CAAA;AACA;;AAEA,MAAA,OAAA,CAAA,eAAA,EAAA,WAAA,GAAA,IAAA,EAAA,GAAA,CAAA;AACA,KAAA;AACA,IAAA,YAAA,EAAA,CAAA,IAAA,EAAA,GAAA,KAAA;AACA,MAAA,OAAA,CAAA,eAAA,EAAA,YAAA,GAAA,IAAA,EAAA,GAAA,CAAA;AACA,KAAA;AACA,IAAA,2BAAA,EAAA;AACA,MAAA,IAAA;AACA,MAAA,OAAA;AACA,MAAA,QAAA;AACA,SAAA;AACA,MAAA,OAAA,CAAA,eAAA,EAAA,2BAAA,GAAA,IAAA,EAAA,OAAA,EAAA,QAAA,CAAA;AACA,KAAA;AACA,GAAA;;AAEA,EAAA,OAAA,qBAAA;AACA;;;;"}