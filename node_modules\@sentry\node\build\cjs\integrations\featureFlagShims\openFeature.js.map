{"version": 3, "file": "openFeature.js", "sources": ["../../../../src/integrations/featureFlagShims/openFeature.ts"], "sourcesContent": ["import { consoleSandbox, defineIntegration, isBrowser } from '@sentry/core';\n\n/**\n * This is a shim for the OpenFeature integration.\n * We need this in order to not throw runtime errors when accidentally importing this on the server through a meta framework like Next.js.\n */\nexport const openFeatureIntegrationShim = defineIntegration((_options?: unknown) => {\n  if (!isBrowser()) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn('The openFeatureIntegration() can only be used in the browser.');\n    });\n  }\n\n  return {\n    name: 'OpenFeature',\n  };\n});\n\n/**\n * This is a shim for the OpenFeature integration hook.\n */\nexport class OpenFeatureIntegrationHookShim {\n  /**\n   *\n   */\n  public constructor() {\n    if (!isBrowser()) {\n      consoleSandbox(() => {\n        // eslint-disable-next-line no-console\n        console.warn('The OpenFeatureIntegrationHook can only be used in the browser.');\n      });\n    }\n  }\n\n  /**\n   *\n   */\n  public after(): void {\n    // No-op\n  }\n\n  /**\n   *\n   */\n  public error(): void {\n    // No-op\n  }\n}\n"], "names": ["defineIntegration", "<PERSON><PERSON><PERSON><PERSON>", "consoleSandbox"], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACO,MAAM,6BAA6BA,sBAAiB,CAAC,CAAC,QAAQ,KAAe;AACpF,EAAE,IAAI,CAACC,cAAS,EAAE,EAAE;AACpB,IAAIC,mBAAc,CAAC,MAAM;AACzB;AACA,MAAM,OAAO,CAAC,IAAI,CAAC,+DAA+D,CAAC;AACnF,KAAK,CAAC;AACN;;AAEA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,aAAa;AACvB,GAAG;AACH,CAAC;;AAED;AACA;AACA;AACO,MAAM,8BAA+B,CAAA;AAC5C;AACA;AACA;AACA,GAAS,WAAW,GAAG;AACvB,IAAI,IAAI,CAACD,cAAS,EAAE,EAAE;AACtB,MAAMC,mBAAc,CAAC,MAAM;AAC3B;AACA,QAAQ,OAAO,CAAC,IAAI,CAAC,iEAAiE,CAAC;AACvF,OAAO,CAAC;AACR;AACA;;AAEA;AACA;AACA;AACA,GAAS,KAAK,GAAS;AACvB;AACA;;AAEA;AACA;AACA;AACA,GAAS,KAAK,GAAS;AACvB;AACA;AACA;;;;;"}