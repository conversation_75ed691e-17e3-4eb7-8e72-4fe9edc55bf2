{"version": 3, "file": "unleash.js", "sources": ["../../../../src/integrations/featureFlagShims/unleash.ts"], "sourcesContent": ["import { consoleSandbox, defineIntegration, isBrowser } from '@sentry/core';\n\n/**\n * This is a shim for the Unleash integration.\n * We need this in order to not throw runtime errors when accidentally importing this on the server through a meta framework like Next.js.\n */\nexport const unleashIntegrationShim = defineIntegration((_options?: unknown) => {\n  if (!isBrowser()) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn('The unleashIntegration() can only be used in the browser.');\n    });\n  }\n\n  return {\n    name: 'Unleash',\n  };\n});\n"], "names": ["defineIntegration", "<PERSON><PERSON><PERSON><PERSON>", "consoleSandbox"], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACO,MAAM,yBAAyBA,sBAAiB,CAAC,CAAC,QAAQ,KAAe;AAChF,EAAE,IAAI,CAACC,cAAS,EAAE,EAAE;AACpB,IAAIC,mBAAc,CAAC,MAAM;AACzB;AACA,MAAM,OAAO,CAAC,IAAI,CAAC,2DAA2D,CAAC;AAC/E,KAAK,CAAC;AACN;;AAEA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,SAAS;AACnB,GAAG;AACH,CAAC;;;;"}