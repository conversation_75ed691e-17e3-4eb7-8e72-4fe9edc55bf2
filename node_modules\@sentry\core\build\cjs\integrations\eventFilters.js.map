{"version": 3, "file": "eventFilters.js", "sources": ["../../../src/integrations/eventFilters.ts"], "sourcesContent": ["import { DEBUG_BUILD } from '../debug-build';\nimport { defineIntegration } from '../integration';\nimport type { Event } from '../types-hoist/event';\nimport type { IntegrationFn } from '../types-hoist/integration';\nimport type { StackFrame } from '../types-hoist/stackframe';\nimport { getPossibleEventMessages } from '../utils/eventUtils';\nimport { logger } from '../utils/logger';\nimport { getEventDescription } from '../utils/misc';\nimport { stringMatchesSomePattern } from '../utils/string';\n\n// \"Script error.\" is hard coded into browsers for errors that it can't read.\n// this is the result of a script being pulled in from an external domain and CORS.\nconst DEFAULT_IGNORE_ERRORS = [\n  /^Script error\\.?$/,\n  /^Javascript error: Script error\\.? on line 0$/,\n  /^ResizeObserver loop completed with undelivered notifications.$/, // The browser logs this when a ResizeObserver handler takes a bit longer. Usually this is not an actual issue though. It indicates slowness.\n  /^Cannot redefine property: googletag$/, // This is thrown when google tag manager is used in combination with an ad blocker\n  /^Can't find variable: gmo$/, // Error from Google Search App https://issuetracker.google.com/issues/396043331\n  /^undefined is not an object \\(evaluating 'a\\.[A-Z]'\\)$/, // Random error that happens but not actionable or noticeable to end-users.\n  'can\\'t redefine non-configurable property \"solana\"', // Probably a browser extension or custom browser (Brave) throwing this error\n  \"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)\", // Error thrown by GTM, seemingly not affecting end-users\n  \"Can't find variable: _AutofillCallbackHandler\", // Unactionable error in instagram webview https://developers.facebook.com/community/threads/320013549791141/\n  /^Non-Error promise rejection captured with value: Object Not Found Matching Id:\\d+, MethodName:simulateEvent, ParamCount:\\d+$/, // unactionable error from CEFSharp, a .NET library that embeds chromium in .NET apps\n  /^Java exception was raised during method invocation$/, // error from Facebook Mobile browser (https://github.com/getsentry/sentry-javascript/issues/15065)\n];\n\n/** Options for the EventFilters integration */\nexport interface EventFiltersOptions {\n  allowUrls: Array<string | RegExp>;\n  denyUrls: Array<string | RegExp>;\n  ignoreErrors: Array<string | RegExp>;\n  ignoreTransactions: Array<string | RegExp>;\n  ignoreInternal: boolean;\n  disableErrorDefaults: boolean;\n}\n\nconst INTEGRATION_NAME = 'EventFilters';\n\n/**\n * An integration that filters out events (errors and transactions) based on:\n *\n * - (Errors) A curated list of known low-value or irrelevant errors (see {@link DEFAULT_IGNORE_ERRORS})\n * - (Errors) A list of error messages or urls/filenames passed in via\n *   - Top level Sentry.init options (`ignoreErrors`, `denyUrls`, `allowUrls`)\n *   - The same options passed to the integration directly via @param options\n * - (Transactions/Spans) A list of root span (transaction) names passed in via\n *   - Top level Sentry.init option (`ignoreTransactions`)\n *   - The same option passed to the integration directly via @param options\n *\n * Events filtered by this integration will not be sent to Sentry.\n */\nexport const eventFiltersIntegration = defineIntegration((options: Partial<EventFiltersOptions> = {}) => {\n  let mergedOptions: Partial<EventFiltersOptions> | undefined;\n  return {\n    name: INTEGRATION_NAME,\n    setup(client) {\n      const clientOptions = client.getOptions();\n      mergedOptions = _mergeOptions(options, clientOptions);\n    },\n    processEvent(event, _hint, client) {\n      if (!mergedOptions) {\n        const clientOptions = client.getOptions();\n        mergedOptions = _mergeOptions(options, clientOptions);\n      }\n      return _shouldDropEvent(event, mergedOptions) ? null : event;\n    },\n  };\n});\n\n/**\n * An integration that filters out events (errors and transactions) based on:\n *\n * - (Errors) A curated list of known low-value or irrelevant errors (see {@link DEFAULT_IGNORE_ERRORS})\n * - (Errors) A list of error messages or urls/filenames passed in via\n *   - Top level Sentry.init options (`ignoreErrors`, `denyUrls`, `allowUrls`)\n *   - The same options passed to the integration directly via @param options\n * - (Transactions/Spans) A list of root span (transaction) names passed in via\n *   - Top level Sentry.init option (`ignoreTransactions`)\n *   - The same option passed to the integration directly via @param options\n *\n * Events filtered by this integration will not be sent to Sentry.\n *\n * @deprecated this integration was renamed and will be removed in a future major version.\n * Use `eventFiltersIntegration` instead.\n */\nexport const inboundFiltersIntegration = defineIntegration(((options: Partial<EventFiltersOptions> = {}) => {\n  return {\n    ...eventFiltersIntegration(options),\n    name: 'InboundFilters',\n  };\n}) satisfies IntegrationFn);\n\nfunction _mergeOptions(\n  internalOptions: Partial<EventFiltersOptions> = {},\n  clientOptions: Partial<EventFiltersOptions> = {},\n): Partial<EventFiltersOptions> {\n  return {\n    allowUrls: [...(internalOptions.allowUrls || []), ...(clientOptions.allowUrls || [])],\n    denyUrls: [...(internalOptions.denyUrls || []), ...(clientOptions.denyUrls || [])],\n    ignoreErrors: [\n      ...(internalOptions.ignoreErrors || []),\n      ...(clientOptions.ignoreErrors || []),\n      ...(internalOptions.disableErrorDefaults ? [] : DEFAULT_IGNORE_ERRORS),\n    ],\n    ignoreTransactions: [...(internalOptions.ignoreTransactions || []), ...(clientOptions.ignoreTransactions || [])],\n  };\n}\n\nfunction _shouldDropEvent(event: Event, options: Partial<EventFiltersOptions>): boolean {\n  if (!event.type) {\n    // Filter errors\n    if (_isIgnoredError(event, options.ignoreErrors)) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Event dropped due to being matched by \\`ignoreErrors\\` option.\\nEvent: ${getEventDescription(event)}`,\n        );\n      return true;\n    }\n    if (_isUselessError(event)) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Event dropped due to not having an error message, error type or stacktrace.\\nEvent: ${getEventDescription(\n            event,\n          )}`,\n        );\n      return true;\n    }\n    if (_isDeniedUrl(event, options.denyUrls)) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Event dropped due to being matched by \\`denyUrls\\` option.\\nEvent: ${getEventDescription(\n            event,\n          )}.\\nUrl: ${_getEventFilterUrl(event)}`,\n        );\n      return true;\n    }\n    if (!_isAllowedUrl(event, options.allowUrls)) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Event dropped due to not being matched by \\`allowUrls\\` option.\\nEvent: ${getEventDescription(\n            event,\n          )}.\\nUrl: ${_getEventFilterUrl(event)}`,\n        );\n      return true;\n    }\n  } else if (event.type === 'transaction') {\n    // Filter transactions\n\n    if (_isIgnoredTransaction(event, options.ignoreTransactions)) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Event dropped due to being matched by \\`ignoreTransactions\\` option.\\nEvent: ${getEventDescription(event)}`,\n        );\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction _isIgnoredError(event: Event, ignoreErrors?: Array<string | RegExp>): boolean {\n  if (!ignoreErrors?.length) {\n    return false;\n  }\n\n  return getPossibleEventMessages(event).some(message => stringMatchesSomePattern(message, ignoreErrors));\n}\n\nfunction _isIgnoredTransaction(event: Event, ignoreTransactions?: Array<string | RegExp>): boolean {\n  if (!ignoreTransactions?.length) {\n    return false;\n  }\n\n  const name = event.transaction;\n  return name ? stringMatchesSomePattern(name, ignoreTransactions) : false;\n}\n\nfunction _isDeniedUrl(event: Event, denyUrls?: Array<string | RegExp>): boolean {\n  if (!denyUrls?.length) {\n    return false;\n  }\n  const url = _getEventFilterUrl(event);\n  return !url ? false : stringMatchesSomePattern(url, denyUrls);\n}\n\nfunction _isAllowedUrl(event: Event, allowUrls?: Array<string | RegExp>): boolean {\n  if (!allowUrls?.length) {\n    return true;\n  }\n  const url = _getEventFilterUrl(event);\n  return !url ? true : stringMatchesSomePattern(url, allowUrls);\n}\n\nfunction _getLastValidUrl(frames: StackFrame[] = []): string | null {\n  for (let i = frames.length - 1; i >= 0; i--) {\n    const frame = frames[i];\n\n    if (frame && frame.filename !== '<anonymous>' && frame.filename !== '[native code]') {\n      return frame.filename || null;\n    }\n  }\n\n  return null;\n}\n\nfunction _getEventFilterUrl(event: Event): string | null {\n  try {\n    // If there are linked exceptions or exception aggregates we only want to match against the top frame of the \"root\" (the main exception)\n    // The root always comes last in linked exceptions\n    const rootException = [...(event.exception?.values ?? [])]\n      .reverse()\n      .find(value => value.mechanism?.parent_id === undefined && value.stacktrace?.frames?.length);\n    const frames = rootException?.stacktrace?.frames;\n    return frames ? _getLastValidUrl(frames) : null;\n  } catch (oO) {\n    DEBUG_BUILD && logger.error(`Cannot extract url for event ${getEventDescription(event)}`);\n    return null;\n  }\n}\n\nfunction _isUselessError(event: Event): boolean {\n  // We only want to consider events for dropping that actually have recorded exception values.\n  if (!event.exception?.values?.length) {\n    return false;\n  }\n\n  return (\n    // No top-level message\n    !event.message &&\n    // There are no exception values that have a stacktrace, a non-generic-Error type or value\n    !event.exception.values.some(value => value.stacktrace || (value.type && value.type !== 'Error') || value.value)\n  );\n}\n"], "names": ["defineIntegration", "DEBUG_BUILD", "logger", "getEventDescription", "getPossibleEventMessages", "stringMatchesSomePattern"], "mappings": ";;;;;;;;;AAUA;AACA;AACA,MAAM,wBAAwB;AAC9B,EAAE,mBAAmB;AACrB,EAAE,+CAA+C;AACjD,EAAE,iEAAiE;AACnE,EAAE,uCAAuC;AACzC,EAAE,4BAA4B;AAC9B,EAAE,wDAAwD;AAC1D,EAAE,oDAAoD;AACtD,EAAE,+GAA+G;AACjH,EAAE,+CAA+C;AACjD,EAAE,+HAA+H;AACjI,EAAE,sDAAsD;AACxD,CAAC;;AAED;;AAUA,MAAM,gBAAA,GAAmB,cAAc;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACa,MAAA,uBAAA,GAA0BA,6BAAiB,CAAC,CAAC,OAAO,GAAiC,EAAE,KAAK;AACzG,EAAE,IAAI,aAAa;AACnB,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB,MAAM,MAAM,aAAc,GAAE,MAAM,CAAC,UAAU,EAAE;AAC/C,MAAM,gBAAgB,aAAa,CAAC,OAAO,EAAE,aAAa,CAAC;AAC3D,KAAK;AACL,IAAI,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE;AACvC,MAAM,IAAI,CAAC,aAAa,EAAE;AAC1B,QAAQ,MAAM,aAAc,GAAE,MAAM,CAAC,UAAU,EAAE;AACjD,QAAQ,gBAAgB,aAAa,CAAC,OAAO,EAAE,aAAa,CAAC;AAC7D;AACA,MAAM,OAAO,gBAAgB,CAAC,KAAK,EAAE,aAAa,CAAE,GAAE,IAAK,GAAE,KAAK;AAClE,KAAK;AACL,GAAG;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACa,MAAA,yBAAA,GAA4BA,6BAAiB,EAAE,CAAC,OAAO,GAAiC,EAAE,KAAK;AAC5G,EAAE,OAAO;AACT,IAAI,GAAG,uBAAuB,CAAC,OAAO,CAAC;AACvC,IAAI,IAAI,EAAE,gBAAgB;AAC1B,GAAG;AACH,CAAC;;AAED,SAAS,aAAa;AACtB,EAAE,eAAe,GAAiC,EAAE;AACpD,EAAE,aAAa,GAAiC,EAAE;AAClD,EAAgC;AAChC,EAAE,OAAO;AACT,IAAI,SAAS,EAAE,CAAC,IAAI,eAAe,CAAC,SAAU,IAAG,EAAE,CAAC,EAAE,IAAI,aAAa,CAAC,aAAa,EAAE,CAAC,CAAC;AACzF,IAAI,QAAQ,EAAE,CAAC,IAAI,eAAe,CAAC,QAAS,IAAG,EAAE,CAAC,EAAE,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC;AACtF,IAAI,YAAY,EAAE;AAClB,MAAM,IAAI,eAAe,CAAC,gBAAgB,EAAE,CAAC;AAC7C,MAAM,IAAI,aAAa,CAAC,gBAAgB,EAAE,CAAC;AAC3C,MAAM,IAAI,eAAe,CAAC,oBAAA,GAAuB,EAAC,GAAI,qBAAqB,CAAC;AAC5E,KAAK;AACL,IAAI,kBAAkB,EAAE,CAAC,IAAI,eAAe,CAAC,kBAAmB,IAAG,EAAE,CAAC,EAAE,IAAI,aAAa,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACpH,GAAG;AACH;;AAEA,SAAS,gBAAgB,CAAC,KAAK,EAAS,OAAO,EAAyC;AACxF,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;AACnB;AACA,IAAI,IAAI,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC,EAAE;AACtD,MAAMC,sBAAY;AAClB,QAAQC,aAAM,CAAC,IAAI;AACnB,UAAU,CAAC,uEAAuE,EAAEC,wBAAmB,CAAC,KAAK,CAAC,CAAC,CAAA;AACA,SAAA;AACA,MAAA,OAAA,IAAA;AACA;AACA,IAAA,IAAA,eAAA,CAAA,KAAA,CAAA,EAAA;AACA,MAAAF,sBAAA;AACA,QAAAC,aAAA,CAAA,IAAA;AACA,UAAA,CAAA,oFAAA,EAAAC,wBAAA;AACA,YAAA,KAAA;AACA,WAAA,CAAA,CAAA;AACA,SAAA;AACA,MAAA,OAAA,IAAA;AACA;AACA,IAAA,IAAA,YAAA,CAAA,KAAA,EAAA,OAAA,CAAA,QAAA,CAAA,EAAA;AACA,MAAAF,sBAAA;AACA,QAAAC,aAAA,CAAA,IAAA;AACA,UAAA,CAAA,mEAAA,EAAAC,wBAAA;AACA,YAAA,KAAA;AACA,WAAA,CAAA,QAAA,EAAA,kBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,SAAA;AACA,MAAA,OAAA,IAAA;AACA;AACA,IAAA,IAAA,CAAA,aAAA,CAAA,KAAA,EAAA,OAAA,CAAA,SAAA,CAAA,EAAA;AACA,MAAAF,sBAAA;AACA,QAAAC,aAAA,CAAA,IAAA;AACA,UAAA,CAAA,wEAAA,EAAAC,wBAAA;AACA,YAAA,KAAA;AACA,WAAA,CAAA,QAAA,EAAA,kBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,SAAA;AACA,MAAA,OAAA,IAAA;AACA;AACA,GAAA,MAAA,IAAA,KAAA,CAAA,IAAA,KAAA,aAAA,EAAA;AACA;;AAEA,IAAA,IAAA,qBAAA,CAAA,KAAA,EAAA,OAAA,CAAA,kBAAA,CAAA,EAAA;AACA,MAAAF,sBAAA;AACA,QAAAC,aAAA,CAAA,IAAA;AACA,UAAA,CAAA,6EAAA,EAAAC,wBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,SAAA;AACA,MAAA,OAAA,IAAA;AACA;AACA;AACA,EAAA,OAAA,KAAA;AACA;;AAEA,SAAA,eAAA,CAAA,KAAA,EAAA,YAAA,EAAA;AACA,EAAA,IAAA,CAAA,YAAA,EAAA,MAAA,EAAA;AACA,IAAA,OAAA,KAAA;AACA;;AAEA,EAAA,OAAAC,mCAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,OAAA,IAAAC,+BAAA,CAAA,OAAA,EAAA,YAAA,CAAA,CAAA;AACA;;AAEA,SAAA,qBAAA,CAAA,KAAA,EAAA,kBAAA,EAAA;AACA,EAAA,IAAA,CAAA,kBAAA,EAAA,MAAA,EAAA;AACA,IAAA,OAAA,KAAA;AACA;;AAEA,EAAA,MAAA,IAAA,GAAA,KAAA,CAAA,WAAA;AACA,EAAA,OAAA,IAAA,GAAAA,+BAAA,CAAA,IAAA,EAAA,kBAAA,CAAA,GAAA,KAAA;AACA;;AAEA,SAAA,YAAA,CAAA,KAAA,EAAA,QAAA,EAAA;AACA,EAAA,IAAA,CAAA,QAAA,EAAA,MAAA,EAAA;AACA,IAAA,OAAA,KAAA;AACA;AACA,EAAA,MAAA,GAAA,GAAA,kBAAA,CAAA,KAAA,CAAA;AACA,EAAA,OAAA,CAAA,GAAA,GAAA,KAAA,GAAAA,+BAAA,CAAA,GAAA,EAAA,QAAA,CAAA;AACA;;AAEA,SAAA,aAAA,CAAA,KAAA,EAAA,SAAA,EAAA;AACA,EAAA,IAAA,CAAA,SAAA,EAAA,MAAA,EAAA;AACA,IAAA,OAAA,IAAA;AACA;AACA,EAAA,MAAA,GAAA,GAAA,kBAAA,CAAA,KAAA,CAAA;AACA,EAAA,OAAA,CAAA,GAAA,GAAA,IAAA,GAAAA,+BAAA,CAAA,GAAA,EAAA,SAAA,CAAA;AACA;;AAEA,SAAA,gBAAA,CAAA,MAAA,GAAA,EAAA,EAAA;AACA,EAAA,KAAA,IAAA,CAAA,GAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,EAAA;AACA,IAAA,MAAA,KAAA,GAAA,MAAA,CAAA,CAAA,CAAA;;AAEA,IAAA,IAAA,KAAA,IAAA,KAAA,CAAA,QAAA,KAAA,aAAA,IAAA,KAAA,CAAA,QAAA,KAAA,eAAA,EAAA;AACA,MAAA,OAAA,KAAA,CAAA,QAAA,IAAA,IAAA;AACA;AACA;;AAEA,EAAA,OAAA,IAAA;AACA;;AAEA,SAAA,kBAAA,CAAA,KAAA,EAAA;AACA,EAAA,IAAA;AACA;AACA;AACA,IAAA,MAAA,aAAA,GAAA,CAAA,IAAA,KAAA,CAAA,SAAA,EAAA,MAAA,IAAA,EAAA,CAAA;AACA,OAAA,OAAA;AACA,OAAA,IAAA,CAAA,KAAA,IAAA,KAAA,CAAA,SAAA,EAAA,SAAA,KAAA,SAAA,IAAA,KAAA,CAAA,UAAA,EAAA,MAAA,EAAA,MAAA,CAAA;AACA,IAAA,MAAA,MAAA,GAAA,aAAA,EAAA,UAAA,EAAA,MAAA;AACA,IAAA,OAAA,MAAA,GAAA,gBAAA,CAAA,MAAA,CAAA,GAAA,IAAA;AACA,GAAA,CAAA,OAAA,EAAA,EAAA;AACA,IAAAJ,sBAAA,IAAAC,aAAA,CAAA,KAAA,CAAA,CAAA,6BAAA,EAAAC,wBAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA,OAAA,IAAA;AACA;AACA;;AAEA,SAAA,eAAA,CAAA,KAAA,EAAA;AACA;AACA,EAAA,IAAA,CAAA,KAAA,CAAA,SAAA,EAAA,MAAA,EAAA,MAAA,EAAA;AACA,IAAA,OAAA,KAAA;AACA;;AAEA,EAAA;AACA;AACA,IAAA,CAAA,KAAA,CAAA,OAAA;AACA;AACA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,IAAA,KAAA,CAAA,UAAA,KAAA,KAAA,CAAA,IAAA,IAAA,KAAA,CAAA,IAAA,KAAA,OAAA,CAAA,IAAA,KAAA,CAAA,KAAA;AACA;AACA;;;;;"}