{"version": 3, "file": "index.js", "sources": ["../../../../../../src/integrations/tracing/fastify/fastify-otel/index.js"], "sourcesContent": ["/*\nVendored in and modified from @fastify/otel version 0.8.0\nhttps://github.com/fastify/otel/releases/tag/v0.8.0\n\nTried not to modify the original code too much keeping it as a JavaScript CJS module to make it easier to update when required\n\nModifications include:\n- Removed reading of package.json to get the version and package name\n\nMIT License\n\nCopyright (c) 2024 Fastify\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\n/* eslint-disable jsdoc/require-jsdoc */\n/* eslint-disable max-lines */\n/* eslint-disable no-param-reassign */\nimport dc from 'node:diagnostics_channel';\nimport { context, diag, propagation, SpanStatusCode, trace } from '@opentelemetry/api';\nimport { getRPCMetadata, RPCType } from '@opentelemetry/core';\nimport { InstrumentationBase } from '@opentelemetry/instrumentation';\nimport {\n  ATTR_HTTP_REQUEST_METHOD,\n  ATTR_HTTP_RESPONSE_STATUS_CODE,\n  ATTR_HTTP_ROUTE,\n  ATTR_SERVICE_NAME,\n} from '@opentelemetry/semantic-conventions';\nimport * as minimatch from 'minimatch';\n\n// SENTRY VENDOR NOTE\n// Instead of using the package.json file, we hard code the package name and version here.\nconst PACKAGE_NAME = '@fastify/otel';\nconst PACKAGE_VERSION = '0.8.0';\n\n// Constants\nconst SUPPORTED_VERSIONS = '>=4.0.0 <6';\nconst FASTIFY_HOOKS = [\n  'onRequest',\n  'preParsing',\n  'preValidation',\n  'preHandler',\n  'preSerialization',\n  'onSend',\n  'onResponse',\n  'onError',\n];\nconst ATTRIBUTE_NAMES = {\n  HOOK_NAME: 'hook.name',\n  FASTIFY_TYPE: 'fastify.type',\n  HOOK_CALLBACK_NAME: 'hook.callback.name',\n  ROOT: 'fastify.root',\n};\nconst HOOK_TYPES = {\n  ROUTE: 'route-hook',\n  INSTANCE: 'hook',\n  HANDLER: 'request-handler',\n};\nconst ANONYMOUS_FUNCTION_NAME = 'anonymous';\n\n// Symbols\nconst kInstrumentation = Symbol('fastify otel instance');\nconst kRequestSpan = Symbol('fastify otel request spans');\nconst kRequestContext = Symbol('fastify otel request context');\nconst kAddHookOriginal = Symbol('fastify otel addhook original');\nconst kSetNotFoundOriginal = Symbol('fastify otel setnotfound original');\nconst kIgnorePaths = Symbol('fastify otel ignore path');\n\nexport class FastifyOtelInstrumentation extends InstrumentationBase {\n  constructor(config) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n    this.servername = config?.servername ?? process.env.OTEL_SERVICE_NAME ?? 'fastify';\n    this[kIgnorePaths] = null;\n    this._logger = diag.createComponentLogger({ namespace: PACKAGE_NAME });\n\n    if (config?.ignorePaths != null || process.env.OTEL_FASTIFY_IGNORE_PATHS != null) {\n      const ignorePaths = config?.ignorePaths ?? process.env.OTEL_FASTIFY_IGNORE_PATHS;\n\n      if ((typeof ignorePaths !== 'string' || ignorePaths.length === 0) && typeof ignorePaths !== 'function') {\n        throw new TypeError('ignorePaths must be a string or a function');\n      }\n\n      const globMatcher = minimatch.minimatch;\n\n      this[kIgnorePaths] = routeOptions => {\n        if (typeof ignorePaths === 'function') {\n          return ignorePaths(routeOptions);\n        } else {\n          return globMatcher(routeOptions.url, ignorePaths);\n        }\n      };\n    }\n  }\n\n  enable() {\n    if (this._handleInitialization === undefined && this.getConfig().registerOnInitialization) {\n      const FastifyInstrumentationPlugin = this.plugin();\n      this._handleInitialization = message => {\n        message.fastify.register(FastifyInstrumentationPlugin);\n      };\n      dc.subscribe('fastify.initialization', this._handleInitialization);\n    }\n    return super.enable();\n  }\n\n  disable() {\n    if (this._handleInitialization) {\n      dc.unsubscribe('fastify.initialization', this._handleInitialization);\n      this._handleInitialization = undefined;\n    }\n    return super.disable();\n  }\n\n  // We do not do patching in this instrumentation\n  init() {\n    return [];\n  }\n\n  plugin() {\n    const instrumentation = this;\n\n    FastifyInstrumentationPlugin[Symbol.for('skip-override')] = true;\n    FastifyInstrumentationPlugin[Symbol.for('fastify.display-name')] = '@fastify/otel';\n    FastifyInstrumentationPlugin[Symbol.for('plugin-meta')] = {\n      fastify: SUPPORTED_VERSIONS,\n      name: '@fastify/otel',\n    };\n\n    return FastifyInstrumentationPlugin;\n\n    function FastifyInstrumentationPlugin(instance, opts, done) {\n      instance.decorate(kInstrumentation, instrumentation);\n      // addHook and notfoundHandler are essentially inherited from the prototype\n      // what is important is to bound it to the right instance\n      instance.decorate(kAddHookOriginal, instance.addHook);\n      instance.decorate(kSetNotFoundOriginal, instance.setNotFoundHandler);\n      instance.decorateRequest('opentelemetry', function openetelemetry() {\n        const ctx = this[kRequestContext];\n        const span = this[kRequestSpan];\n        return {\n          span,\n          tracer: instrumentation.tracer,\n          context: ctx,\n          inject: (carrier, setter) => {\n            return propagation.inject(ctx, carrier, setter);\n          },\n          extract: (carrier, getter) => {\n            return propagation.extract(ctx, carrier, getter);\n          },\n        };\n      });\n      instance.decorateRequest(kRequestSpan, null);\n      instance.decorateRequest(kRequestContext, null);\n\n      instance.addHook('onRoute', function (routeOptions) {\n        if (instrumentation[kIgnorePaths]?.(routeOptions) === true) {\n          instrumentation._logger.debug(\n            `Ignoring route instrumentation ${routeOptions.method} ${routeOptions.url} because it matches the ignore path`,\n          );\n          return;\n        }\n\n        for (const hook of FASTIFY_HOOKS) {\n          if (routeOptions[hook] != null) {\n            const handlerLike = routeOptions[hook];\n\n            if (typeof handlerLike === 'function') {\n              routeOptions[hook] = handlerWrapper(handlerLike, {\n                [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n                [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - route -> ${hook}`,\n                [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.ROUTE,\n                [ATTR_HTTP_ROUTE]: routeOptions.url,\n                [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n                  handlerLike.name?.length > 0 ? handlerLike.name : ANONYMOUS_FUNCTION_NAME /* c8 ignore next */,\n              });\n            } else if (Array.isArray(handlerLike)) {\n              const wrappedHandlers = [];\n\n              for (const handler of handlerLike) {\n                wrappedHandlers.push(\n                  handlerWrapper(handler, {\n                    [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n                    [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - route -> ${hook}`,\n                    [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.ROUTE,\n                    [ATTR_HTTP_ROUTE]: routeOptions.url,\n                    [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n                      handler.name?.length > 0 ? handler.name : ANONYMOUS_FUNCTION_NAME,\n                  }),\n                );\n              }\n\n              routeOptions[hook] = wrappedHandlers;\n            }\n          }\n        }\n\n        // We always want to add the onSend hook to the route to be executed last\n        if (routeOptions.onSend != null) {\n          routeOptions.onSend = Array.isArray(routeOptions.onSend)\n            ? [...routeOptions.onSend, onSendHook]\n            : [routeOptions.onSend, onSendHook];\n        } else {\n          routeOptions.onSend = onSendHook;\n        }\n\n        // We always want to add the onError hook to the route to be executed last\n        if (routeOptions.onError != null) {\n          routeOptions.onError = Array.isArray(routeOptions.onError)\n            ? [...routeOptions.onError, onErrorHook]\n            : [routeOptions.onError, onErrorHook];\n        } else {\n          routeOptions.onError = onErrorHook;\n        }\n\n        routeOptions.handler = handlerWrapper(routeOptions.handler, {\n          [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n          [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - route-handler`,\n          [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.HANDLER,\n          [ATTR_HTTP_ROUTE]: routeOptions.url,\n          [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n            routeOptions.handler.name.length > 0 ? routeOptions.handler.name : ANONYMOUS_FUNCTION_NAME,\n        });\n      });\n\n      instance.addHook('onRequest', function (request, _reply, hookDone) {\n        if (this[kInstrumentation].isEnabled() === false) {\n          return hookDone();\n        } else if (\n          this[kInstrumentation][kIgnorePaths]?.({\n            url: request.url,\n            method: request.method,\n          }) === true\n        ) {\n          this[kInstrumentation]._logger.debug(\n            `Ignoring request ${request.method} ${request.url} because it matches the ignore path`,\n          );\n          return hookDone();\n        }\n\n        let ctx = context.active();\n\n        if (trace.getSpan(ctx) == null) {\n          ctx = propagation.extract(ctx, request.headers);\n        }\n\n        const rpcMetadata = getRPCMetadata(ctx);\n\n        if (request.routeOptions.url != null && rpcMetadata?.type === RPCType.HTTP) {\n          rpcMetadata.route = request.routeOptions.url;\n        }\n\n        /** @type {import('@opentelemetry/api').Span} */\n        const span = this[kInstrumentation].tracer.startSpan(\n          'request',\n          {\n            attributes: {\n              [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n              [ATTRIBUTE_NAMES.ROOT]: '@fastify/otel',\n              [ATTR_HTTP_ROUTE]: request.url,\n              [ATTR_HTTP_REQUEST_METHOD]: request.method,\n            },\n          },\n          ctx,\n        );\n\n        request[kRequestContext] = trace.setSpan(ctx, span);\n        request[kRequestSpan] = span;\n\n        context.with(request[kRequestContext], () => {\n          hookDone();\n        });\n      });\n\n      // onResponse is the last hook to be executed, only added for 404 handlers\n      instance.addHook('onResponse', function (request, reply, hookDone) {\n        const span = request[kRequestSpan];\n\n        if (span != null) {\n          span.setStatus({\n            code: SpanStatusCode.OK,\n            message: 'OK',\n          });\n          span.setAttributes({\n            [ATTR_HTTP_RESPONSE_STATUS_CODE]: 404,\n          });\n          span.end();\n        }\n\n        request[kRequestSpan] = null;\n\n        hookDone();\n      });\n\n      instance.addHook = addHookPatched;\n      instance.setNotFoundHandler = setNotFoundHandlerPatched;\n\n      done();\n\n      function onSendHook(request, reply, payload, hookDone) {\n        /** @type {import('@opentelemetry/api').Span} */\n        const span = request[kRequestSpan];\n\n        if (span != null) {\n          if (reply.statusCode < 500) {\n            span.setStatus({\n              code: SpanStatusCode.OK,\n              message: 'OK',\n            });\n          }\n\n          span.setAttributes({\n            [ATTR_HTTP_RESPONSE_STATUS_CODE]: reply.statusCode,\n          });\n          span.end();\n        }\n\n        request[kRequestSpan] = null;\n\n        hookDone(null, payload);\n      }\n\n      function onErrorHook(request, reply, error, hookDone) {\n        /** @type {Span} */\n        const span = request[kRequestSpan];\n\n        if (span != null) {\n          span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: error.message,\n          });\n          span.recordException(error);\n        }\n\n        hookDone();\n      }\n\n      function addHookPatched(name, hook) {\n        const addHookOriginal = this[kAddHookOriginal];\n\n        if (FASTIFY_HOOKS.includes(name)) {\n          return addHookOriginal.call(\n            this,\n            name,\n            handlerWrapper(hook, {\n              [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n              [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - ${name}`,\n              [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.INSTANCE,\n              [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n                hook.name?.length > 0 ? hook.name : ANONYMOUS_FUNCTION_NAME /* c8 ignore next */,\n            }),\n          );\n        } else {\n          return addHookOriginal.call(this, name, hook);\n        }\n      }\n\n      function setNotFoundHandlerPatched(hooks, handler) {\n        const setNotFoundHandlerOriginal = this[kSetNotFoundOriginal];\n        if (typeof hooks === 'function') {\n          handler = handlerWrapper(hooks, {\n            [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n            [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - not-found-handler`,\n            [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.INSTANCE,\n            [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n              hooks.name?.length > 0 ? hooks.name : ANONYMOUS_FUNCTION_NAME /* c8 ignore next */,\n          });\n          setNotFoundHandlerOriginal.call(this, handler);\n        } else {\n          if (hooks.preValidation != null) {\n            hooks.preValidation = handlerWrapper(hooks.preValidation, {\n              [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n              [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - not-found-handler - preValidation`,\n              [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.INSTANCE,\n              [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n                hooks.preValidation.name?.length > 0\n                  ? hooks.preValidation.name\n                  : ANONYMOUS_FUNCTION_NAME /* c8 ignore next */,\n            });\n          }\n\n          if (hooks.preHandler != null) {\n            hooks.preHandler = handlerWrapper(hooks.preHandler, {\n              [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n              [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - not-found-handler - preHandler`,\n              [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.INSTANCE,\n              [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n                hooks.preHandler.name?.length > 0\n                  ? hooks.preHandler.name\n                  : ANONYMOUS_FUNCTION_NAME /* c8 ignore next */,\n            });\n          }\n\n          handler = handlerWrapper(handler, {\n            [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n            [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - not-found-handler`,\n            [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.INSTANCE,\n            [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n              handler.name?.length > 0 ? handler.name : ANONYMOUS_FUNCTION_NAME /* c8 ignore next */,\n          });\n          setNotFoundHandlerOriginal.call(this, hooks, handler);\n        }\n      }\n\n      function handlerWrapper(handler, spanAttributes = {}) {\n        return function handlerWrapped(...args) {\n          /** @type {FastifyOtelInstrumentation} */\n          const instrumentation = this[kInstrumentation];\n          const [request] = args;\n\n          if (instrumentation.isEnabled() === false) {\n            return handler.call(this, ...args);\n          }\n\n          const ctx = request[kRequestContext] ?? context.active();\n          const span = instrumentation.tracer.startSpan(\n            `handler - ${\n              handler.name?.length > 0\n                ? handler.name\n                : this.pluginName /* c8 ignore next */ ?? ANONYMOUS_FUNCTION_NAME /* c8 ignore next */\n            }`,\n            {\n              attributes: spanAttributes,\n            },\n            ctx,\n          );\n\n          return context.with(\n            trace.setSpan(ctx, span),\n            function () {\n              try {\n                const res = handler.call(this, ...args);\n\n                if (typeof res?.then === 'function') {\n                  return res.then(\n                    result => {\n                      span.end();\n                      return result;\n                    },\n                    error => {\n                      span.setStatus({\n                        code: SpanStatusCode.ERROR,\n                        message: error.message,\n                      });\n                      span.recordException(error);\n                      span.end();\n                      return Promise.reject(error);\n                    },\n                  );\n                }\n\n                span.end();\n                return res;\n              } catch (error) {\n                span.setStatus({\n                  code: SpanStatusCode.ERROR,\n                  message: error.message,\n                });\n                span.recordException(error);\n                span.end();\n                throw error;\n              }\n            },\n            this,\n          );\n        };\n      }\n    }\n  }\n}\n"], "names": ["InstrumentationBase", "diag", "dc", "propagation", "ATTR_SERVICE_NAME", "ATTR_HTTP_ROUTE", "context", "trace", "getRPCMetadata", "RPCType", "ATTR_HTTP_REQUEST_METHOD", "SpanStatusCode", "ATTR_HTTP_RESPONSE_STATUS_CODE"], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAiBA;AACA;AACA,MAAM,YAAA,GAAe,eAAe;AACpC,MAAM,eAAA,GAAkB,OAAO;;AAE/B;AACA,MAAM,kBAAA,GAAqB,YAAY;AACvC,MAAM,gBAAgB;AACtB,EAAE,WAAW;AACb,EAAE,YAAY;AACd,EAAE,eAAe;AACjB,EAAE,YAAY;AACd,EAAE,kBAAkB;AACpB,EAAE,QAAQ;AACV,EAAE,YAAY;AACd,EAAE,SAAS;AACX,CAAC;AACD,MAAM,kBAAkB;AACxB,EAAE,SAAS,EAAE,WAAW;AACxB,EAAE,YAAY,EAAE,cAAc;AAC9B,EAAE,kBAAkB,EAAE,oBAAoB;AAC1C,EAAE,IAAI,EAAE,cAAc;AACtB,CAAC;AACD,MAAM,aAAa;AACnB,EAAE,KAAK,EAAE,YAAY;AACrB,EAAE,QAAQ,EAAE,MAAM;AAClB,EAAE,OAAO,EAAE,iBAAiB;AAC5B,CAAC;AACD,MAAM,uBAAA,GAA0B,WAAW;;AAE3C;AACA,MAAM,gBAAiB,GAAE,MAAM,CAAC,uBAAuB,CAAC;AACxD,MAAM,YAAa,GAAE,MAAM,CAAC,4BAA4B,CAAC;AACzD,MAAM,eAAgB,GAAE,MAAM,CAAC,8BAA8B,CAAC;AAC9D,MAAM,gBAAiB,GAAE,MAAM,CAAC,+BAA+B,CAAC;AAChE,MAAM,oBAAqB,GAAE,MAAM,CAAC,mCAAmC,CAAC;AACxE,MAAM,YAAa,GAAE,MAAM,CAAC,0BAA0B,CAAC;;AAEhD,MAAM,0BAA2B,SAAQA,mCAAoB,CAAA;AACpE,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,KAAK,CAAC,YAAY,EAAE,eAAe,EAAE,MAAM,CAAC;AAChD,IAAI,IAAI,CAAC,UAAW,GAAE,MAAM,EAAE,UAAA,IAAc,OAAO,CAAC,GAAG,CAAC,iBAAA,IAAqB,SAAS;AACtF,IAAI,IAAI,CAAC,YAAY,CAAA,GAAI,IAAI;AAC7B,IAAI,IAAI,CAAC,OAAQ,GAAEC,QAAI,CAAC,qBAAqB,CAAC,EAAE,SAAS,EAAE,YAAA,EAAc,CAAC;;AAE1E,IAAI,IAAI,MAAM,EAAE,eAAe,IAAA,IAAQ,OAAO,CAAC,GAAG,CAAC,yBAA0B,IAAG,IAAI,EAAE;AACtF,MAAM,MAAM,WAAY,GAAE,MAAM,EAAE,WAAY,IAAG,OAAO,CAAC,GAAG,CAAC,yBAAyB;;AAEtF,MAAM,IAAI,CAAC,OAAO,WAAY,KAAI,YAAY,WAAW,CAAC,MAAO,KAAI,CAAC,KAAK,OAAO,WAAY,KAAI,UAAU,EAAE;AAC9G,QAAQ,MAAM,IAAI,SAAS,CAAC,4CAA4C,CAAC;AACzE;;AAEA,MAAM,MAAM,WAAA,GAAc,SAAS,CAAC,SAAS;;AAE7C,MAAM,IAAI,CAAC,YAAY,CAAE,GAAE,gBAAgB;AAC3C,QAAQ,IAAI,OAAO,WAAY,KAAI,UAAU,EAAE;AAC/C,UAAU,OAAO,WAAW,CAAC,YAAY,CAAC;AAC1C,eAAe;AACf,UAAU,OAAO,WAAW,CAAC,YAAY,CAAC,GAAG,EAAE,WAAW,CAAC;AAC3D;AACA,OAAO;AACP;AACA;;AAEA,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,IAAI,CAAC,0BAA0B,SAAA,IAAa,IAAI,CAAC,SAAS,EAAE,CAAC,wBAAwB,EAAE;AAC/F,MAAM,MAAM,4BAA6B,GAAE,IAAI,CAAC,MAAM,EAAE;AACxD,MAAM,IAAI,CAAC,qBAAsB,GAAE,WAAW;AAC9C,QAAQ,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,4BAA4B,CAAC;AAC9D,OAAO;AACP,MAAMC,0BAAE,CAAC,SAAS,CAAC,wBAAwB,EAAE,IAAI,CAAC,qBAAqB,CAAC;AACxE;AACA,IAAI,OAAO,KAAK,CAAC,MAAM,EAAE;AACzB;;AAEA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,IAAI,CAAC,qBAAqB,EAAE;AACpC,MAAMA,0BAAE,CAAC,WAAW,CAAC,wBAAwB,EAAE,IAAI,CAAC,qBAAqB,CAAC;AAC1E,MAAM,IAAI,CAAC,qBAAsB,GAAE,SAAS;AAC5C;AACA,IAAI,OAAO,KAAK,CAAC,OAAO,EAAE;AAC1B;;AAEA;AACA,EAAE,IAAI,GAAG;AACT,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,MAAM,GAAG;AACX,IAAI,MAAM,eAAgB,GAAE,IAAI;;AAEhC,IAAI,4BAA4B,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAE,GAAE,IAAI;AACpE,IAAI,4BAA4B,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAE,GAAE,eAAe;AACtF,IAAI,4BAA4B,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA,GAAI;AAC9D,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,IAAI,EAAE,eAAe;AAC3B,KAAK;;AAEL,IAAI,OAAO,4BAA4B;;AAEvC,IAAI,SAAS,4BAA4B,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE;AAChE,MAAM,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,EAAE,eAAe,CAAC;AAC1D;AACA;AACA,MAAM,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC;AAC3D,MAAM,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,EAAE,QAAQ,CAAC,kBAAkB,CAAC;AAC1E,MAAM,QAAQ,CAAC,eAAe,CAAC,eAAe,EAAE,SAAS,cAAc,GAAG;AAC1E,QAAQ,MAAM,GAAI,GAAE,IAAI,CAAC,eAAe,CAAC;AACzC,QAAQ,MAAM,IAAK,GAAE,IAAI,CAAC,YAAY,CAAC;AACvC,QAAQ,OAAO;AACf,UAAU,IAAI;AACd,UAAU,MAAM,EAAE,eAAe,CAAC,MAAM;AACxC,UAAU,OAAO,EAAE,GAAG;AACtB,UAAU,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK;AACvC,YAAY,OAAOC,eAAW,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC;AAC3D,WAAW;AACX,UAAU,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK;AACxC,YAAY,OAAOA,eAAW,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC;AAC5D,WAAW;AACX,SAAS;AACT,OAAO,CAAC;AACR,MAAM,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC;AAClD,MAAM,QAAQ,CAAC,eAAe,CAAC,eAAe,EAAE,IAAI,CAAC;;AAErD,MAAM,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,YAAY,EAAE;AAC1D,QAAQ,IAAI,eAAe,CAAC,YAAY,CAAC,GAAG,YAAY,CAAA,KAAM,IAAI,EAAE;AACpE,UAAU,eAAe,CAAC,OAAO,CAAC,KAAK;AACvC,YAAY,CAAC,+BAA+B,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,mCAAmC,CAAC;AAC1H,WAAW;AACX,UAAU;AACV;;AAEA,QAAQ,KAAK,MAAM,IAAK,IAAG,aAAa,EAAE;AAC1C,UAAU,IAAI,YAAY,CAAC,IAAI,CAAE,IAAG,IAAI,EAAE;AAC1C,YAAY,MAAM,WAAY,GAAE,YAAY,CAAC,IAAI,CAAC;;AAElD,YAAY,IAAI,OAAO,WAAY,KAAI,UAAU,EAAE;AACnD,cAAc,YAAY,CAAC,IAAI,CAAA,GAAI,cAAc,CAAC,WAAW,EAAE;AAC/D,gBAAgB,CAACC,qCAAiB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC,UAAU;AAC1E,gBAAgB,CAAC,eAAe,CAAC,SAAS,GAAG,CAAC,EAAA,IAAA,CAAA,UAAA,CAAA,YAAA,EAAA,IAAA,CAAA,CAAA;AACA,gBAAA,CAAA,eAAA,CAAA,YAAA,GAAA,UAAA,CAAA,KAAA;AACA,gBAAA,CAAAC,mCAAA,GAAA,YAAA,CAAA,GAAA;AACA,gBAAA,CAAA,eAAA,CAAA,kBAAA;AACA,kBAAA,WAAA,CAAA,IAAA,EAAA,MAAA,GAAA,CAAA,GAAA,WAAA,CAAA,IAAA,GAAA,uBAAA;AACA,eAAA,CAAA;AACA,aAAA,MAAA,IAAA,KAAA,CAAA,OAAA,CAAA,WAAA,CAAA,EAAA;AACA,cAAA,MAAA,eAAA,GAAA,EAAA;;AAEA,cAAA,KAAA,MAAA,OAAA,IAAA,WAAA,EAAA;AACA,gBAAA,eAAA,CAAA,IAAA;AACA,kBAAA,cAAA,CAAA,OAAA,EAAA;AACA,oBAAA,CAAAD,qCAAA,GAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;AACA,oBAAA,CAAA,eAAA,CAAA,SAAA,GAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,YAAA,EAAA,IAAA,CAAA,CAAA;AACA,oBAAA,CAAA,eAAA,CAAA,YAAA,GAAA,UAAA,CAAA,KAAA;AACA,oBAAA,CAAAC,mCAAA,GAAA,YAAA,CAAA,GAAA;AACA,oBAAA,CAAA,eAAA,CAAA,kBAAA;AACA,sBAAA,OAAA,CAAA,IAAA,EAAA,MAAA,GAAA,CAAA,GAAA,OAAA,CAAA,IAAA,GAAA,uBAAA;AACA,mBAAA,CAAA;AACA,iBAAA;AACA;;AAEA,cAAA,YAAA,CAAA,IAAA,CAAA,GAAA,eAAA;AACA;AACA;AACA;;AAEA;AACA,QAAA,IAAA,YAAA,CAAA,MAAA,IAAA,IAAA,EAAA;AACA,UAAA,YAAA,CAAA,MAAA,GAAA,KAAA,CAAA,OAAA,CAAA,YAAA,CAAA,MAAA;AACA,cAAA,CAAA,GAAA,YAAA,CAAA,MAAA,EAAA,UAAA;AACA,cAAA,CAAA,YAAA,CAAA,MAAA,EAAA,UAAA,CAAA;AACA,SAAA,MAAA;AACA,UAAA,YAAA,CAAA,MAAA,GAAA,UAAA;AACA;;AAEA;AACA,QAAA,IAAA,YAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,UAAA,YAAA,CAAA,OAAA,GAAA,KAAA,CAAA,OAAA,CAAA,YAAA,CAAA,OAAA;AACA,cAAA,CAAA,GAAA,YAAA,CAAA,OAAA,EAAA,WAAA;AACA,cAAA,CAAA,YAAA,CAAA,OAAA,EAAA,WAAA,CAAA;AACA,SAAA,MAAA;AACA,UAAA,YAAA,CAAA,OAAA,GAAA,WAAA;AACA;;AAEA,QAAA,YAAA,CAAA,OAAA,GAAA,cAAA,CAAA,YAAA,CAAA,OAAA,EAAA;AACA,UAAA,CAAAD,qCAAA,GAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;AACA,UAAA,CAAA,eAAA,CAAA,SAAA,GAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,gBAAA,CAAA;AACA,UAAA,CAAA,eAAA,CAAA,YAAA,GAAA,UAAA,CAAA,OAAA;AACA,UAAA,CAAAC,mCAAA,GAAA,YAAA,CAAA,GAAA;AACA,UAAA,CAAA,eAAA,CAAA,kBAAA;AACA,YAAA,YAAA,CAAA,OAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,GAAA,YAAA,CAAA,OAAA,CAAA,IAAA,GAAA,uBAAA;AACA,SAAA,CAAA;AACA,OAAA,CAAA;;AAEA,MAAA,QAAA,CAAA,OAAA,CAAA,WAAA,EAAA,UAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA;AACA,QAAA,IAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,SAAA,EAAA,KAAA,KAAA,EAAA;AACA,UAAA,OAAA,QAAA,EAAA;AACA,SAAA,MAAA;AACA,UAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,YAAA,CAAA,GAAA;AACA,YAAA,GAAA,EAAA,OAAA,CAAA,GAAA;AACA,YAAA,MAAA,EAAA,OAAA,CAAA,MAAA;AACA,WAAA,CAAA,KAAA;AACA,UAAA;AACA,UAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,OAAA,CAAA,KAAA;AACA,YAAA,CAAA,iBAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA,EAAA,OAAA,CAAA,GAAA,CAAA,mCAAA,CAAA;AACA,WAAA;AACA,UAAA,OAAA,QAAA,EAAA;AACA;;AAEA,QAAA,IAAA,GAAA,GAAAC,WAAA,CAAA,MAAA,EAAA;;AAEA,QAAA,IAAAC,SAAA,CAAA,OAAA,CAAA,GAAA,CAAA,IAAA,IAAA,EAAA;AACA,UAAA,GAAA,GAAAJ,eAAA,CAAA,OAAA,CAAA,GAAA,EAAA,OAAA,CAAA,OAAA,CAAA;AACA;;AAEA,QAAA,MAAA,WAAA,GAAAK,mBAAA,CAAA,GAAA,CAAA;;AAEA,QAAA,IAAA,OAAA,CAAA,YAAA,CAAA,GAAA,IAAA,IAAA,IAAA,WAAA,EAAA,IAAA,KAAAC,YAAA,CAAA,IAAA,EAAA;AACA,UAAA,WAAA,CAAA,KAAA,GAAA,OAAA,CAAA,YAAA,CAAA,GAAA;AACA;;AAEA;AACA,QAAA,MAAA,IAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,MAAA,CAAA,SAAA;AACA,UAAA,SAAA;AACA,UAAA;AACA,YAAA,UAAA,EAAA;AACA,cAAA,CAAAL,qCAAA,GAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;AACA,cAAA,CAAA,eAAA,CAAA,IAAA,GAAA,eAAA;AACA,cAAA,CAAAC,mCAAA,GAAA,OAAA,CAAA,GAAA;AACA,cAAA,CAAAK,4CAAA,GAAA,OAAA,CAAA,MAAA;AACA,aAAA;AACA,WAAA;AACA,UAAA,GAAA;AACA,SAAA;;AAEA,QAAA,OAAA,CAAA,eAAA,CAAA,GAAAH,SAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA;AACA,QAAA,OAAA,CAAA,YAAA,CAAA,GAAA,IAAA;;AAEA,QAAAD,WAAA,CAAA,IAAA,CAAA,OAAA,CAAA,eAAA,CAAA,EAAA,MAAA;AACA,UAAA,QAAA,EAAA;AACA,SAAA,CAAA;AACA,OAAA,CAAA;;AAEA;AACA,MAAA,QAAA,CAAA,OAAA,CAAA,YAAA,EAAA,UAAA,OAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,QAAA,MAAA,IAAA,GAAA,OAAA,CAAA,YAAA,CAAA;;AAEA,QAAA,IAAA,IAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA;AACA,YAAA,IAAA,EAAAK,kBAAA,CAAA,EAAA;AACA,YAAA,OAAA,EAAA,IAAA;AACA,WAAA,CAAA;AACA,UAAA,IAAA,CAAA,aAAA,CAAA;AACA,YAAA,CAAAC,kDAAA,GAAA,GAAA;AACA,WAAA,CAAA;AACA,UAAA,IAAA,CAAA,GAAA,EAAA;AACA;;AAEA,QAAA,OAAA,CAAA,YAAA,CAAA,GAAA,IAAA;;AAEA,QAAA,QAAA,EAAA;AACA,OAAA,CAAA;;AAEA,MAAA,QAAA,CAAA,OAAA,GAAA,cAAA;AACA,MAAA,QAAA,CAAA,kBAAA,GAAA,yBAAA;;AAEA,MAAA,IAAA,EAAA;;AAEA,MAAA,SAAA,UAAA,CAAA,OAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA;AACA;AACA,QAAA,MAAA,IAAA,GAAA,OAAA,CAAA,YAAA,CAAA;;AAEA,QAAA,IAAA,IAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,KAAA,CAAA,UAAA,GAAA,GAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,CAAA;AACA,cAAA,IAAA,EAAAD,kBAAA,CAAA,EAAA;AACA,cAAA,OAAA,EAAA,IAAA;AACA,aAAA,CAAA;AACA;;AAEA,UAAA,IAAA,CAAA,aAAA,CAAA;AACA,YAAA,CAAAC,kDAAA,GAAA,KAAA,CAAA,UAAA;AACA,WAAA,CAAA;AACA,UAAA,IAAA,CAAA,GAAA,EAAA;AACA;;AAEA,QAAA,OAAA,CAAA,YAAA,CAAA,GAAA,IAAA;;AAEA,QAAA,QAAA,CAAA,IAAA,EAAA,OAAA,CAAA;AACA;;AAEA,MAAA,SAAA,WAAA,CAAA,OAAA,EAAA,KAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA;AACA,QAAA,MAAA,IAAA,GAAA,OAAA,CAAA,YAAA,CAAA;;AAEA,QAAA,IAAA,IAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA;AACA,YAAA,IAAA,EAAAD,kBAAA,CAAA,KAAA;AACA,YAAA,OAAA,EAAA,KAAA,CAAA,OAAA;AACA,WAAA,CAAA;AACA,UAAA,IAAA,CAAA,eAAA,CAAA,KAAA,CAAA;AACA;;AAEA,QAAA,QAAA,EAAA;AACA;;AAEA,MAAA,SAAA,cAAA,CAAA,IAAA,EAAA,IAAA,EAAA;AACA,QAAA,MAAA,eAAA,GAAA,IAAA,CAAA,gBAAA,CAAA;;AAEA,QAAA,IAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AACA,UAAA,OAAA,eAAA,CAAA,IAAA;AACA,YAAA,IAAA;AACA,YAAA,IAAA;AACA,YAAA,cAAA,CAAA,IAAA,EAAA;AACA,cAAA,CAAAP,qCAAA,GAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;AACA,cAAA,CAAA,eAAA,CAAA,SAAA,GAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA,cAAA,CAAA,eAAA,CAAA,YAAA,GAAA,UAAA,CAAA,QAAA;AACA,cAAA,CAAA,eAAA,CAAA,kBAAA;AACA,gBAAA,IAAA,CAAA,IAAA,EAAA,MAAA,GAAA,CAAA,GAAA,IAAA,CAAA,IAAA,GAAA,uBAAA;AACA,aAAA,CAAA;AACA,WAAA;AACA,SAAA,MAAA;AACA,UAAA,OAAA,eAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,IAAA,CAAA;AACA;AACA;;AAEA,MAAA,SAAA,yBAAA,CAAA,KAAA,EAAA,OAAA,EAAA;AACA,QAAA,MAAA,0BAAA,GAAA,IAAA,CAAA,oBAAA,CAAA;AACA,QAAA,IAAA,OAAA,KAAA,KAAA,UAAA,EAAA;AACA,UAAA,OAAA,GAAA,cAAA,CAAA,KAAA,EAAA;AACA,YAAA,CAAAA,qCAAA,GAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;AACA,YAAA,CAAA,eAAA,CAAA,SAAA,GAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,oBAAA,CAAA;AACA,YAAA,CAAA,eAAA,CAAA,YAAA,GAAA,UAAA,CAAA,QAAA;AACA,YAAA,CAAA,eAAA,CAAA,kBAAA;AACA,cAAA,KAAA,CAAA,IAAA,EAAA,MAAA,GAAA,CAAA,GAAA,KAAA,CAAA,IAAA,GAAA,uBAAA;AACA,WAAA,CAAA;AACA,UAAA,0BAAA,CAAA,IAAA,CAAA,IAAA,EAAA,OAAA,CAAA;AACA,SAAA,MAAA;AACA,UAAA,IAAA,KAAA,CAAA,aAAA,IAAA,IAAA,EAAA;AACA,YAAA,KAAA,CAAA,aAAA,GAAA,cAAA,CAAA,KAAA,CAAA,aAAA,EAAA;AACA,cAAA,CAAAA,qCAAA,GAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;AACA,cAAA,CAAA,eAAA,CAAA,SAAA,GAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,oCAAA,CAAA;AACA,cAAA,CAAA,eAAA,CAAA,YAAA,GAAA,UAAA,CAAA,QAAA;AACA,cAAA,CAAA,eAAA,CAAA,kBAAA;AACA,gBAAA,KAAA,CAAA,aAAA,CAAA,IAAA,EAAA,MAAA,GAAA;AACA,oBAAA,KAAA,CAAA,aAAA,CAAA;AACA,oBAAA,uBAAA;AACA,aAAA,CAAA;AACA;;AAEA,UAAA,IAAA,KAAA,CAAA,UAAA,IAAA,IAAA,EAAA;AACA,YAAA,KAAA,CAAA,UAAA,GAAA,cAAA,CAAA,KAAA,CAAA,UAAA,EAAA;AACA,cAAA,CAAAA,qCAAA,GAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;AACA,cAAA,CAAA,eAAA,CAAA,SAAA,GAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,iCAAA,CAAA;AACA,cAAA,CAAA,eAAA,CAAA,YAAA,GAAA,UAAA,CAAA,QAAA;AACA,cAAA,CAAA,eAAA,CAAA,kBAAA;AACA,gBAAA,KAAA,CAAA,UAAA,CAAA,IAAA,EAAA,MAAA,GAAA;AACA,oBAAA,KAAA,CAAA,UAAA,CAAA;AACA,oBAAA,uBAAA;AACA,aAAA,CAAA;AACA;;AAEA,UAAA,OAAA,GAAA,cAAA,CAAA,OAAA,EAAA;AACA,YAAA,CAAAA,qCAAA,GAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;AACA,YAAA,CAAA,eAAA,CAAA,SAAA,GAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,oBAAA,CAAA;AACA,YAAA,CAAA,eAAA,CAAA,YAAA,GAAA,UAAA,CAAA,QAAA;AACA,YAAA,CAAA,eAAA,CAAA,kBAAA;AACA,cAAA,OAAA,CAAA,IAAA,EAAA,MAAA,GAAA,CAAA,GAAA,OAAA,CAAA,IAAA,GAAA,uBAAA;AACA,WAAA,CAAA;AACA,UAAA,0BAAA,CAAA,IAAA,CAAA,IAAA,EAAA,KAAA,EAAA,OAAA,CAAA;AACA;AACA;;AAEA,MAAA,SAAA,cAAA,CAAA,OAAA,EAAA,cAAA,GAAA,EAAA,EAAA;AACA,QAAA,OAAA,SAAA,cAAA,CAAA,GAAA,IAAA,EAAA;AACA;AACA,UAAA,MAAA,eAAA,GAAA,IAAA,CAAA,gBAAA,CAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,GAAA,IAAA;;AAEA,UAAA,IAAA,eAAA,CAAA,SAAA,EAAA,KAAA,KAAA,EAAA;AACA,YAAA,OAAA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA,GAAA,IAAA,CAAA;AACA;;AAEA,UAAA,MAAA,GAAA,GAAA,OAAA,CAAA,eAAA,CAAA,IAAAE,WAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,IAAA,GAAA,eAAA,CAAA,MAAA,CAAA,SAAA;AACA,YAAA,CAAA,UAAA;AACA,cAAA,OAAA,CAAA,IAAA,EAAA,MAAA,GAAA;AACA,kBAAA,OAAA,CAAA;AACA,kBAAA,IAAA,CAAA,UAAA,yBAAA,uBAAA;AACA,aAAA,CAAA;AACA,YAAA;AACA,cAAA,UAAA,EAAA,cAAA;AACA,aAAA;AACA,YAAA,GAAA;AACA,WAAA;;AAEA,UAAA,OAAAA,WAAA,CAAA,IAAA;AACA,YAAAC,SAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA;AACA,YAAA,YAAA;AACA,cAAA,IAAA;AACA,gBAAA,MAAA,GAAA,GAAA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA,GAAA,IAAA,CAAA;;AAEA,gBAAA,IAAA,OAAA,GAAA,EAAA,IAAA,KAAA,UAAA,EAAA;AACA,kBAAA,OAAA,GAAA,CAAA,IAAA;AACA,oBAAA,MAAA,IAAA;AACA,sBAAA,IAAA,CAAA,GAAA,EAAA;AACA,sBAAA,OAAA,MAAA;AACA,qBAAA;AACA,oBAAA,KAAA,IAAA;AACA,sBAAA,IAAA,CAAA,SAAA,CAAA;AACA,wBAAA,IAAA,EAAAI,kBAAA,CAAA,KAAA;AACA,wBAAA,OAAA,EAAA,KAAA,CAAA,OAAA;AACA,uBAAA,CAAA;AACA,sBAAA,IAAA,CAAA,eAAA,CAAA,KAAA,CAAA;AACA,sBAAA,IAAA,CAAA,GAAA,EAAA;AACA,sBAAA,OAAA,OAAA,CAAA,MAAA,CAAA,KAAA,CAAA;AACA,qBAAA;AACA,mBAAA;AACA;;AAEA,gBAAA,IAAA,CAAA,GAAA,EAAA;AACA,gBAAA,OAAA,GAAA;AACA,eAAA,CAAA,OAAA,KAAA,EAAA;AACA,gBAAA,IAAA,CAAA,SAAA,CAAA;AACA,kBAAA,IAAA,EAAAA,kBAAA,CAAA,KAAA;AACA,kBAAA,OAAA,EAAA,KAAA,CAAA,OAAA;AACA,iBAAA,CAAA;AACA,gBAAA,IAAA,CAAA,eAAA,CAAA,KAAA,CAAA;AACA,gBAAA,IAAA,CAAA,GAAA,EAAA;AACA,gBAAA,MAAA,KAAA;AACA;AACA,aAAA;AACA,YAAA,IAAA;AACA,WAAA;AACA,SAAA;AACA;AACA;AACA;AACA;;;;"}