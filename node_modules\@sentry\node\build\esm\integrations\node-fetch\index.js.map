{"version": 3, "file": "index.js", "sources": ["../../../../src/integrations/node-fetch/index.ts"], "sourcesContent": ["import type { UndiciInstrumentationConfig } from '@opentelemetry/instrumentation-undici';\nimport { UndiciInstrumentation } from '@opentelemetry/instrumentation-undici';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration, getClient, hasSpansEnabled, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN } from '@sentry/core';\nimport { generateInstrumentOnce } from '../../otel/instrument';\nimport type { NodeClient } from '../../sdk/client';\nimport type { NodeClientOptions } from '../../types';\nimport { SentryNodeFetchInstrumentation } from './SentryNodeFetchInstrumentation';\n\nconst INTEGRATION_NAME = 'NodeFetch';\n\ninterface NodeFetchOptions {\n  /**\n   * Whether breadcrumbs should be recorded for requests.\n   * Defaults to true\n   */\n  breadcrumbs?: boolean;\n\n  /**\n   * If set to false, do not emit any spans.\n   * This will ensure that the default UndiciInstrumentation from OpenTelemetry is not setup,\n   * only the Sentry-specific instrumentation for breadcrumbs & trace propagation is applied.\n   *\n   * If `skipOpenTelemetrySetup: true` is configured, this defaults to `false`, otherwise it defaults to `true`.\n   */\n  spans?: boolean;\n\n  /**\n   * Do not capture spans or breadcrumbs for outgoing fetch requests to URLs where the given callback returns `true`.\n   * This controls both span & breadcrumb creation - spans will be non recording if tracing is disabled.\n   */\n  ignoreOutgoingRequests?: (url: string) => boolean;\n}\n\nconst instrumentOtelNodeFetch = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  UndiciInstrumentation,\n  (options: NodeFetchOptions) => {\n    return getConfigWithDefaults(options);\n  },\n);\n\nconst instrumentSentryNodeFetch = generateInstrumentOnce(\n  `${INTEGRATION_NAME}.sentry`,\n  SentryNodeFetchInstrumentation,\n  (options: NodeFetchOptions) => {\n    return options;\n  },\n);\n\nconst _nativeNodeFetchIntegration = ((options: NodeFetchOptions = {}) => {\n  return {\n    name: 'NodeFetch',\n    setupOnce() {\n      const instrumentSpans = _shouldInstrumentSpans(options, getClient<NodeClient>()?.getOptions());\n\n      // This is the \"regular\" OTEL instrumentation that emits spans\n      if (instrumentSpans) {\n        instrumentOtelNodeFetch(options);\n      }\n\n      // This is the Sentry-specific instrumentation that creates breadcrumbs & propagates traces\n      // This must be registered after the OTEL one, to ensure that the core trace propagation logic takes presedence\n      // Otherwise, the sentry-trace header may be set multiple times\n      instrumentSentryNodeFetch(options);\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const nativeNodeFetchIntegration = defineIntegration(_nativeNodeFetchIntegration);\n\n// Matching the behavior of the base instrumentation\nfunction getAbsoluteUrl(origin: string, path: string = '/'): string {\n  const url = `${origin}`;\n\n  if (url.endsWith('/') && path.startsWith('/')) {\n    return `${url}${path.slice(1)}`;\n  }\n\n  if (!url.endsWith('/') && !path.startsWith('/')) {\n    return `${url}/${path.slice(1)}`;\n  }\n\n  return `${url}${path}`;\n}\n\nfunction _shouldInstrumentSpans(options: NodeFetchOptions, clientOptions: Partial<NodeClientOptions> = {}): boolean {\n  // If `spans` is passed in, it takes precedence\n  // Else, we by default emit spans, unless `skipOpenTelemetrySetup` is set to `true` or spans are not enabled\n  return typeof options.spans === 'boolean'\n    ? options.spans\n    : !clientOptions.skipOpenTelemetrySetup && hasSpansEnabled(clientOptions);\n}\n\nfunction getConfigWithDefaults(options: Partial<NodeFetchOptions> = {}): UndiciInstrumentationConfig {\n  const instrumentationConfig = {\n    requireParentforSpans: false,\n    ignoreRequestHook: request => {\n      const url = getAbsoluteUrl(request.origin, request.path);\n      const _ignoreOutgoingRequests = options.ignoreOutgoingRequests;\n      const shouldIgnore = _ignoreOutgoingRequests && url && _ignoreOutgoingRequests(url);\n\n      return !!shouldIgnore;\n    },\n    startSpanHook: () => {\n      return {\n        [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.node_fetch',\n      };\n    },\n  } satisfies UndiciInstrumentationConfig;\n\n  return instrumentationConfig;\n}\n"], "names": [], "mappings": ";;;;;AASA,MAAM,gBAAA,GAAmB,WAAW;;AAyBpC,MAAM,uBAAA,GAA0B,sBAAsB;AACtD,EAAE,gBAAgB;AAClB,EAAE,qBAAqB;AACvB,EAAE,CAAC,OAAO,KAAuB;AACjC,IAAI,OAAO,qBAAqB,CAAC,OAAO,CAAC;AACzC,GAAG;AACH,CAAC;;AAED,MAAM,yBAAA,GAA4B,sBAAsB;AACxD,EAAE,CAAC,EAAA,gBAAA,CAAA,OAAA,CAAA;AACA,EAAA,8BAAA;AACA,EAAA,CAAA,OAAA,KAAA;AACA,IAAA,OAAA,OAAA;AACA,GAAA;AACA,CAAA;;AAEA,MAAA,2BAAA,IAAA,CAAA,OAAA,GAAA,EAAA,KAAA;AACA,EAAA,OAAA;AACA,IAAA,IAAA,EAAA,WAAA;AACA,IAAA,SAAA,GAAA;AACA,MAAA,MAAA,eAAA,GAAA,sBAAA,CAAA,OAAA,EAAA,SAAA,EAAA,EAAA,UAAA,EAAA,CAAA;;AAEA;AACA,MAAA,IAAA,eAAA,EAAA;AACA,QAAA,uBAAA,CAAA,OAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA,MAAA,yBAAA,CAAA,OAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA,CAAA,CAAA;;AAEA,MAAA,0BAAA,GAAA,iBAAA,CAAA,2BAAA;;AAEA;AACA,SAAA,cAAA,CAAA,MAAA,EAAA,IAAA,GAAA,GAAA,EAAA;AACA,EAAA,MAAA,GAAA,GAAA,CAAA,EAAA,MAAA,CAAA,CAAA;;AAEA,EAAA,IAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,IAAA,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AAEA,EAAA,IAAA,CAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,IAAA,OAAA,CAAA,EAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AAEA,EAAA,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AACA;;AAEA,SAAA,sBAAA,CAAA,OAAA,EAAA,aAAA,GAAA,EAAA,EAAA;AACA;AACA;AACA,EAAA,OAAA,OAAA,OAAA,CAAA,KAAA,KAAA;AACA,MAAA,OAAA,CAAA;AACA,MAAA,CAAA,aAAA,CAAA,sBAAA,IAAA,eAAA,CAAA,aAAA,CAAA;AACA;;AAEA,SAAA,qBAAA,CAAA,OAAA,GAAA,EAAA,EAAA;AACA,EAAA,MAAA,qBAAA,GAAA;AACA,IAAA,qBAAA,EAAA,KAAA;AACA,IAAA,iBAAA,EAAA,OAAA,IAAA;AACA,MAAA,MAAA,GAAA,GAAA,cAAA,CAAA,OAAA,CAAA,MAAA,EAAA,OAAA,CAAA,IAAA,CAAA;AACA,MAAA,MAAA,uBAAA,GAAA,OAAA,CAAA,sBAAA;AACA,MAAA,MAAA,YAAA,GAAA,uBAAA,IAAA,GAAA,IAAA,uBAAA,CAAA,GAAA,CAAA;;AAEA,MAAA,OAAA,CAAA,CAAA,YAAA;AACA,KAAA;AACA,IAAA,aAAA,EAAA,MAAA;AACA,MAAA,OAAA;AACA,QAAA,CAAA,gCAAA,GAAA,2BAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA;;AAEA,EAAA,OAAA,qBAAA;AACA;;;;"}