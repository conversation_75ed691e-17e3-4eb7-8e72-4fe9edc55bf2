{"version": 3, "file": "index.js", "sources": ["../../../../../src/integrations/tracing/hapi/index.ts"], "sourcesContent": ["import { HapiInstrumentation } from '@opentelemetry/instrumentation-hapi';\nimport type { IntegrationFn, Span } from '@sentry/core';\nimport {\n  captureException,\n  defineIntegration,\n  getClient,\n  getDefaultIsolationScope,\n  getIsolationScope,\n  logger,\n  SDK_VERSION,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  spanToJSON,\n} from '@sentry/core';\nimport { DEBUG_BUILD } from '../../../debug-build';\nimport { generateInstrumentOnce } from '../../../otel/instrument';\nimport { ensureIsWrapped } from '../../../utils/ensureIsWrapped';\nimport type { Request, RequestEvent, Server } from './types';\n\nconst INTEGRATION_NAME = 'Hapi';\n\nexport const instrumentHapi = generateInstrumentOnce(INTEGRATION_NAME, () => new HapiInstrumentation());\n\nconst _hapiIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentHapi();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for [Hapi](https://hapi.dev/).\n *\n * If you also want to capture errors, you need to call `setupHapiErrorHandler(server)` after you set up your server.\n *\n * For more information, see the [hapi documentation](https://docs.sentry.io/platforms/javascript/guides/hapi/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *   integrations: [Sentry.hapiIntegration()],\n * })\n * ```\n */\nexport const hapiIntegration = defineIntegration(_hapiIntegration);\n\nfunction isErrorEvent(event: unknown): event is RequestEvent {\n  return !!(event && typeof event === 'object' && 'error' in event && event.error);\n}\n\nfunction sendErrorToSentry(errorData: object): void {\n  captureException(errorData, {\n    mechanism: {\n      type: 'hapi',\n      handled: false,\n      data: {\n        function: 'hapiErrorPlugin',\n      },\n    },\n  });\n}\n\nexport const hapiErrorPlugin = {\n  name: 'SentryHapiErrorPlugin',\n  version: SDK_VERSION,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  register: async function (serverArg: Record<any, any>) {\n    const server = serverArg as unknown as Server;\n\n    server.events.on({ name: 'request', channels: ['error'] }, (request: Request, event: RequestEvent) => {\n      if (getIsolationScope() !== getDefaultIsolationScope()) {\n        const route = request.route;\n        if (route.path) {\n          getIsolationScope().setTransactionName(`${route.method.toUpperCase()} ${route.path}`);\n        }\n      } else {\n        DEBUG_BUILD &&\n          logger.warn('Isolation scope is still the default isolation scope - skipping setting transactionName');\n      }\n\n      if (isErrorEvent(event)) {\n        sendErrorToSentry(event.error);\n      }\n    });\n  },\n};\n\n/**\n * Add a Hapi plugin to capture errors to Sentry.\n *\n * @param server The Hapi server to attach the error handler to\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n * const Hapi = require('@hapi/hapi');\n *\n * const init = async () => {\n *   const server = Hapi.server();\n *\n *   // all your routes here\n *\n *   await Sentry.setupHapiErrorHandler(server);\n *\n *   await server.start();\n * };\n * ```\n */\nexport async function setupHapiErrorHandler(server: Server): Promise<void> {\n  await server.register(hapiErrorPlugin);\n\n  // Sadly, middleware spans do not go through `requestHook`, so we handle those here\n  // We register this hook in this method, because if we register it in the integration `setup`,\n  // it would always run even for users that are not even using hapi\n  const client = getClient();\n  if (client) {\n    client.on('spanStart', span => {\n      addHapiSpanAttributes(span);\n    });\n  }\n\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  ensureIsWrapped(server.register, 'hapi');\n}\n\nfunction addHapiSpanAttributes(span: Span): void {\n  const attributes = spanToJSON(span).data;\n\n  // this is one of: router, plugin, server.ext\n  const type = attributes['hapi.type'];\n\n  // If this is already set, or we have no Hapi span, no need to process again...\n  if (attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] || !type) {\n    return;\n  }\n\n  span.setAttributes({\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.hapi',\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: `${type}.hapi`,\n  });\n}\n"], "names": ["generateInstrumentOnce", "HapiInstrumentation", "defineIntegration", "captureException", "SDK_VERSION", "getIsolationScope", "getDefaultIsolationScope", "DEBUG_BUILD", "logger", "getClient", "ensureIsWrapped", "spanToJSON", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN"], "mappings": ";;;;;;;;AAmBA,MAAM,gBAAA,GAAmB,MAAM;;AAElB,MAAA,cAAA,GAAiBA,iCAAsB,CAAC,gBAAgB,EAAE,MAAM,IAAIC,uCAAmB,EAAE;;AAEtG,MAAM,gBAAiB,IAAG,MAAM;AAChC,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,SAAS,GAAG;AAChB,MAAM,cAAc,EAAE;AACtB,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACa,eAAgB,GAAEC,sBAAiB,CAAC,gBAAgB;;AAEjE,SAAS,YAAY,CAAC,KAAK,EAAkC;AAC7D,EAAE,OAAO,CAAC,EAAE,KAAM,IAAG,OAAO,KAAM,KAAI,QAAS,IAAG,WAAW,KAAA,IAAS,KAAK,CAAC,KAAK,CAAC;AAClF;;AAEA,SAAS,iBAAiB,CAAC,SAAS,EAAgB;AACpD,EAAEC,qBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,SAAS,EAAE;AACf,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,IAAI,EAAE;AACZ,QAAQ,QAAQ,EAAE,iBAAiB;AACnC,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ;;AAEO,MAAM,kBAAkB;AAC/B,EAAE,IAAI,EAAE,uBAAuB;AAC/B,EAAE,OAAO,EAAEC,gBAAW;AACtB;AACA,EAAE,QAAQ,EAAE,gBAAgB,SAAS,EAAoB;AACzD,IAAI,MAAM,MAAO,GAAE,SAAU;;AAE7B,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAA,EAAG,EAAE,CAAC,OAAO,EAAW,KAAK,KAAmB;AAC1G,MAAM,IAAIC,sBAAiB,OAAOC,6BAAwB,EAAE,EAAE;AAC9D,QAAQ,MAAM,KAAA,GAAQ,OAAO,CAAC,KAAK;AACnC,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE;AACxB,UAAUD,sBAAiB,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAA,KAAA,CAAA,MAAA,CAAA,WAAA,EAAA,CAAA,CAAA,EAAA,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA;AACA,OAAA,MAAA;AACA,QAAAE,sBAAA;AACA,UAAAC,WAAA,CAAA,IAAA,CAAA,yFAAA,CAAA;AACA;;AAEA,MAAA,IAAA,YAAA,CAAA,KAAA,CAAA,EAAA;AACA,QAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AACA;AACA,KAAA,CAAA;AACA,GAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAA,qBAAA,CAAA,MAAA,EAAA;AACA,EAAA,MAAA,MAAA,CAAA,QAAA,CAAA,eAAA,CAAA;;AAEA;AACA;AACA;AACA,EAAA,MAAA,MAAA,GAAAC,cAAA,EAAA;AACA,EAAA,IAAA,MAAA,EAAA;AACA,IAAA,MAAA,CAAA,EAAA,CAAA,WAAA,EAAA,IAAA,IAAA;AACA,MAAA,qBAAA,CAAA,IAAA,CAAA;AACA,KAAA,CAAA;AACA;;AAEA;AACA,EAAAC,+BAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,CAAA;AACA;;AAEA,SAAA,qBAAA,CAAA,IAAA,EAAA;AACA,EAAA,MAAA,UAAA,GAAAC,eAAA,CAAA,IAAA,CAAA,CAAA,IAAA;;AAEA;AACA,EAAA,MAAA,IAAA,GAAA,UAAA,CAAA,WAAA,CAAA;;AAEA;AACA,EAAA,IAAA,UAAA,CAAAC,iCAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,CAAA,aAAA,CAAA;AACA,IAAA,CAAAC,qCAAA,GAAA,qBAAA;AACA,IAAA,CAAAD,iCAAA,GAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA;AACA,GAAA,CAAA;AACA;;;;;;;"}