{"version": 3, "file": "SentryNodeFetchInstrumentation.js", "sources": ["../../../../src/integrations/node-fetch/SentryNodeFetchInstrumentation.ts"], "sourcesContent": ["import { context } from '@opentelemetry/api';\nimport { isTracingSuppressed, VERSION } from '@opentelemetry/core';\nimport type { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport { InstrumentationBase } from '@opentelemetry/instrumentation';\nimport type { SanitizedRequestData } from '@sentry/core';\nimport {\n  addBreadcrumb,\n  getBreadcrumbLogLevelFromHttpStatusCode,\n  getClient,\n  getSanitizedUrlString,\n  getTraceData,\n  LRUMap,\n  parseUrl,\n} from '@sentry/core';\nimport { shouldPropagateTraceForUrl } from '@sentry/opentelemetry';\nimport * as diagch from 'diagnostics_channel';\nimport { NODE_MAJOR, NODE_MINOR } from '../../nodeVersion';\nimport { mergeBaggageHeaders } from '../../utils/baggage';\nimport type { UndiciRequest, UndiciResponse } from './types';\n\nconst SENTRY_TRACE_HEADER = 'sentry-trace';\nconst SENTRY_BAGGAGE_HEADER = 'baggage';\n\n// For baggage, we make sure to merge this into a possibly existing header\nconst BAGGAGE_HEADER_REGEX = /baggage: (.*)\\r\\n/;\n\nexport type SentryNodeFetchInstrumentationOptions = InstrumentationConfig & {\n  /**\n   * Whether breadcrumbs should be recorded for requests.\n   *\n   * @default `true`\n   */\n  breadcrumbs?: boolean;\n\n  /**\n   * Do not capture breadcrumbs or inject headers for outgoing fetch requests to URLs where the given callback returns `true`.\n   * The same option can be passed to the top-level httpIntegration where it controls both, breadcrumb and\n   * span creation.\n   *\n   * @param url Contains the entire URL, including query string (if any), protocol, host, etc. of the outgoing request.\n   */\n  ignoreOutgoingRequests?: (url: string) => boolean;\n};\n\ninterface ListenerRecord {\n  name: string;\n  unsubscribe: () => void;\n}\n\n/**\n * This custom node-fetch instrumentation is used to instrument outgoing fetch requests.\n * It does not emit any spans.\n *\n * The reason this is isolated from the OpenTelemetry instrumentation is that users may overwrite this,\n * which would lead to Sentry not working as expected.\n *\n * This is heavily inspired & adapted from:\n * https://github.com/open-telemetry/opentelemetry-js-contrib/blob/28e209a9da36bc4e1f8c2b0db7360170ed46cb80/plugins/node/instrumentation-undici/src/undici.ts\n */\nexport class SentryNodeFetchInstrumentation extends InstrumentationBase<SentryNodeFetchInstrumentationOptions> {\n  // Keep ref to avoid https://github.com/nodejs/node/issues/42170 bug and for\n  // unsubscribing.\n  private _channelSubs: Array<ListenerRecord>;\n  private _propagationDecisionMap: LRUMap<string, boolean>;\n  private _ignoreOutgoingRequestsMap: WeakMap<UndiciRequest, boolean>;\n\n  public constructor(config: SentryNodeFetchInstrumentationOptions = {}) {\n    super('@sentry/instrumentation-node-fetch', VERSION, config);\n    this._channelSubs = [];\n    this._propagationDecisionMap = new LRUMap<string, boolean>(100);\n    this._ignoreOutgoingRequestsMap = new WeakMap<UndiciRequest, boolean>();\n  }\n\n  /** No need to instrument files/modules. */\n  public init(): void {\n    return undefined;\n  }\n\n  /** Disable the instrumentation. */\n  public disable(): void {\n    super.disable();\n    this._channelSubs.forEach(sub => sub.unsubscribe());\n    this._channelSubs = [];\n  }\n\n  /** Enable the instrumentation. */\n  public enable(): void {\n    // \"enabled\" handling is currently a bit messy with InstrumentationBase.\n    // If constructed with `{enabled: false}`, this `.enable()` is still called,\n    // and `this.getConfig().enabled !== this.isEnabled()`, creating confusion.\n    //\n    // For now, this class will setup for instrumenting if `.enable()` is\n    // called, but use `this.getConfig().enabled` to determine if\n    // instrumentation should be generated. This covers the more likely common\n    // case of config being given a construction time, rather than later via\n    // `instance.enable()`, `.disable()`, or `.setConfig()` calls.\n    super.enable();\n\n    // This method is called by the super-class constructor before ours is\n    // called. So we need to ensure the property is initalized.\n    this._channelSubs = this._channelSubs || [];\n\n    // Avoid to duplicate subscriptions\n    if (this._channelSubs.length > 0) {\n      return;\n    }\n\n    this._subscribeToChannel('undici:request:create', this._onRequestCreated.bind(this));\n    this._subscribeToChannel('undici:request:headers', this._onResponseHeaders.bind(this));\n  }\n\n  /**\n   * This method is called when a request is created.\n   * You can still mutate the request here before it is sent.\n   */\n  private _onRequestCreated({ request }: { request: UndiciRequest }): void {\n    const config = this.getConfig();\n    const enabled = config.enabled !== false;\n\n    if (!enabled) {\n      return;\n    }\n\n    const shouldIgnore = this._shouldIgnoreOutgoingRequest(request);\n    // We store this decisision for later so we do not need to re-evaluate it\n    // Additionally, the active context is not correct in _onResponseHeaders, so we need to make sure it is evaluated here\n    this._ignoreOutgoingRequestsMap.set(request, shouldIgnore);\n\n    if (shouldIgnore) {\n      return;\n    }\n\n    const url = getAbsoluteUrl(request.origin, request.path);\n\n    // Manually add the trace headers, if it applies\n    // Note: We do not use `propagation.inject()` here, because our propagator relies on an active span\n    // Which we do not have in this case\n    // The propagator _may_ overwrite this, but this should be fine as it is the same data\n    const tracePropagationTargets = getClient()?.getOptions().tracePropagationTargets;\n    const addedHeaders = shouldPropagateTraceForUrl(url, tracePropagationTargets, this._propagationDecisionMap)\n      ? getTraceData()\n      : undefined;\n\n    if (!addedHeaders) {\n      return;\n    }\n\n    const { 'sentry-trace': sentryTrace, baggage } = addedHeaders;\n\n    // We do not want to overwrite existing headers here\n    // If the core UndiciInstrumentation is registered, it will already have set the headers\n    // We do not want to add any then\n    if (Array.isArray(request.headers)) {\n      const requestHeaders = request.headers;\n\n      // We do not want to overwrite existing header here, if it was already set\n      if (sentryTrace && !requestHeaders.includes(SENTRY_TRACE_HEADER)) {\n        requestHeaders.push(SENTRY_TRACE_HEADER, sentryTrace);\n      }\n\n      // For baggage, we make sure to merge this into a possibly existing header\n      const existingBaggagePos = requestHeaders.findIndex(header => header === SENTRY_BAGGAGE_HEADER);\n      if (baggage && existingBaggagePos === -1) {\n        requestHeaders.push(SENTRY_BAGGAGE_HEADER, baggage);\n      } else if (baggage) {\n        const existingBaggage = requestHeaders[existingBaggagePos + 1];\n        const merged = mergeBaggageHeaders(existingBaggage, baggage);\n        if (merged) {\n          requestHeaders[existingBaggagePos + 1] = merged;\n        }\n      }\n    } else {\n      const requestHeaders = request.headers;\n      // We do not want to overwrite existing header here, if it was already set\n      if (sentryTrace && !requestHeaders.includes(`${SENTRY_TRACE_HEADER}:`)) {\n        request.headers += `${SENTRY_TRACE_HEADER}: ${sentryTrace}\\r\\n`;\n      }\n\n      const existingBaggage = request.headers.match(BAGGAGE_HEADER_REGEX)?.[1];\n      if (baggage && !existingBaggage) {\n        request.headers += `${SENTRY_BAGGAGE_HEADER}: ${baggage}\\r\\n`;\n      } else if (baggage) {\n        const merged = mergeBaggageHeaders(existingBaggage, baggage);\n        if (merged) {\n          request.headers = request.headers.replace(BAGGAGE_HEADER_REGEX, `baggage: ${merged}\\r\\n`);\n        }\n      }\n    }\n  }\n\n  /**\n   * This method is called when a response is received.\n   */\n  private _onResponseHeaders({ request, response }: { request: UndiciRequest; response: UndiciResponse }): void {\n    const config = this.getConfig();\n    const enabled = config.enabled !== false;\n\n    if (!enabled) {\n      return;\n    }\n\n    const _breadcrumbs = config.breadcrumbs;\n    const breadCrumbsEnabled = typeof _breadcrumbs === 'undefined' ? true : _breadcrumbs;\n\n    const shouldIgnore = this._ignoreOutgoingRequestsMap.get(request);\n\n    if (breadCrumbsEnabled && !shouldIgnore) {\n      addRequestBreadcrumb(request, response);\n    }\n  }\n\n  /** Subscribe to a diagnostics channel. */\n  private _subscribeToChannel(\n    diagnosticChannel: string,\n    onMessage: (message: unknown, name: string | symbol) => void,\n  ): void {\n    // `diagnostics_channel` had a ref counting bug until v18.19.0.\n    // https://github.com/nodejs/node/pull/47520\n    const useNewSubscribe = NODE_MAJOR > 18 || (NODE_MAJOR === 18 && NODE_MINOR >= 19);\n\n    let unsubscribe: () => void;\n    if (useNewSubscribe) {\n      diagch.subscribe?.(diagnosticChannel, onMessage);\n      unsubscribe = () => diagch.unsubscribe?.(diagnosticChannel, onMessage);\n    } else {\n      const channel = diagch.channel(diagnosticChannel);\n      channel.subscribe(onMessage);\n      unsubscribe = () => channel.unsubscribe(onMessage);\n    }\n\n    this._channelSubs.push({\n      name: diagnosticChannel,\n      unsubscribe,\n    });\n  }\n\n  /**\n   * Check if the given outgoing request should be ignored.\n   */\n  private _shouldIgnoreOutgoingRequest(request: UndiciRequest): boolean {\n    if (isTracingSuppressed(context.active())) {\n      return true;\n    }\n\n    // Add trace propagation headers\n    const url = getAbsoluteUrl(request.origin, request.path);\n    const ignoreOutgoingRequests = this.getConfig().ignoreOutgoingRequests;\n\n    if (typeof ignoreOutgoingRequests !== 'function' || !url) {\n      return false;\n    }\n\n    return ignoreOutgoingRequests(url);\n  }\n}\n\n/** Add a breadcrumb for outgoing requests. */\nfunction addRequestBreadcrumb(request: UndiciRequest, response: UndiciResponse): void {\n  const data = getBreadcrumbData(request);\n\n  const statusCode = response.statusCode;\n  const level = getBreadcrumbLogLevelFromHttpStatusCode(statusCode);\n\n  addBreadcrumb(\n    {\n      category: 'http',\n      data: {\n        status_code: statusCode,\n        ...data,\n      },\n      type: 'http',\n      level,\n    },\n    {\n      event: 'response',\n      request,\n      response,\n    },\n  );\n}\n\nfunction getBreadcrumbData(request: UndiciRequest): Partial<SanitizedRequestData> {\n  try {\n    const url = getAbsoluteUrl(request.origin, request.path);\n    const parsedUrl = parseUrl(url);\n\n    const data: Partial<SanitizedRequestData> = {\n      url: getSanitizedUrlString(parsedUrl),\n      'http.method': request.method || 'GET',\n    };\n\n    if (parsedUrl.search) {\n      data['http.query'] = parsedUrl.search;\n    }\n    if (parsedUrl.hash) {\n      data['http.fragment'] = parsedUrl.hash;\n    }\n\n    return data;\n  } catch {\n    return {};\n  }\n}\n\nfunction getAbsoluteUrl(origin: string, path: string = '/'): string {\n  try {\n    const url = new URL(path, origin);\n    return url.toString();\n  } catch {\n    // fallback: Construct it on our own\n    const url = `${origin}`;\n\n    if (url.endsWith('/') && path.startsWith('/')) {\n      return `${url}${path.slice(1)}`;\n    }\n\n    if (!url.endsWith('/') && !path.startsWith('/')) {\n      return `${url}/${path.slice(1)}`;\n    }\n\n    return `${url}${path}`;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAoBA,MAAM,mBAAA,GAAsB,cAAc;AAC1C,MAAM,qBAAA,GAAwB,SAAS;;AAEvC;AACA,MAAM,oBAAA,GAAuB,mBAAmB;;AAyBhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,8BAAA,SAAuC,mBAAmB,CAAwC;AAC/G;AACA;;AAKA,GAAS,WAAW,CAAC,MAAM,GAA0C,EAAE,EAAE;AACzE,IAAI,KAAK,CAAC,oCAAoC,EAAE,OAAO,EAAE,MAAM,CAAC;AAChE,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE;AAC1B,IAAI,IAAI,CAAC,uBAAwB,GAAE,IAAI,MAAM,CAAkB,GAAG,CAAC;AACnE,IAAI,IAAI,CAAC,0BAAA,GAA6B,IAAI,OAAO,EAA0B;AAC3E;;AAEA;AACA,GAAS,IAAI,GAAS;AACtB,IAAI,OAAO,SAAS;AACpB;;AAEA;AACA,GAAS,OAAO,GAAS;AACzB,IAAI,KAAK,CAAC,OAAO,EAAE;AACnB,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAI,IAAG,GAAG,CAAC,WAAW,EAAE,CAAC;AACvD,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE;AAC1B;;AAEA;AACA,GAAS,MAAM,GAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,CAAC,MAAM,EAAE;;AAElB;AACA;AACA,IAAI,IAAI,CAAC,YAAa,GAAE,IAAI,CAAC,YAAA,IAAgB,EAAE;;AAE/C;AACA,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAA,GAAS,CAAC,EAAE;AACtC,MAAM;AACN;;AAEA,IAAI,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxF,IAAI,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1F;;AAEA;AACA;AACA;AACA;AACA,GAAU,iBAAiB,CAAC,EAAE,OAAQ,EAAC,EAAoC;AAC3E,IAAI,MAAM,MAAO,GAAE,IAAI,CAAC,SAAS,EAAE;AACnC,IAAI,MAAM,OAAQ,GAAE,MAAM,CAAC,OAAA,KAAY,KAAK;;AAE5C,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM;AACN;;AAEA,IAAI,MAAM,eAAe,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;AACnE;AACA;AACA,IAAI,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;;AAE9D,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM;AACN;;AAEA,IAAI,MAAM,GAAA,GAAM,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC;;AAE5D;AACA;AACA;AACA;AACA,IAAI,MAAM,uBAAwB,GAAE,SAAS,EAAE,EAAE,UAAU,EAAE,CAAC,uBAAuB;AACrF,IAAI,MAAM,YAAa,GAAE,0BAA0B,CAAC,GAAG,EAAE,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;AAC9G,QAAQ,YAAY;AACpB,QAAQ,SAAS;;AAEjB,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM;AACN;;AAEA,IAAI,MAAM,EAAE,cAAc,EAAE,WAAW,EAAE,OAAA,EAAU,GAAE,YAAY;;AAEjE;AACA;AACA;AACA,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACxC,MAAM,MAAM,cAAA,GAAiB,OAAO,CAAC,OAAO;;AAE5C;AACA,MAAM,IAAI,WAAA,IAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;AACxE,QAAQ,cAAc,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC;AAC7D;;AAEA;AACA,MAAM,MAAM,kBAAA,GAAqB,cAAc,CAAC,SAAS,CAAC,MAAA,IAAU,MAAA,KAAW,qBAAqB,CAAC;AACrG,MAAM,IAAI,OAAQ,IAAG,uBAAuB,EAAE,EAAE;AAChD,QAAQ,cAAc,CAAC,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC;AAC3D,OAAQ,MAAK,IAAI,OAAO,EAAE;AAC1B,QAAQ,MAAM,kBAAkB,cAAc,CAAC,kBAAmB,GAAE,CAAC,CAAC;AACtE,QAAQ,MAAM,SAAS,mBAAmB,CAAC,eAAe,EAAE,OAAO,CAAC;AACpE,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,cAAc,CAAC,kBAAA,GAAqB,CAAC,CAAA,GAAI,MAAM;AACzD;AACA;AACA,WAAW;AACX,MAAM,MAAM,cAAA,GAAiB,OAAO,CAAC,OAAO;AAC5C;AACA,MAAM,IAAI,WAAA,IAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,OAAA,IAAA,CAAA,EAAA,mBAAA,CAAA,EAAA,EAAA,WAAA,CAAA,IAAA,CAAA;AACA;;AAEA,MAAA,MAAA,eAAA,GAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,oBAAA,CAAA,GAAA,CAAA,CAAA;AACA,MAAA,IAAA,OAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,OAAA,CAAA,OAAA,IAAA,CAAA,EAAA,qBAAA,CAAA,EAAA,EAAA,OAAA,CAAA,IAAA,CAAA;AACA,OAAA,MAAA,IAAA,OAAA,EAAA;AACA,QAAA,MAAA,MAAA,GAAA,mBAAA,CAAA,eAAA,EAAA,OAAA,CAAA;AACA,QAAA,IAAA,MAAA,EAAA;AACA,UAAA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA,oBAAA,EAAA,CAAA,SAAA,EAAA,MAAA,CAAA,IAAA,CAAA,CAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,QAAA,EAAA,EAAA;AACA,IAAA,MAAA,MAAA,GAAA,IAAA,CAAA,SAAA,EAAA;AACA,IAAA,MAAA,OAAA,GAAA,MAAA,CAAA,OAAA,KAAA,KAAA;;AAEA,IAAA,IAAA,CAAA,OAAA,EAAA;AACA,MAAA;AACA;;AAEA,IAAA,MAAA,YAAA,GAAA,MAAA,CAAA,WAAA;AACA,IAAA,MAAA,kBAAA,GAAA,OAAA,YAAA,KAAA,WAAA,GAAA,IAAA,GAAA,YAAA;;AAEA,IAAA,MAAA,YAAA,GAAA,IAAA,CAAA,0BAAA,CAAA,GAAA,CAAA,OAAA,CAAA;;AAEA,IAAA,IAAA,kBAAA,IAAA,CAAA,YAAA,EAAA;AACA,MAAA,oBAAA,CAAA,OAAA,EAAA,QAAA,CAAA;AACA;AACA;;AAEA;AACA,GAAA,mBAAA;AACA,IAAA,iBAAA;AACA,IAAA,SAAA;AACA,IAAA;AACA;AACA;AACA,IAAA,MAAA,eAAA,GAAA,UAAA,GAAA,EAAA,KAAA,UAAA,KAAA,EAAA,IAAA,UAAA,IAAA,EAAA,CAAA;;AAEA,IAAA,IAAA,WAAA;AACA,IAAA,IAAA,eAAA,EAAA;AACA,MAAA,MAAA,CAAA,SAAA,GAAA,iBAAA,EAAA,SAAA,CAAA;AACA,MAAA,WAAA,GAAA,MAAA,MAAA,CAAA,WAAA,GAAA,iBAAA,EAAA,SAAA,CAAA;AACA,KAAA,MAAA;AACA,MAAA,MAAA,OAAA,GAAA,MAAA,CAAA,OAAA,CAAA,iBAAA,CAAA;AACA,MAAA,OAAA,CAAA,SAAA,CAAA,SAAA,CAAA;AACA,MAAA,WAAA,GAAA,MAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA;AACA;;AAEA,IAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA;AACA,MAAA,IAAA,EAAA,iBAAA;AACA,MAAA,WAAA;AACA,KAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA,GAAA,4BAAA,CAAA,OAAA,EAAA;AACA,IAAA,IAAA,mBAAA,CAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA;AACA,MAAA,OAAA,IAAA;AACA;;AAEA;AACA,IAAA,MAAA,GAAA,GAAA,cAAA,CAAA,OAAA,CAAA,MAAA,EAAA,OAAA,CAAA,IAAA,CAAA;AACA,IAAA,MAAA,sBAAA,GAAA,IAAA,CAAA,SAAA,EAAA,CAAA,sBAAA;;AAEA,IAAA,IAAA,OAAA,sBAAA,KAAA,UAAA,IAAA,CAAA,GAAA,EAAA;AACA,MAAA,OAAA,KAAA;AACA;;AAEA,IAAA,OAAA,sBAAA,CAAA,GAAA,CAAA;AACA;AACA;;AAEA;AACA,SAAA,oBAAA,CAAA,OAAA,EAAA,QAAA,EAAA;AACA,EAAA,MAAA,IAAA,GAAA,iBAAA,CAAA,OAAA,CAAA;;AAEA,EAAA,MAAA,UAAA,GAAA,QAAA,CAAA,UAAA;AACA,EAAA,MAAA,KAAA,GAAA,uCAAA,CAAA,UAAA,CAAA;;AAEA,EAAA,aAAA;AACA,IAAA;AACA,MAAA,QAAA,EAAA,MAAA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,WAAA,EAAA,UAAA;AACA,QAAA,GAAA,IAAA;AACA,OAAA;AACA,MAAA,IAAA,EAAA,MAAA;AACA,MAAA,KAAA;AACA,KAAA;AACA,IAAA;AACA,MAAA,KAAA,EAAA,UAAA;AACA,MAAA,OAAA;AACA,MAAA,QAAA;AACA,KAAA;AACA,GAAA;AACA;;AAEA,SAAA,iBAAA,CAAA,OAAA,EAAA;AACA,EAAA,IAAA;AACA,IAAA,MAAA,GAAA,GAAA,cAAA,CAAA,OAAA,CAAA,MAAA,EAAA,OAAA,CAAA,IAAA,CAAA;AACA,IAAA,MAAA,SAAA,GAAA,QAAA,CAAA,GAAA,CAAA;;AAEA,IAAA,MAAA,IAAA,GAAA;AACA,MAAA,GAAA,EAAA,qBAAA,CAAA,SAAA,CAAA;AACA,MAAA,aAAA,EAAA,OAAA,CAAA,MAAA,IAAA,KAAA;AACA,KAAA;;AAEA,IAAA,IAAA,SAAA,CAAA,MAAA,EAAA;AACA,MAAA,IAAA,CAAA,YAAA,CAAA,GAAA,SAAA,CAAA,MAAA;AACA;AACA,IAAA,IAAA,SAAA,CAAA,IAAA,EAAA;AACA,MAAA,IAAA,CAAA,eAAA,CAAA,GAAA,SAAA,CAAA,IAAA;AACA;;AAEA,IAAA,OAAA,IAAA;AACA,GAAA,CAAA,MAAA;AACA,IAAA,OAAA,EAAA;AACA;AACA;;AAEA,SAAA,cAAA,CAAA,MAAA,EAAA,IAAA,GAAA,GAAA,EAAA;AACA,EAAA,IAAA;AACA,IAAA,MAAA,GAAA,GAAA,IAAA,GAAA,CAAA,IAAA,EAAA,MAAA,CAAA;AACA,IAAA,OAAA,GAAA,CAAA,QAAA,EAAA;AACA,GAAA,CAAA,MAAA;AACA;AACA,IAAA,MAAA,GAAA,GAAA,CAAA,EAAA,MAAA,CAAA,CAAA;;AAEA,IAAA,IAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,MAAA,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AAEA,IAAA,IAAA,CAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,MAAA,OAAA,CAAA,EAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AAEA,IAAA,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA;;;;"}