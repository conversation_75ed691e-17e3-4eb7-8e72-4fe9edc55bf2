{"version": 3, "file": "handlers.js", "sources": ["../../../src/instrument/handlers.ts"], "sourcesContent": ["import { DEBUG_BUILD } from '../debug-build';\nimport { logger } from '../utils/logger';\nimport { getFunctionName } from '../utils/stacktrace';\n\nexport type InstrumentHandlerType =\n  | 'console'\n  | 'dom'\n  | 'fetch'\n  | 'fetch-body-resolved'\n  | 'history'\n  | 'xhr'\n  | 'error'\n  | 'unhandledrejection';\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type InstrumentHandlerCallback = (data: any) => void;\n\n// We keep the handlers globally\nconst handlers: { [key in InstrumentHandlerType]?: InstrumentHandlerCallback[] } = {};\nconst instrumented: { [key in InstrumentHandlerType]?: boolean } = {};\n\n/** Add a handler function. */\nexport function addHandler(type: InstrumentHandlerType, handler: InstrumentHandlerCallback): void {\n  handlers[type] = handlers[type] || [];\n  (handlers[type] as InstrumentHandlerCallback[]).push(handler);\n}\n\n/**\n * Reset all instrumentation handlers.\n * This can be used by tests to ensure we have a clean slate of instrumentation handlers.\n */\nexport function resetInstrumentationHandlers(): void {\n  Object.keys(handlers).forEach(key => {\n    handlers[key as InstrumentHandlerType] = undefined;\n  });\n}\n\n/** Maybe run an instrumentation function, unless it was already called. */\nexport function maybeInstrument(type: InstrumentHandlerType, instrumentFn: () => void): void {\n  if (!instrumented[type]) {\n    instrumented[type] = true;\n    try {\n      instrumentFn();\n    } catch (e) {\n      DEBUG_BUILD && logger.error(`Error while instrumenting ${type}`, e);\n    }\n  }\n}\n\n/** Trigger handlers for a given instrumentation type. */\nexport function triggerHandlers(type: InstrumentHandlerType, data: unknown): void {\n  const typeHandlers = type && handlers[type];\n  if (!typeHandlers) {\n    return;\n  }\n\n  for (const handler of typeHandlers) {\n    try {\n      handler(data);\n    } catch (e) {\n      DEBUG_BUILD &&\n        logger.error(\n          `Error while triggering instrumentation handler.\\nType: ${type}\\nName: ${getFunctionName(handler)}\\nError:`,\n          e,\n        );\n    }\n  }\n}\n"], "names": ["DEBUG_BUILD", "logger", "getFunctionName"], "mappings": ";;;;;;AAgBA;AACA,MAAM,QAAQ,GAAqE,EAAE;AACrF,MAAM,YAAY,GAAiD,EAAE;;AAErE;AACO,SAAS,UAAU,CAAC,IAAI,EAAyB,OAAO,EAAmC;AAClG,EAAE,QAAQ,CAAC,IAAI,CAAE,GAAE,QAAQ,CAAC,IAAI,CAAA,IAAK,EAAE;AACvC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAA,GAAkC,IAAI,CAAC,OAAO,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACO,SAAS,4BAA4B,GAAS;AACrD,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAA,IAAO;AACvC,IAAI,QAAQ,CAAC,GAAI,EAAA,GAA4B,SAAS;AACtD,GAAG,CAAC;AACJ;;AAEA;AACO,SAAS,eAAe,CAAC,IAAI,EAAyB,YAAY,EAAoB;AAC7F,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;AAC3B,IAAI,YAAY,CAAC,IAAI,CAAA,GAAI,IAAI;AAC7B,IAAI,IAAI;AACR,MAAM,YAAY,EAAE;AACpB,KAAM,CAAA,OAAO,CAAC,EAAE;AAChB,MAAMA,sBAAA,IAAeC,aAAM,CAAC,KAAK,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAA,EAAA,CAAA,CAAA;AACA;AACA;AACA;;AAEA;AACA,SAAA,eAAA,CAAA,IAAA,EAAA,IAAA,EAAA;AACA,EAAA,MAAA,YAAA,GAAA,IAAA,IAAA,QAAA,CAAA,IAAA,CAAA;AACA,EAAA,IAAA,CAAA,YAAA,EAAA;AACA,IAAA;AACA;;AAEA,EAAA,KAAA,MAAA,OAAA,IAAA,YAAA,EAAA;AACA,IAAA,IAAA;AACA,MAAA,OAAA,CAAA,IAAA,CAAA;AACA,KAAA,CAAA,OAAA,CAAA,EAAA;AACA,MAAAD,sBAAA;AACA,QAAAC,aAAA,CAAA,KAAA;AACA,UAAA,CAAA,uDAAA,EAAA,IAAA,CAAA,QAAA,EAAAC,0BAAA,CAAA,OAAA,CAAA,CAAA,QAAA,CAAA;AACA,UAAA,CAAA;AACA,SAAA;AACA;AACA;AACA;;;;;;;"}