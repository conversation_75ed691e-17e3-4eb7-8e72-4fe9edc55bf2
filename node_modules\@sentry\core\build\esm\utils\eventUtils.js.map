{"version": 3, "file": "eventUtils.js", "sources": ["../../../src/utils/eventUtils.ts"], "sourcesContent": ["import type { Event } from '../types-hoist/event';\n\n/**\n * Get a list of possible event messages from a Sentry event.\n */\nexport function getPossibleEventMessages(event: Event): string[] {\n  const possibleMessages: string[] = [];\n\n  if (event.message) {\n    possibleMessages.push(event.message);\n  }\n\n  try {\n    // @ts-expect-error Try catching to save bundle size\n    const lastException = event.exception.values[event.exception.values.length - 1];\n    if (lastException?.value) {\n      possibleMessages.push(lastException.value);\n      if (lastException.type) {\n        possibleMessages.push(`${lastException.type}: ${lastException.value}`);\n      }\n    }\n  } catch (e) {\n    // ignore errors here\n  }\n\n  return possibleMessages;\n}\n"], "names": [], "mappings": "AAEA;AACA;AACA;AACO,SAAS,wBAAwB,CAAC,KAAK,EAAmB;AACjE,EAAE,MAAM,gBAAgB,GAAa,EAAE;;AAEvC,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACxC;;AAEA,EAAE,IAAI;AACN;AACA,IAAI,MAAM,aAAc,GAAE,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,MAAO,GAAE,CAAC,CAAC;AACnF,IAAI,IAAI,aAAa,EAAE,KAAK,EAAE;AAC9B,MAAM,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;AAChD,MAAM,IAAI,aAAa,CAAC,IAAI,EAAE;AAC9B,QAAQ,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAA,aAAA,CAAA,IAAA,CAAA,EAAA,EAAA,aAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA;AACA;AACA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA;AACA;;AAEA,EAAA,OAAA,gBAAA;AACA;;;;"}