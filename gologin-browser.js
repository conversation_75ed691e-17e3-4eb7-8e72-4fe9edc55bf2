const { GoLogin } = require('gologin');
const puppeteer = require('puppeteer-core');
const https = require('https');
const http = require('http');

let browser;
let page;
let gologin;

const GOLOGIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2ODU3ZjJhNDk2NzMyYTJjNjUwNzA0MTIiLCJ0eXBlIjoiZGV2Iiwiand0aWQiOiI2ODU4MjRkNjg5ZTc0ZWNiMGRhMjFhZTAifQ.ZjeVBKq61ox9NyBPfAOZE0n7EUszWywZNfG0_zonY_8';

async function initializeGologinBrowser(profileId = '68611990b897afb56f5c0689') {
    try {
        // Đóng browser cũ nếu có
        if (browser && browser.isConnected()) {
            console.log('Đóng trình duyệt GoLogin cũ...');
            try {
                await browser.close();
                if (gologin) {
                    await gologin.stop();
                }
            } catch (error) {
                console.log('Lỗi khi đóng trình duyệt cũ:', error.message);
            }
        }

        console.log('<PERSON>ang kết nối với GoLogin Local API...');

        // Thử các endpoint khác nhau
        let data;
        try {
            // Thử endpoint v1
            data = await makeHttpRequest(`http://localhost:36912/v1/browser/${profileId}`, 'POST');
        } catch (error) {
            try {
                // Thử endpoint v2
                data = await makeHttpRequest(`http://localhost:36912/v2/browser/${profileId}/start`, 'POST');
            } catch (error2) {
                // Thử endpoint đơn giản
                data = await makeHttpRequest(`http://localhost:36912/start/${profileId}`, 'POST');
            }
        }
        console.log('GoLogin response:', data);

        if (!data.wsUrl) {
            throw new Error('Không nhận được WebSocket URL từ GoLogin');
        }

        console.log('Kết nối với trình duyệt GoLogin...');
        browser = await puppeteer.connect({
            browserWSEndpoint: data.wsUrl,
            ignoreHTTPSErrors: true,
        });
        
        if (status !== 'success') {
            throw new Error(`GoLogin không thể khởi động: ${status}`);
        }

        console.log('Kết nối với trình duyệt GoLogin...');
        browser = await puppeteer.connect({
            browserWSEndpoint: wsUrl,
            ignoreHTTPSErrors: true,
        });

        // Lấy page đầu tiên hoặc tạo mới
        const pages = await browser.pages();
        if (pages.length > 0) {
            page = pages[0];
        } else {
            page = await browser.newPage();
        }

        // Set viewport
        await page.setViewport({
            width: 1366,
            height: 768
        });

        console.log('✓ GoLogin browser đã khởi tạo thành công');
        console.log('Profile ID:', gologin.profile_id);

        return { browser, page, gologin };

    } catch (error) {
        console.error('Lỗi khởi tạo GoLogin:', error.message);
        throw error;
    }
}

async function getChromePath() {
    const { executablePath } = require('puppeteer');
    try {
        return executablePath();
    } catch (error) {
        // Fallback paths
        const paths = [
            'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
            'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
        ];
        
        const fs = require('fs');
        for (const path of paths) {
            if (fs.existsSync(path)) {
                return path;
            }
        }
        
        throw new Error('Không tìm thấy Chrome executable');
    }
}

async function closeBrowser() {
    try {
        if (browser && browser.isConnected()) {
            await browser.close();
        }
        if (gologin) {
            await gologin.stop();
        }
        console.log('✓ Đã đóng GoLogin browser');
    } catch (error) {
        console.error('Lỗi khi đóng browser:', error.message);
    }
}

function getBrowser() {
    return browser;
}

function getPage() {
    return page;
}

function getGologin() {
    return gologin;
}

// Hàm HTTP request đơn giản
function makeHttpRequest(url, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port,
            path: urlObj.pathname,
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const req = http.request(options, (res) => {
            let responseData = '';
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(responseData);
                    resolve(jsonData);
                } catch (error) {
                    resolve(responseData);
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

// Hàm sleep helper
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

module.exports = {
    initializeGologinBrowser,
    closeBrowser,
    getBrowser,
    getPage,
    getGologin,
    sleep
};
