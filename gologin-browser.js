const { GoLogin } = require('gologin');
const puppeteer = require('puppeteer-core');
const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

let browser;
let page;
let gologin;

const GOLOGIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2ODY5ZWZiYmJhZWFhYmU0ZjNjYmU1NjAiLCJ0eXBlIjoiZGV2Iiwiand0aWQiOiI2ODY5ZjA2MmM2ZDBmMmM2MmQ4YTliYWYifQ.8-pL-goXMs-7e_H7Rb3CcEh017ztWiyoMgqKX8yZgOo';
const PROFILE_ID = '6869eff066690ae8297cac35';

// Đường dẫn lưu user data
const USER_DATA_DIR = path.join(__dirname, 'user-data');
const SESSION_FILE = path.join(USER_DATA_DIR, 'canva-session.json');

// Tạo thư mục user-data nếu chưa có
if (!fs.existsSync(USER_DATA_DIR)) {
    fs.mkdirSync(USER_DATA_DIR, { recursive: true });
    console.log('✓ Đã tạo thư mục user-data');
}

// Hàm lưu session data
async function saveSessionData(page) {
    try {
        console.log('Đang lưu session data...');

        // Lấy cookies
        const cookies = await page.cookies();

        // Lấy localStorage
        const localStorage = await page.evaluate(() => {
            const data = {};
            for (let i = 0; i < window.localStorage.length; i++) {
                const key = window.localStorage.key(i);
                data[key] = window.localStorage.getItem(key);
            }
            return data;
        });

        // Lấy sessionStorage
        const sessionStorage = await page.evaluate(() => {
            const data = {};
            for (let i = 0; i < window.sessionStorage.length; i++) {
                const key = window.sessionStorage.key(i);
                data[key] = window.sessionStorage.getItem(key);
            }
            return data;
        });

        const sessionData = {
            cookies,
            localStorage,
            sessionStorage,
            timestamp: Date.now(),
            url: await page.url()
        };

        fs.writeFileSync(SESSION_FILE, JSON.stringify(sessionData, null, 2));
        console.log('✓ Đã lưu session data thành công');

    } catch (error) {
        console.log('Lỗi khi lưu session data:', error.message);
    }
}

// Hàm khôi phục session data
async function restoreSessionData(page) {
    try {
        if (!fs.existsSync(SESSION_FILE)) {
            console.log('Không tìm thấy session data đã lưu');
            return false;
        }

        console.log('Đang khôi phục session data...');
        const sessionData = JSON.parse(fs.readFileSync(SESSION_FILE, 'utf8'));

        // Kiểm tra session có quá cũ không (7 ngày)
        const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 ngày
        if (Date.now() - sessionData.timestamp > maxAge) {
            console.log('Session data đã quá cũ, bỏ qua khôi phục');
            return false;
        }

        // Điều hướng đến trang Canva trước
        await page.goto('https://www.canva.com', { waitUntil: 'networkidle0' });

        // Khôi phục cookies
        if (sessionData.cookies && sessionData.cookies.length > 0) {
            await page.setCookie(...sessionData.cookies);
            console.log('✓ Đã khôi phục cookies');
        }

        // Khôi phục localStorage
        if (sessionData.localStorage) {
            await page.evaluate((data) => {
                for (const [key, value] of Object.entries(data)) {
                    window.localStorage.setItem(key, value);
                }
            }, sessionData.localStorage);
            console.log('✓ Đã khôi phục localStorage');
        }

        // Khôi phục sessionStorage
        if (sessionData.sessionStorage) {
            await page.evaluate((data) => {
                for (const [key, value] of Object.entries(data)) {
                    window.sessionStorage.setItem(key, value);
                }
            }, sessionData.sessionStorage);
            console.log('✓ Đã khôi phục sessionStorage');
        }

        // Refresh trang để áp dụng session
        await page.reload({ waitUntil: 'networkidle0' });
        console.log('✓ Đã khôi phục session data thành công');

        return true;

    } catch (error) {
        console.log('Lỗi khi khôi phục session data:', error.message);
        return false;
    }
}

// Hàm xóa session data cũ
function clearSessionData() {
    try {
        if (fs.existsSync(SESSION_FILE)) {
            fs.unlinkSync(SESSION_FILE);
            console.log('✓ Đã xóa session data cũ');
        }
    } catch (error) {
        console.log('Lỗi khi xóa session data:', error.message);
    }
}

async function initializeGologinBrowser(profileId = PROFILE_ID) {
    try {
        // Đóng browser cũ nếu có
        if (browser && browser.isConnected()) {
            console.log('Đóng trình duyệt GoLogin cũ...');
            try {
                await browser.close();
                if (gologin) {
                    await gologin.stop();
                }
            } catch (error) {
                console.log('Lỗi khi đóng trình duyệt cũ:', error.message);
            }
        }

        console.log('Đang khởi tạo GoLogin với SDK...');

        // Sử dụng GoLogin SDK với cấu hình đơn giản
        gologin = new GoLogin({
            token: GOLOGIN_TOKEN,
            profile_id: profileId,
            executablePath: await getChromePath(),
            skipOrbitaHashChecking: true,
            skipFontsChecking: true,
            skipFontsLoading: true,
        });

        console.log('Đang khởi động profile GoLogin...');
        const { status, wsUrl } = await gologin.start();

        if (status !== 'success') {
            throw new Error(`GoLogin không thể khởi động: ${status}`);
        }

        console.log('Kết nối với trình duyệt GoLogin...');
        browser = await puppeteer.connect({
            browserWSEndpoint: wsUrl,
            ignoreHTTPSErrors: true,
        });
        
        if (status !== 'success') {
            throw new Error(`GoLogin không thể khởi động: ${status}`);
        }

        console.log('Kết nối với trình duyệt GoLogin...');
        browser = await puppeteer.connect({
            browserWSEndpoint: wsUrl,
            ignoreHTTPSErrors: true,
        });

        // Lấy page đầu tiên hoặc tạo mới
        const pages = await browser.pages();
        if (pages.length > 0) {
            page = pages[0];
        } else {
            page = await browser.newPage();
        }

        // Set viewport
        await page.setViewport({
            width: 1366,
            height: 768
        });

        // Thử khôi phục session data nếu có
        console.log('Kiểm tra session data đã lưu...');
        const sessionRestored = await restoreSessionData(page);

        if (sessionRestored) {
            console.log('✓ Đã khôi phục session data thành công');
        } else {
            console.log('Không có session data hoặc không thể khôi phục');
        }

        console.log('✓ GoLogin browser đã khởi tạo thành công');
        console.log('Profile ID:', gologin.profile_id);

        return { browser, page, gologin };

    } catch (error) {
        console.error('Lỗi khởi tạo GoLogin:', error.message);
        throw error;
    }
}

async function getChromePath() {
    const { executablePath } = require('puppeteer');
    try {
        return executablePath();
    } catch (error) {
        // Fallback paths
        const paths = [
            'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
            'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
        ];
        
        const fs = require('fs');
        for (const path of paths) {
            if (fs.existsSync(path)) {
                return path;
            }
        }
        
        throw new Error('Không tìm thấy Chrome executable');
    }
}

async function closeBrowser() {
    try {
        // Lưu session data trước khi đóng
        if (page && browser && browser.isConnected()) {
            console.log('Lưu session data trước khi đóng...');
            await saveSessionData(page);
        }

        if (browser && browser.isConnected()) {
            await browser.close();
        }
        if (gologin) {
            await gologin.stop();
        }
        console.log('✓ Đã đóng GoLogin browser');
    } catch (error) {
        console.error('Lỗi khi đóng browser:', error.message);
    }
}

function getBrowser() {
    return browser;
}

function getPage() {
    return page;
}

function getGologin() {
    return gologin;
}

// Hàm HTTP request đơn giản
function makeHttpRequest(url, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port,
            path: urlObj.pathname,
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const req = http.request(options, (res) => {
            let responseData = '';
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(responseData);
                    resolve(jsonData);
                } catch (error) {
                    resolve(responseData);
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

// Hàm sleep helper
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

module.exports = {
    initializeGologinBrowser,
    closeBrowser,
    getBrowser,
    getPage,
    getGologin,
    saveSessionData,
    restoreSessionData,
    clearSessionData,
    sleep
};
