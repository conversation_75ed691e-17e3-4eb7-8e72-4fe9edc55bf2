Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

/**
 * This symbol is used to mark express layer as being already instrumented
 * since its possible to use a given layer multiple times (ex: middlewares)
 */
const kLayerPatched = Symbol('express-layer-patched');

/**
 * This const define where on the `request` object the Instrumentation will mount the
 * current stack of express layer.
 *
 * It is necessary because express doesn't store the different layers
 * (ie: middleware, router etc) that it called to get to the current layer.
 * Given that, the only way to know the route of a given layer is to
 * store the path of where each previous layer has been mounted.
 *
 * ex: bodyParser > auth middleware > /users router > get /:id
 *  in this case the stack would be: ["/users", "/:id"]
 *
 * ex2: bodyParser > /api router > /v1 router > /users router > get /:id
 *  stack: ["/api", "/v1", "/users", ":id"]
 *
 */
const _LAYERS_STORE_PROPERTY = '__ot_middlewares';

exports._LAYERS_STORE_PROPERTY = _LAYERS_STORE_PROPERTY;
exports.kLayerPatched = kLayerPatched;
//# sourceMappingURL=internal-types.js.map
