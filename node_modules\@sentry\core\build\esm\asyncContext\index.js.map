{"version": 3, "file": "index.js", "sources": ["../../../src/asyncContext/index.ts"], "sourcesContent": ["import type { Carrier } from './../carrier';\nimport { getMainCarrier, getSentryCarrier } from './../carrier';\nimport { getStackAsyncContextStrategy } from './stackStrategy';\nimport type { AsyncContextStrategy } from './types';\n\n/**\n * @private Private API with no semver guarantees!\n *\n * Sets the global async context strategy\n */\nexport function setAsyncContextStrategy(strategy: AsyncContextStrategy | undefined): void {\n  // Get main carrier (global for every environment)\n  const registry = getMainCarrier();\n  const sentry = getSentryCarrier(registry);\n  sentry.acs = strategy;\n}\n\n/**\n * Get the current async context strategy.\n * If none has been setup, the default will be used.\n */\nexport function getAsyncContextStrategy(carrier: Carrier): AsyncContextStrategy {\n  const sentry = getSentryCarrier(carrier);\n\n  if (sentry.acs) {\n    return sentry.acs;\n  }\n\n  // Otherwise, use the default one (stack)\n  return getStackAsyncContextStrategy();\n}\n"], "names": [], "mappings": ";;;AAKA;AACA;AACA;AACA;AACA;AACO,SAAS,uBAAuB,CAAC,QAAQ,EAA0C;AAC1F;AACA,EAAE,MAAM,QAAA,GAAW,cAAc,EAAE;AACnC,EAAE,MAAM,MAAO,GAAE,gBAAgB,CAAC,QAAQ,CAAC;AAC3C,EAAE,MAAM,CAAC,GAAI,GAAE,QAAQ;AACvB;;AAEA;AACA;AACA;AACA;AACO,SAAS,uBAAuB,CAAC,OAAO,EAAiC;AAChF,EAAE,MAAM,MAAO,GAAE,gBAAgB,CAAC,OAAO,CAAC;;AAE1C,EAAE,IAAI,MAAM,CAAC,GAAG,EAAE;AAClB,IAAI,OAAO,MAAM,CAAC,GAAG;AACrB;;AAEA;AACA,EAAE,OAAO,4BAA4B,EAAE;AACvC;;;;"}