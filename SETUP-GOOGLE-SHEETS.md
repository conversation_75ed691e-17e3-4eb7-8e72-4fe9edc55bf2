# 🔧 Hướng dẫn Setup Google Sheets Integration

## 📋 <PERSON><PERSON><PERSON> bước cần thực hiện:

### 1. Tạo Google Cloud Project
1. Truy cập: https://console.cloud.google.com/
2. Tạo project mới hoặc chọn project có sẵn
3. Tên project: `canva-automation` (hoặc tên bạn muốn)

### 2. Bật Google Sheets API
1. Vào **APIs & Services** > **Library**
2. Tìm kiếm "Google Sheets API"
3. Click **Enable**

### 3. Tạo Service Account
1. Vào **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **Service Account**
3. Tên: `canva-sheets-service`
4. Click **Create and Continue**
5. Role: **Editor** (hoặc **Owner**)
6. Click **Done**

### 4. Tạo JSON Key
1. Click vào Service Account vừa tạo
2. Tab **Keys** > **Add Key** > **Create New Key**
3. Chọn **JSON** > **Create**
4. File JSON sẽ được download về máy
5. **Đổi tên file thành `google-credentials.json`**
6. **Copy file vào thư mục gốc của project**

### 5. Tạo Google Sheet
1. Tạo Google Sheet mới: https://sheets.google.com
2. Tên sheet: `Canva Members Log`
3. Tạo 2 sheet tabs:
   - `1 Month` - cho thành viên 1 tháng
   - `1 Year` - cho thành viên 1 năm
4. Header row cho mỗi sheet:
   ```
   Email | Date Added | Duration | Status
   ```

### 6. Share Sheet với Service Account
1. Mở file `google-credentials.json`
2. Copy email từ trường `client_email`
3. Share Google Sheet với email đó (quyền **Editor**)

### 7. Lấy Sheet ID
1. Mở Google Sheet
2. Copy ID từ URL: `https://docs.google.com/spreadsheets/d/{SHEET_ID}/edit`
3. Thêm vào file `.env`:
   ```
   GOOGLE_SHEET_ID=your_sheet_id_here
   ```

## 📁 Cấu trúc files cần có:

```
canva-automation/
├── google-credentials.json    ← File JSON từ Google Cloud
├── .env                      ← Chứa GOOGLE_SHEET_ID
├── google-sheets.js          ← Code quản lý Google Sheets
├── queue-manager.js          ← Code quản lý hàng đợi
└── app.js                    ← Main application
```

## 🚀 Cách sử dụng sau khi setup:

### API Endpoints mới:

1. **Thêm member 1 tháng:**
   ```
   GET http://localhost:3000/addmail1m?email=<EMAIL>
   ```

2. **Thêm member 1 năm:**
   ```
   GET http://localhost:3000/addmail1y?email=<EMAIL>
   ```

3. **Xem trạng thái hàng đợi:**
   ```
   GET http://localhost:3000/queue-status
   ```

4. **Xem thống kê Google Sheets:**
   ```
   GET http://localhost:3000/sheets-stats
   ```

## ✨ Tính năng mới:

- ✅ **Hàng đợi tuần tự**: Tất cả requests được xử lý theo thứ tự
- ✅ **Tốc độ gõ như người thật**: Email được nhập với tốc độ ngẫu nhiên 80-200ms/ký tự
- ✅ **Ghi log tự động**: Mỗi email được ghi vào Google Sheets với timestamp
- ✅ **Phân loại theo thời gian**: 1 tháng và 1 năm được ghi vào sheet riêng
- ✅ **Retry mechanism**: Tự động thử lại 3 lần nếu thất bại
- ✅ **Real-time status**: Theo dõi trạng thái hàng đợi real-time

## 🔍 Troubleshooting:

1. **Lỗi "google-credentials.json not found"**:
   - Đảm bảo file JSON đã được đổi tên đúng
   - File phải ở thư mục gốc của project

2. **Lỗi "Permission denied"**:
   - Kiểm tra đã share Sheet với service account email chưa
   - Đảm bảo quyền là Editor hoặc Owner

3. **Lỗi "Sheet not found"**:
   - Kiểm tra GOOGLE_SHEET_ID trong .env
   - Đảm bảo Sheet có 2 tabs: "1 Month" và "1 Year"

## 📞 Cần hỗ trợ?

Nếu gặp vấn đề, hãy gửi:
1. File `google-credentials.json` (nội dung)
2. Google Sheet ID
3. Log lỗi từ console
