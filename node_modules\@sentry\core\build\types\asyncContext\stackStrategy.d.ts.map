{"version": 3, "file": "stackStrategy.d.ts", "sourceRoot": "", "sources": ["../../../src/asyncContext/stackStrategy.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAExC,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAGjC,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,SAAS,CAAC;AAEpD,UAAU,KAAK;IACb,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,KAAK,CAAC;CACd;AAED;;GAEG;AACH,qBAAa,iBAAiB;IAC5B,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAsB;IAC7C,OAAO,CAAC,eAAe,CAAQ;gBAEZ,KAAK,CAAC,EAAE,KAAK,EAAE,cAAc,CAAC,EAAE,KAAK;IAoBxD;;OAEG;IACI,SAAS,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC;IA6BrD;;OAEG;IACI,SAAS,CAAC,CAAC,SAAS,MAAM,KAAK,CAAC,GAAG,SAAS;IAInD;;OAEG;IACI,QAAQ,IAAI,KAAK;IAIxB;;OAEG;IACI,iBAAiB,IAAI,KAAK;IAIjC;;OAEG;IACI,WAAW,IAAI,KAAK;IAI3B;;OAEG;IACH,OAAO,CAAC,UAAU;IAUlB;;OAEG;IACH,OAAO,CAAC,SAAS;CAIlB;AA+BD;;GAEG;AACH,wBAAgB,4BAA4B,IAAI,oBAAoB,CAWnE"}