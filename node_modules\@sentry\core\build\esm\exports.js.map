{"version": 3, "file": "exports.js", "sources": ["../../src/exports.ts"], "sourcesContent": ["import { getClient, getCurrentScope, getIsolationScope, withIsolationScope } from './currentScopes';\nimport { DEBUG_BUILD } from './debug-build';\nimport type { CaptureContext } from './scope';\nimport { closeSession, makeSession, updateSession } from './session';\nimport type { CheckIn, FinishedCheckIn, MonitorConfig } from './types-hoist/checkin';\nimport type { Event, EventHint } from './types-hoist/event';\nimport type { EventProcessor } from './types-hoist/eventprocessor';\nimport type { Extra, Extras } from './types-hoist/extra';\nimport type { Primitive } from './types-hoist/misc';\nimport type { Session, SessionContext } from './types-hoist/session';\nimport type { SeverityLevel } from './types-hoist/severity';\nimport type { User } from './types-hoist/user';\nimport { isThenable } from './utils/is';\nimport { logger } from './utils/logger';\nimport { uuid4 } from './utils/misc';\nimport type { ExclusiveEventHintOrCaptureContext } from './utils/prepareEvent';\nimport { parseEventHintOrCaptureContext } from './utils/prepareEvent';\nimport { timestampInSeconds } from './utils/time';\nimport { GLOBAL_OBJ } from './utils/worldwide';\n\n/**\n * Captures an exception event and sends it to Sentry.\n *\n * @param exception The exception to capture.\n * @param hint Optional additional data to attach to the Sentry event.\n * @returns the id of the captured Sentry event.\n */\nexport function captureException(exception: unknown, hint?: ExclusiveEventHintOrCaptureContext): string {\n  return getCurrentScope().captureException(exception, parseEventHintOrCaptureContext(hint));\n}\n\n/**\n * Captures a message event and sends it to Sentry.\n *\n * @param message The message to send to Sentry.\n * @param captureContext Define the level of the message or pass in additional data to attach to the message.\n * @returns the id of the captured message.\n */\nexport function captureMessage(message: string, captureContext?: CaptureContext | SeverityLevel): string {\n  // This is necessary to provide explicit scopes upgrade, without changing the original\n  // arity of the `captureMessage(message, level)` method.\n  const level = typeof captureContext === 'string' ? captureContext : undefined;\n  const context = typeof captureContext !== 'string' ? { captureContext } : undefined;\n  return getCurrentScope().captureMessage(message, level, context);\n}\n\n/**\n * Captures a manually created event and sends it to Sentry.\n *\n * @param event The event to send to Sentry.\n * @param hint Optional additional data to attach to the Sentry event.\n * @returns the id of the captured event.\n */\nexport function captureEvent(event: Event, hint?: EventHint): string {\n  return getCurrentScope().captureEvent(event, hint);\n}\n\n/**\n * Sets context data with the given name.\n * @param name of the context\n * @param context Any kind of data. This data will be normalized.\n */\nexport function setContext(name: string, context: { [key: string]: unknown } | null): void {\n  getIsolationScope().setContext(name, context);\n}\n\n/**\n * Set an object that will be merged sent as extra data with the event.\n * @param extras Extras object to merge into current context.\n */\nexport function setExtras(extras: Extras): void {\n  getIsolationScope().setExtras(extras);\n}\n\n/**\n * Set key:value that will be sent as extra data with the event.\n * @param key String of extra\n * @param extra Any kind of data. This data will be normalized.\n */\nexport function setExtra(key: string, extra: Extra): void {\n  getIsolationScope().setExtra(key, extra);\n}\n\n/**\n * Set an object that will be merged sent as tags data with the event.\n * @param tags Tags context object to merge into current context.\n */\nexport function setTags(tags: { [key: string]: Primitive }): void {\n  getIsolationScope().setTags(tags);\n}\n\n/**\n * Set key:value that will be sent as tags data with the event.\n *\n * Can also be used to unset a tag, by passing `undefined`.\n *\n * @param key String key of tag\n * @param value Value of tag\n */\nexport function setTag(key: string, value: Primitive): void {\n  getIsolationScope().setTag(key, value);\n}\n\n/**\n * Updates user context information for future events.\n *\n * @param user User context object to be set in the current context. Pass `null` to unset the user.\n */\nexport function setUser(user: User | null): void {\n  getIsolationScope().setUser(user);\n}\n\n/**\n * The last error event id of the isolation scope.\n *\n * Warning: This function really returns the last recorded error event id on the current\n * isolation scope. If you call this function after handling a certain error and another error\n * is captured in between, the last one is returned instead of the one you might expect.\n * Also, ids of events that were never sent to Sentry (for example because\n * they were dropped in `beforeSend`) could be returned.\n *\n * @returns The last event id of the isolation scope.\n */\nexport function lastEventId(): string | undefined {\n  return getIsolationScope().lastEventId();\n}\n\n/**\n * Create a cron monitor check in and send it to Sentry.\n *\n * @param checkIn An object that describes a check in.\n * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n * to create a monitor automatically when sending a check in.\n */\nexport function captureCheckIn(checkIn: CheckIn, upsertMonitorConfig?: MonitorConfig): string {\n  const scope = getCurrentScope();\n  const client = getClient();\n  if (!client) {\n    DEBUG_BUILD && logger.warn('Cannot capture check-in. No client defined.');\n  } else if (!client.captureCheckIn) {\n    DEBUG_BUILD && logger.warn('Cannot capture check-in. Client does not support sending check-ins.');\n  } else {\n    return client.captureCheckIn(checkIn, upsertMonitorConfig, scope);\n  }\n\n  return uuid4();\n}\n\n/**\n * Wraps a callback with a cron monitor check in. The check in will be sent to Sentry when the callback finishes.\n *\n * @param monitorSlug The distinct slug of the monitor.\n * @param callback Callback to be monitored\n * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n * to create a monitor automatically when sending a check in.\n */\nexport function withMonitor<T>(\n  monitorSlug: CheckIn['monitorSlug'],\n  callback: () => T,\n  upsertMonitorConfig?: MonitorConfig,\n): T {\n  const checkInId = captureCheckIn({ monitorSlug, status: 'in_progress' }, upsertMonitorConfig);\n  const now = timestampInSeconds();\n\n  function finishCheckIn(status: FinishedCheckIn['status']): void {\n    captureCheckIn({ monitorSlug, status, checkInId, duration: timestampInSeconds() - now });\n  }\n\n  return withIsolationScope(() => {\n    let maybePromiseResult: T;\n    try {\n      maybePromiseResult = callback();\n    } catch (e) {\n      finishCheckIn('error');\n      throw e;\n    }\n\n    if (isThenable(maybePromiseResult)) {\n      return maybePromiseResult.then(\n        r => {\n          finishCheckIn('ok');\n          return r;\n        },\n        e => {\n          finishCheckIn('error');\n          throw e;\n        },\n      ) as T;\n    }\n    finishCheckIn('ok');\n\n    return maybePromiseResult;\n  });\n}\n\n/**\n * Call `flush()` on the current client, if there is one. See {@link Client.flush}.\n *\n * @param timeout Maximum time in ms the client should wait to flush its event queue. Omitting this parameter will cause\n * the client to wait until all events are sent before resolving the promise.\n * @returns A promise which resolves to `true` if the queue successfully drains before the timeout, or `false` if it\n * doesn't (or if there's no client defined).\n */\nexport async function flush(timeout?: number): Promise<boolean> {\n  const client = getClient();\n  if (client) {\n    return client.flush(timeout);\n  }\n  DEBUG_BUILD && logger.warn('Cannot flush events. No client defined.');\n  return Promise.resolve(false);\n}\n\n/**\n * Call `close()` on the current client, if there is one. See {@link Client.close}.\n *\n * @param timeout Maximum time in ms the client should wait to flush its event queue before shutting down. Omitting this\n * parameter will cause the client to wait until all events are sent before disabling itself.\n * @returns A promise which resolves to `true` if the queue successfully drains before the timeout, or `false` if it\n * doesn't (or if there's no client defined).\n */\nexport async function close(timeout?: number): Promise<boolean> {\n  const client = getClient();\n  if (client) {\n    return client.close(timeout);\n  }\n  DEBUG_BUILD && logger.warn('Cannot flush events and disable SDK. No client defined.');\n  return Promise.resolve(false);\n}\n\n/**\n * Returns true if Sentry has been properly initialized.\n */\nexport function isInitialized(): boolean {\n  return !!getClient();\n}\n\n/** If the SDK is initialized & enabled. */\nexport function isEnabled(): boolean {\n  const client = getClient();\n  return client?.getOptions().enabled !== false && !!client?.getTransport();\n}\n\n/**\n * Add an event processor.\n * This will be added to the current isolation scope, ensuring any event that is processed in the current execution\n * context will have the processor applied.\n */\nexport function addEventProcessor(callback: EventProcessor): void {\n  getIsolationScope().addEventProcessor(callback);\n}\n\n/**\n * Start a session on the current isolation scope.\n *\n * @param context (optional) additional properties to be applied to the returned session object\n *\n * @returns the new active session\n */\nexport function startSession(context?: SessionContext): Session {\n  const isolationScope = getIsolationScope();\n  const currentScope = getCurrentScope();\n\n  // Will fetch userAgent if called from browser sdk\n  const { userAgent } = GLOBAL_OBJ.navigator || {};\n\n  const session = makeSession({\n    user: currentScope.getUser() || isolationScope.getUser(),\n    ...(userAgent && { userAgent }),\n    ...context,\n  });\n\n  // End existing session if there's one\n  const currentSession = isolationScope.getSession();\n  if (currentSession?.status === 'ok') {\n    updateSession(currentSession, { status: 'exited' });\n  }\n\n  endSession();\n\n  // Afterwards we set the new session on the scope\n  isolationScope.setSession(session);\n\n  return session;\n}\n\n/**\n * End the session on the current isolation scope.\n */\nexport function endSession(): void {\n  const isolationScope = getIsolationScope();\n  const currentScope = getCurrentScope();\n\n  const session = currentScope.getSession() || isolationScope.getSession();\n  if (session) {\n    closeSession(session);\n  }\n  _sendSessionUpdate();\n\n  // the session is over; take it off of the scope\n  isolationScope.setSession();\n}\n\n/**\n * Sends the current Session on the scope\n */\nfunction _sendSessionUpdate(): void {\n  const isolationScope = getIsolationScope();\n  const client = getClient();\n  const session = isolationScope.getSession();\n  if (session && client) {\n    client.captureSession(session);\n  }\n}\n\n/**\n * Sends the current session on the scope to Sentry\n *\n * @param end If set the session will be marked as exited and removed from the scope.\n *            Defaults to `false`.\n */\nexport function captureSession(end: boolean = false): void {\n  // both send the update and pull the session from the scope\n  if (end) {\n    endSession();\n    return;\n  }\n\n  // only send the update\n  _sendSessionUpdate();\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,SAAS,EAAW,IAAI,EAA+C;AACxG,EAAE,OAAO,eAAe,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,8BAA8B,CAAC,IAAI,CAAC,CAAC;AAC5F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,OAAO,EAAU,cAAc,EAA2C;AACzG;AACA;AACA,EAAE,MAAM,KAAM,GAAE,OAAO,cAAA,KAAmB,QAAS,GAAE,cAAe,GAAE,SAAS;AAC/E,EAAE,MAAM,OAAA,GAAU,OAAO,cAAe,KAAI,QAAS,GAAE,EAAE,cAAA,EAAiB,GAAE,SAAS;AACrF,EAAE,OAAO,eAAe,EAAE,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC;AAClE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAsB;AACrE,EAAE,OAAO,eAAe,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAC,IAAI,EAAU,OAAO,EAA2C;AAC3F,EAAE,iBAAiB,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;AAC/C;;AAEA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAC,MAAM,EAAgB;AAChD,EAAE,iBAAiB,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,GAAG,EAAU,KAAK,EAAe;AAC1D,EAAE,iBAAiB,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC;AAC1C;;AAEA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,IAAI,EAAsC;AAClE,EAAE,iBAAiB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,MAAM,CAAC,GAAG,EAAU,KAAK,EAAmB;AAC5D,EAAE,iBAAiB,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,IAAI,EAAqB;AACjD,EAAE,iBAAiB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,GAAuB;AAClD,EAAE,OAAO,iBAAiB,EAAE,CAAC,WAAW,EAAE;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,OAAO,EAAW,mBAAmB,EAA0B;AAC9F,EAAE,MAAM,KAAA,GAAQ,eAAe,EAAE;AACjC,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE;AAC5B,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,eAAe,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC;AAC7E,GAAE,MAAO,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;AACrC,IAAI,eAAe,MAAM,CAAC,IAAI,CAAC,qEAAqE,CAAC;AACrG,SAAS;AACT,IAAI,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,mBAAmB,EAAE,KAAK,CAAC;AACrE;;AAEA,EAAE,OAAO,KAAK,EAAE;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW;AAC3B,EAAE,WAAW;AACb,EAAE,QAAQ;AACV,EAAE,mBAAmB;AACrB,EAAK;AACL,EAAE,MAAM,SAAA,GAAY,cAAc,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,aAAA,EAAe,EAAE,mBAAmB,CAAC;AAC/F,EAAE,MAAM,GAAA,GAAM,kBAAkB,EAAE;;AAElC,EAAE,SAAS,aAAa,CAAC,MAAM,EAAmC;AAClE,IAAI,cAAc,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,kBAAkB,KAAK,GAAA,EAAK,CAAC;AAC5F;;AAEA,EAAE,OAAO,kBAAkB,CAAC,MAAM;AAClC,IAAI,IAAI,kBAAkB;AAC1B,IAAI,IAAI;AACR,MAAM,kBAAmB,GAAE,QAAQ,EAAE;AACrC,KAAM,CAAA,OAAO,CAAC,EAAE;AAChB,MAAM,aAAa,CAAC,OAAO,CAAC;AAC5B,MAAM,MAAM,CAAC;AACb;;AAEA,IAAI,IAAI,UAAU,CAAC,kBAAkB,CAAC,EAAE;AACxC,MAAM,OAAO,kBAAkB,CAAC,IAAI;AACpC,QAAQ,KAAK;AACb,UAAU,aAAa,CAAC,IAAI,CAAC;AAC7B,UAAU,OAAO,CAAC;AAClB,SAAS;AACT,QAAQ,KAAK;AACb,UAAU,aAAa,CAAC,OAAO,CAAC;AAChC,UAAU,MAAM,CAAC;AACjB,SAAS;AACT,OAAQ;AACR;AACA,IAAI,aAAa,CAAC,IAAI,CAAC;;AAEvB,IAAI,OAAO,kBAAkB;AAC7B,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,eAAe,KAAK,CAAC,OAAO,EAA6B;AAChE,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE;AAC5B,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;AAChC;AACA,EAAE,eAAe,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC;AACvE,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,eAAe,KAAK,CAAC,OAAO,EAA6B;AAChE,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE;AAC5B,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;AAChC;AACA,EAAE,eAAe,MAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC;AACvF,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;AAC/B;;AAEA;AACA;AACA;AACO,SAAS,aAAa,GAAY;AACzC,EAAE,OAAO,CAAC,CAAC,SAAS,EAAE;AACtB;;AAEA;AACO,SAAS,SAAS,GAAY;AACrC,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE;AAC5B,EAAE,OAAO,MAAM,EAAE,UAAU,EAAE,CAAC,OAAA,KAAY,KAAA,IAAS,CAAC,CAAC,MAAM,EAAE,YAAY,EAAE;AAC3E;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,QAAQ,EAAwB;AAClE,EAAE,iBAAiB,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,OAAO,EAA4B;AAChE,EAAE,MAAM,cAAA,GAAiB,iBAAiB,EAAE;AAC5C,EAAE,MAAM,YAAA,GAAe,eAAe,EAAE;;AAExC;AACA,EAAE,MAAM,EAAE,SAAA,EAAY,GAAE,UAAU,CAAC,SAAA,IAAa,EAAE;;AAElD,EAAE,MAAM,OAAA,GAAU,WAAW,CAAC;AAC9B,IAAI,IAAI,EAAE,YAAY,CAAC,OAAO,EAAG,IAAG,cAAc,CAAC,OAAO,EAAE;AAC5D,IAAI,IAAI,SAAA,IAAa,EAAE,SAAA,EAAW,CAAC;AACnC,IAAI,GAAG,OAAO;AACd,GAAG,CAAC;;AAEJ;AACA,EAAE,MAAM,cAAe,GAAE,cAAc,CAAC,UAAU,EAAE;AACpD,EAAE,IAAI,cAAc,EAAE,MAAO,KAAI,IAAI,EAAE;AACvC,IAAI,aAAa,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,QAAS,EAAC,CAAC;AACvD;;AAEA,EAAE,UAAU,EAAE;;AAEd;AACA,EAAE,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC;;AAEpC,EAAE,OAAO,OAAO;AAChB;;AAEA;AACA;AACA;AACO,SAAS,UAAU,GAAS;AACnC,EAAE,MAAM,cAAA,GAAiB,iBAAiB,EAAE;AAC5C,EAAE,MAAM,YAAA,GAAe,eAAe,EAAE;;AAExC,EAAE,MAAM,OAAA,GAAU,YAAY,CAAC,UAAU,EAAC,IAAK,cAAc,CAAC,UAAU,EAAE;AAC1E,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,YAAY,CAAC,OAAO,CAAC;AACzB;AACA,EAAE,kBAAkB,EAAE;;AAEtB;AACA,EAAE,cAAc,CAAC,UAAU,EAAE;AAC7B;;AAEA;AACA;AACA;AACA,SAAS,kBAAkB,GAAS;AACpC,EAAE,MAAM,cAAA,GAAiB,iBAAiB,EAAE;AAC5C,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE;AAC5B,EAAE,MAAM,OAAQ,GAAE,cAAc,CAAC,UAAU,EAAE;AAC7C,EAAE,IAAI,OAAQ,IAAG,MAAM,EAAE;AACzB,IAAI,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;AAClC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,GAAG,GAAY,KAAK,EAAQ;AAC3D;AACA,EAAE,IAAI,GAAG,EAAE;AACX,IAAI,UAAU,EAAE;AAChB,IAAI;AACJ;;AAEA;AACA,EAAE,kBAAkB,EAAE;AACtB;;;;"}