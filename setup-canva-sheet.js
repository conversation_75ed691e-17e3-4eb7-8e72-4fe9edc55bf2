const { GoogleSpreadsheet } = require('google-spreadsheet');
const { JWT } = require('google-auth-library');
require('dotenv').config();
const fs = require('fs');
const path = require('path');

async function setupCanvaSheet() {
    console.log('🔧 Thiết lập sheet "canva"...\n');
    
    try {
        // 1. Đọc credentials
        const credentialsPath = path.join(__dirname, 'google-credentials.json');
        const credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));
        
        // 2. Tạo JWT auth
        const serviceAccountAuth = new JWT({
            email: credentials.client_email,
            key: credentials.private_key,
            scopes: ['https://www.googleapis.com/auth/spreadsheets'],
        });
        
        // 3. Kết nối Google Sheet
        const sheetId = process.env.GOOGLE_SHEET_ID;
        const doc = new GoogleSpreadsheet(sheetId, serviceAccountAuth);
        await doc.loadInfo();
        
        console.log('✅ Kết nối Google Sheet thành công!');
        console.log('📋 Tên Sheet:', doc.title);
        
        // 4. Kiểm tra sheet "canva"
        let canvaSheet = doc.sheetsByTitle['canva'];
        
        if (canvaSheet) {
            console.log('✅ Sheet "canva" đã tồn tại');
        } else {
            console.log('📄 Tạo sheet "canva" mới...');
            canvaSheet = await doc.addSheet({ 
                title: 'canva',
                headerValues: ['Email', 'Date Added', 'Duration', 'Status']
            });
            console.log('✅ Đã tạo sheet "canva" thành công!');
        }
        
        // 5. Kiểm tra headers
        await canvaSheet.loadHeaderRow();
        console.log('📋 Headers:', canvaSheet.headerValues);
        
        // 6. Test ghi dữ liệu
        console.log('\n🧪 Test ghi dữ liệu...');
        const testRow = await canvaSheet.addRow({
            'Email': '<EMAIL>',
            'Date Added': new Date().toLocaleString('vi-VN'),
            'Duration': '1 Month',
            'Status': 'Test'
        });
        
        console.log('✅ Test ghi dữ liệu thành công!');
        
        // 7. Xóa test row
        console.log('🗑️  Xóa test row...');
        await testRow.delete();
        console.log('✅ Xóa test row thành công!');
        
        console.log('\n🎉 THIẾT LẬP HOÀN TẤT!');
        console.log('✅ Sheet "canva" đã sẵn sàng sử dụng');
        console.log('📊 Cấu trúc: Email | Date Added | Duration | Status');
        console.log('🔗 Cả 1 tháng và 1 năm sẽ được ghi vào sheet này');
        
    } catch (error) {
        console.log('❌ LỖI:', error.message);
    }
}

// Chạy setup
setupCanvaSheet().catch(console.error);
