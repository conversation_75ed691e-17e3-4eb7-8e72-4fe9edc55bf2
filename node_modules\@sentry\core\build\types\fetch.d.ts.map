{"version": 3, "file": "fetch.d.ts", "sourceRoot": "", "sources": ["../../src/fetch.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AACjE,OAAO,KAAK,EAAE,IAAI,EAAkB,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAQ3E,KAAK,yBAAyB,GAC1B,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,GAClC,KAAK,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,GAEvB;IACE,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IAC7C,GAAG,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACjD,CAAC;AAEN;;;;GAIG;AACH,wBAAgB,sBAAsB,CACpC,WAAW,EAAE,gBAAgB,EAC7B,gBAAgB,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,EAC1C,mBAAmB,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,EAC7C,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,EAC3B,UAAU,GAAE,UAAgC,GAC3C,IAAI,GAAG,SAAS,CAmElB;AAED;;;;;;;;GAQG;AAEH,wBAAgB,gCAAgC,CAC9C,OAAO,EAAE,MAAM,GAAG,OAAO,EACzB,eAAe,EAAE;IACf,OAAO,CAAC,EACJ;QACE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,MAAM,GAAG,SAAS,CAAC;KAC9C,GACD,yBAAyB,CAAC;CAC/B,EACD,IAAI,CAAC,EAAE,IAAI,GACV,yBAAyB,GAAG,SAAS,CA6EvC"}