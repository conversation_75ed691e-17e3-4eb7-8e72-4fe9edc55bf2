{"name": "google-spreadsheet", "version": "4.1.4", "description": "Google Sheets API -- simple interface to read/write data and manage sheets", "keywords": ["google spreadsheets", "google sheets", "google", "spreadsheet", "spreadsheets", "sheets", "gdata", "api", "googlea<PERSON>", "drive", "google docs", "google drive"], "homepage": "https://theoephraim.github.io/node-google-spreadsheet", "repository": {"type": "git", "url": "git://github.com/theoephraim/node-google-spreadsheet.git"}, "license": "Unlicense", "author": "<PERSON> <<EMAIL>> (https://theoephraim.com)", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "src/index.ts", "src/lib"], "scripts": {"build": "NODE_ENV=production unbuild", "build2": "tsc", "build:dev": "unbuild", "commit": "cz", "docs:preview": "docsify serve docs", "lint": "eslint ./ --ext .ts", "lint:fix": "pnpm run lint --fix", "nodev": "node -v", "readme:copy": "echo \"<!-- DO NOT EDIT THIS FILE, EDIT MAIN README.md AND RUN \\`npm readme:copy\\` instead -->\n\n_Welcome to the docs site for_\n\" | cat - README.md > docs/README.md", "changelog": "auto-changelog --commit-limit false --unreleased-only --stdout", "release": "pnpm run build && release-it", "runscript": "NODE_NO_WARNINGS=1 node --experimental-specifier-resolution=node --loader ts-node/esm", "test": "dmno run -- vitest --no-file-parallelism"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependencies": {"axios": "^1.7.7", "lodash": "^4.17.21"}, "devDependencies": {"@dmno/encrypted-vault-plugin": "^0.0.5", "@swc/core": "^1.3.60", "@types/lodash": "^4.14.195", "@types/node": "^20.2.5", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "auto-changelog": "^2.4.0", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "delay": "^4.3.0", "dmno": "^0.0.22", "docsify-cli": "^4.4.3", "eslint": "^8.41.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-no-floating-promise": "^1.0.2", "google-auth-library": "^9.14.0", "release-it": "^15.11.0", "ts-node": "^10.9.1", "typescript": "^5.5.4", "unbuild": "^1.2.1", "vitest": "^2.0.5"}, "peerDependencies": {"google-auth-library": "^8.8.0 || ^9.0.0"}, "peerDependenciesMeta": {"google-auth-library": {"optional": true}}, "volta": {"node": "20.17.0"}}