require('dotenv').config();
const express = require('express');
const { initializeBrowser } = require('./browser');

const app = express();
const port = process.env.PORT || 3000;

let currentPage;

/**
 * Hàm sleep để tạm dừng thực thi
 * @param {number} ms - Thời gian chờ tính bằng mili giây
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Click vào nút "Mời thành viên" dựa trên HTML cụ thể
 * @param {import('puppeteer').Page} page - Thể hiện của trang Puppeteer
 * @returns {Promise<boolean>} True nếu click thành công
 */
async function clickInviteButton(page) {
    console.log('Tìm nút "Mời thành viên" bằng selector cụ thể...');

    try {
        // Selector dựa trên HTML: <button><span class="khPe7Q">Mời thành viên</span></button>
        const inviteButton = await page.evaluateHandle(() => {
            const spans = document.querySelectorAll('span.khPe7Q');
            for (const span of spans) {
                if (span.textContent && span.textContent.trim() === 'Mời thành viên') {
                    const button = span.closest('button');
                    if (button) {
                        button.setAttribute('data-invite-button', 'found');
                        return button;
                    }
                }
            }
            return null;
        });

        if (inviteButton) {
            await page.click('[data-invite-button="found"]', { delay: 50 });
            console.log('✓ Đã click nút "Mời thành viên" thành công');
            return true;
        } else {
            console.log('✗ Không tìm thấy nút "Mời thành viên"');
            return false;
        }
    } catch (error) {
        console.error('Lỗi khi click nút mời:', error.message);
        return false;
    }
}

/**
 * Click vào nút "Mời qua email" dựa trên HTML cụ thể
 * @param {import('puppeteer').Page} page - Thể hiện của trang Puppeteer
 * @returns {Promise<boolean>} True nếu click thành công
 */
async function clickInviteByEmailButton(page) {
    console.log('Tìm nút "Mời qua email" bằng selector cụ thể...');

    try {
        // Selector dựa trên HTML: <button role="tab"><span class="w3JiGg">Mời qua email</span></button>
        const emailButton = await page.evaluateHandle(() => {
            const spans = document.querySelectorAll('span.w3JiGg');
            for (const span of spans) {
                if (span.textContent && span.textContent.trim() === 'Mời qua email') {
                    const button = span.closest('button[role="tab"]');
                    if (button) {
                        button.setAttribute('data-email-button', 'found');
                        return button;
                    }
                }
            }
            return null;
        });

        if (emailButton) {
            await page.click('[data-email-button="found"]', { delay: 50 });
            console.log('✓ Đã click nút "Mời qua email" thành công');
            return true;
        } else {
            console.log('✗ Không tìm thấy nút "Mời qua email"');
            return false;
        }
    } catch (error) {
        console.error('Lỗi khi click nút mời qua email:', error.message);
        return false;
    }
}

/**
 * Click vào nút "Gửi lời mời" dựa trên HTML cụ thể
 * @param {import('puppeteer').Page} page - Thể hiện của trang Puppeteer
 * @returns {Promise<boolean>} True nếu click thành công
 */
async function clickSendInviteButton(page) {
    console.log('Tìm nút "Gửi lời mời" bằng selector cụ thể...');

    try {
        // Selector dựa trên HTML: <button><span class="khPe7Q">Gửi lời mời</span></button>
        const sendButton = await page.evaluateHandle(() => {
            const spans = document.querySelectorAll('span.khPe7Q');
            for (const span of spans) {
                if (span.textContent && span.textContent.trim() === 'Gửi lời mời') {
                    const button = span.closest('button');
                    if (button) {
                        button.setAttribute('data-send-button', 'found');
                        return button;
                    }
                }
            }
            return null;
        });

        if (sendButton) {
            await page.click('[data-send-button="found"]', { delay: 50 });
            console.log('✓ Đã click nút "Gửi lời mời" thành công');
            return true;
        } else {
            console.log('✗ Không tìm thấy nút "Gửi lời mời"');
            return false;
        }
    } catch (error) {
        console.error('Lỗi khi click nút gửi lời mời:', error.message);
        return false;
    }
}

/**
 * Tạo độ trễ ngẫu nhiên giữa các ký tự để giả lập người thật
 * @returns {number} Thời gian delay tính bằng milliseconds
 */
function getRandomTypingDelay() {
    // Độ trễ ngẫu nhiên từ 80ms đến 200ms (tốc độ gõ tự nhiên)
    return Math.floor(Math.random() * (200 - 80 + 1)) + 80;
}

/**
 * Giả lập di chuyển chuột tự nhiên
 * @param {import('puppeteer').Page} page - Thể hiện của trang Puppeteer
 */
async function simulateHumanMouseMovement(page) {
    // Di chuyển chuột theo đường cong tự nhiên
    const startX = Math.floor(Math.random() * 400) + 200;
    const startY = Math.floor(Math.random() * 300) + 200;
    const endX = Math.floor(Math.random() * 400) + 600;
    const endY = Math.floor(Math.random() * 300) + 400;

    // Di chuyển theo nhiều bước nhỏ
    const steps = 5 + Math.floor(Math.random() * 5);
    for (let i = 0; i <= steps; i++) {
        const x = startX + (endX - startX) * (i / steps);
        const y = startY + (endY - startY) * (i / steps);
        await page.mouse.move(x, y);
        await sleep(50 + Math.floor(Math.random() * 50));
    }
}

/**
 * Giả lập scroll tự nhiên
 * @param {import('puppeteer').Page} page - Thể hiện của trang Puppeteer
 */
async function simulateHumanScroll(page) {
    // Scroll ngẫu nhiên một chút
    const scrollAmount = Math.floor(Math.random() * 300) + 100;
    await page.evaluate((amount) => {
        window.scrollBy(0, amount);
    }, scrollAmount);
    await sleep(500 + Math.floor(Math.random() * 500));

    // Scroll ngược lại một chút
    await page.evaluate((amount) => {
        window.scrollBy(0, -amount / 2);
    }, scrollAmount);
    await sleep(300);
}

/**
 * Xử lý Cloudflare challenge
 * @param {import('puppeteer').Page} page - Thể hiện của trang Puppeteer
 * @returns {Promise<boolean>} True nếu bypass thành công
 */
async function handleCloudflareChallenge(page) {
    console.log('Phát hiện Cloudflare challenge, đang xử lý...');

    try {
        // Chờ và kiểm tra các dấu hiệu của Cloudflare
        const isCloudflare = await page.evaluate(() => {
            return document.title.includes('Just a moment') ||
                   document.body.innerText.includes('Checking your browser') ||
                   document.body.innerText.includes('Ray ID') ||
                   document.querySelector('.cf-browser-verification') !== null;
        });

        if (isCloudflare) {
            console.log('Đang chờ Cloudflare challenge hoàn thành...');

            // Giả lập hành vi người dùng trong khi chờ
            await simulateHumanMouseMovement(page);
            await sleep(2000);

            // Chờ tối đa 30 giây cho challenge hoàn thành
            await page.waitForFunction(() => {
                return !document.title.includes('Just a moment') &&
                       !document.body.innerText.includes('Checking your browser');
            }, { timeout: 30000 });

            console.log('✓ Đã vượt qua Cloudflare challenge');
            await sleep(2000); // Chờ thêm để đảm bảo trang load hoàn toàn
            return true;
        }

        return false;
    } catch (error) {
        console.error('Lỗi khi xử lý Cloudflare challenge:', error.message);
        return false;
    }
}

/**
 * Tìm và nhập email vào input field như người thật
 * @param {import('puppeteer').Page} page - Thể hiện của trang Puppeteer
 * @param {string} email - Email cần nhập
 * @returns {Promise<boolean>} True nếu nhập thành công
 */
async function typeEmailInInput(page, email) {
    console.log(`Tìm input field để nhập email như người thật: ${email}`);

    try {
        // Tìm input field bằng nhiều cách khác nhau
        const inputSelector = await page.evaluate(() => {
            // Thử tìm input có thể nhập email
            const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input:not([type])');
            for (const input of inputs) {
                // Kiểm tra nếu input visible và có thể nhập
                if (input.offsetParent !== null && !input.disabled && !input.readOnly) {
                    // Kiểm tra placeholder hoặc attributes liên quan đến email
                    const placeholder = input.placeholder?.toLowerCase() || '';
                    const autocomplete = input.autocomplete?.toLowerCase() || '';
                    const inputmode = input.inputMode?.toLowerCase() || '';

                    if (placeholder.includes('email') ||
                        autocomplete.includes('email') ||
                        inputmode.includes('email') ||
                        autocomplete.includes('team-member')) {
                        input.setAttribute('data-email-input', 'found');
                        return '[data-email-input="found"]';
                    }
                }
            }
            return null;
        });

        if (inputSelector) {
            // Click vào input để focus
            await page.click(inputSelector);
            await sleep(300); // Chờ focus

            // Xóa nội dung cũ nếu có
            await page.keyboard.down('Control');
            await page.keyboard.press('KeyA');
            await page.keyboard.up('Control');
            await sleep(100);

            console.log('Bắt đầu nhập email từng ký tự...');

            // Nhập từng ký tự với độ trễ ngẫu nhiên
            for (let i = 0; i < email.length; i++) {
                const char = email[i];
                await page.keyboard.type(char);

                // Độ trễ ngẫu nhiên giữa các ký tự
                const delay = getRandomTypingDelay();
                await sleep(delay);

                // Log tiến trình (chỉ hiển thị mỗi 5 ký tự để không spam log)
                if ((i + 1) % 5 === 0 || i === email.length - 1) {
                    console.log(`Đã nhập: ${email.substring(0, i + 1)}`);
                }
            }

            // Trigger events để đảm bảo form nhận diện input
            await page.evaluate((selector) => {
                const input = document.querySelector(selector);
                if (input) {
                    input.dispatchEvent(new Event('input', { bubbles: true }));
                    input.dispatchEvent(new Event('change', { bubbles: true }));
                    input.dispatchEvent(new Event('blur', { bubbles: true }));
                }
            }, inputSelector);

            await sleep(500); // Chờ form xử lý
            console.log('✓ Đã nhập email thành công như người thật');
            return true;
        } else {
            console.log('✗ Không tìm thấy input field phù hợp');
            return false;
        }
    } catch (error) {
        console.error('Lỗi khi nhập email:', error.message);
        return false;
    }
}

/**
 * Tìm và click vào một phần tử chứa văn bản cụ thể.
 * Hàm này dùng cho các nút có văn bản rõ ràng mà selector khác khó bắt.
 * @param {import('puppeteer').Page} page - Thể hiện của trang Puppeteer.
 * @param {string} textToClick - Văn bản cần tìm và click.
 * @param {string} [parentSelector='*'] - Selector CSS hoặc XPath của phần tử cha để giới hạn phạm vi tìm kiếm (mặc định là toàn bộ trang).
 * @param {number} [timeout=30000] - Thời gian chờ tối đa cho phần tử xuất hiện.
 * @returns {Promise<boolean>} True nếu tìm thấy và click thành công, ngược lại là false.
 */
async function clickElementByText(page, textToClick, parentSelector = '*', timeout = 30000) {
    console.log(`Đang tìm và click văn bản: "${textToClick}" trong phạm vi: "${parentSelector}"`);

    try {
        // Sử dụng page.evaluate để tìm element chứa text
        const elementFound = await page.evaluate((text, parent) => {
            const elements = document.querySelectorAll(`${parent} *`);
            for (let element of elements) {
                if (element.textContent && element.textContent.toLowerCase().includes(text.toLowerCase())) {
                    // Tạo một unique identifier cho element
                    element.setAttribute('data-temp-id', 'temp-click-target');
                    return true;
                }
            }
            return false;
        }, textToClick, parentSelector);

        if (elementFound) {
            // Chờ element xuất hiện và click
            await page.waitForSelector('[data-temp-id="temp-click-target"]', { visible: true, timeout: timeout });
            console.log(`Đã tìm thấy văn bản "${textToClick}". Đang click...`);
            await page.click('[data-temp-id="temp-click-target"]', { delay: 50 });

            // Xóa attribute tạm thời
            await page.evaluate(() => {
                const element = document.querySelector('[data-temp-id="temp-click-target"]');
                if (element) {
                    element.removeAttribute('data-temp-id');
                }
            });

            return true;
        } else {
            console.error(`Không tìm thấy phần tử chứa văn bản "${textToClick}".`);
            return false;
        }
    } catch (error) {
        console.error(`Lỗi khi tìm hoặc click văn bản "${textToClick}":`, error);
        return false;
    }
}


async function setupBrowserAndLogin() {
    try {
        console.log('Bắt đầu khởi tạo trình duyệt và đăng nhập Canva...');
        const { browser, page } = await initializeBrowser();
        currentBrowser = browser;
        currentPage = page;
        await currentPage.bringToFront();
        console.log('Trình duyệt đã được khởi tạo thành công.');



        // --- Logic kiểm tra đăng nhập đáng tin cậy hơn ---
        console.log('Kiểm tra trạng thái đăng nhập...');
        try {
            console.log('Cố gắng điều hướng đến trang quản lý thành viên để kiểm tra đăng nhập...');
            await currentPage.goto('https://www.canva.com/settings/people', {
                waitUntil: 'networkidle0',
                timeout: 30000
            });



            // Sử dụng clickElementByText cho nút "Mời thành viên" để kiểm tra sự tồn tại
            const isInviteButtonVisible = await clickElementByText(currentPage, 'Mời thành viên', '*', 10000); // Chỉ kiểm tra sự tồn tại
            if (isInviteButtonVisible) {
                console.log('Đã đăng nhập sẵn và ở đúng trang quản lý thành viên. Bỏ qua các bước đăng nhập.');
                return;
            } else {
                 console.log('Chưa đăng nhập hoặc session đã hết hạn/không hợp lệ. Bắt đầu quá trình đăng nhập đầy đủ...');
            }

        } catch (checkError) {
            console.log('Chưa đăng nhập hoặc session đã hết hạn/không hợp lệ. Bắt đầu quá trình đăng nhập đầy đủ...');
        }
        // --- Kết thúc logic kiểm tra đăng nhập ---


        // Bước 1: Điều hướng đến trang đăng nhập Canva
        console.log('Điều hướng đến trang đăng nhập Canva...');
        await currentPage.goto('https://www.canva.com/login/', {
            waitUntil: 'domcontentloaded',
            timeout: 60000
        });

        await currentPage.evaluate(() => {
            window.scrollBy(0, window.innerHeight);
        });
        await sleep(500);

        // Bước 2: Tìm và click vào nút "Continue with email"
        console.log('Tìm và click nút "Continue with email"...');
        const clickedContinueEmail = await clickElementByText(currentPage, 'Continue with email', '*', 30000);
        if (!clickedContinueEmail) {
            throw new Error('Không thể tìm thấy hoặc click nút "Continue with email".');
        }

        // Chờ một chút để trang load
        await sleep(2000);

        // Thử nhiều selector khác nhau cho trường email
        let emailInputSelector = null;
        const possibleEmailSelectors = [
            'input[type="email"]',
            'input[name="email"]',
            'input[placeholder*="email"]',
            'input[placeholder*="Email"]',
            'input[autocomplete="email"]',
            'input[id*="email"]'
        ];

        for (const selector of possibleEmailSelectors) {
            try {
                await currentPage.waitForSelector(selector, { visible: true, timeout: 5000 });
                emailInputSelector = selector;
                console.log(`Tìm thấy trường email với selector: ${selector}`);
                break;
            } catch (e) {
                console.log(`Không tìm thấy với selector: ${selector}`);
            }
        }

        if (!emailInputSelector) {
            throw new Error('Không thể tìm thấy trường input email với bất kỳ selector nào.');
        }

        console.log('Đã click "Continue with email" và trường input email đã sẵn sàng.');

        // Bước 3: Nhập email
        console.log('Nhập email...');
        await currentPage.type(emailInputSelector, process.env.CANVA_EMAIL, { delay: 20 });
        await sleep(300);

        // Bước 4: Nhấn Enter để tiếp tục sau email
        console.log('Nhấn Enter để tiếp tục sau email...');
        await currentPage.keyboard.press('Enter');

        // Chờ trang chuyển hoặc trường password xuất hiện
        try {
            await currentPage.waitForNavigation({ waitUntil: 'networkidle0', timeout: 30000 });
            console.log('Đã chuyển sang trang nhập mật khẩu.');
        } catch (navError) {
            console.log('Không có navigation, có thể trường password đã xuất hiện trên cùng trang.');
            await sleep(2000);
        }



        // Bước 5: Chờ và nhập mật khẩu
        console.log('Chờ input mật khẩu xuất hiện và nhập mật khẩu...');

        // Chờ thêm thời gian để trang load hoàn toàn
        await sleep(3000);

        // Thử nhiều selector khác nhau cho trường password
        let passwordInputSelector = null;
        const possiblePasswordSelectors = [
            'input[type="password"]',
            'input[name="password"]',
            'input[autocomplete="current-password"]',
            'input[placeholder*="password"]',
            'input[placeholder*="Password"]',
            'input[placeholder="Enter password"]',
            'input.bCVoGQ',
            'input[id*=":r"]',
            'input[placeholder*="mật khẩu"]',
            'input[autocomplete="password"]',
            'input[id*="password"]'
        ];

        console.log('Đang tìm trường password...');
        for (const selector of possiblePasswordSelectors) {
            try {
                console.log(`Thử selector: ${selector}`);
                await currentPage.waitForSelector(selector, { visible: true, timeout: 8000 });
                passwordInputSelector = selector;
                console.log(`✓ Tìm thấy trường password với selector: ${selector}`);
                break;
            } catch (e) {
                console.log(`✗ Không tìm thấy với selector: ${selector}`);
            }
        }

        // Nếu vẫn không tìm thấy, thử tìm bằng cách khác
        if (!passwordInputSelector) {
            console.log('Thử tìm trường password bằng cách khác...');
            try {
                const foundByEvaluate = await currentPage.evaluate(() => {
                    const inputs = document.querySelectorAll('input');
                    for (let input of inputs) {
                        if (input.type === 'password' ||
                            input.name.toLowerCase().includes('password') ||
                            input.placeholder.toLowerCase().includes('password') ||
                            input.id.toLowerCase().includes('password')) {
                            input.setAttribute('data-temp-password', 'found');
                            return true;
                        }
                    }
                    return false;
                });

                if (foundByEvaluate) {
                    passwordInputSelector = '[data-temp-password="found"]';
                    console.log('✓ Tìm thấy trường password bằng JavaScript evaluation');
                }
            } catch (evalError) {
                console.log('Lỗi khi tìm password bằng evaluation:', evalError.message);
            }
        }

        if (!passwordInputSelector) {
            throw new Error('Không thể tìm thấy trường input password với bất kỳ phương pháp nào.');
        }

        await currentPage.type(passwordInputSelector, process.env.CANVA_PASSWORD, { delay: 20 });
        await sleep(300);

        // Bước 6: Nhấn Enter để đăng nhập
        console.log('Nhấn Enter để đăng nhập...');
        await currentPage.keyboard.press('Enter');

        // Chờ đăng nhập thành công
        try {
            await currentPage.waitForNavigation({ waitUntil: 'networkidle0', timeout: 60000 });
            console.log('Đăng nhập thành công!');
        } catch (loginError) {
            console.log('Chờ thêm thời gian để đăng nhập hoàn tất...');
            await sleep(5000);
        }



        // Bước 7: Điều hướng đến trang quản lý thành viên (nếu chưa ở đó)
        console.log('Điều hướng đến trang quản lý thành viên Canva (settings/people)...');
        await currentPage.goto('https://www.canva.com/settings/people', { waitUntil: 'networkidle0', timeout: 60000 });

        // Chờ trang load hoàn toàn
        await sleep(5000);

        // Sử dụng hàm click cụ thể cho nút "Mời thành viên"
        let isInviteButtonReady = await clickInviteButton(currentPage);

        // Nếu không tìm thấy bằng selector cụ thể, thử các text khác
        if (!isInviteButtonReady) {
            const possibleInviteTexts = ['Invite members', 'Add members', 'Invite people'];
            for (const inviteText of possibleInviteTexts) {
                console.log(`Thử tìm nút với text: "${inviteText}"`);
                isInviteButtonReady = await clickElementByText(currentPage, inviteText, '*', 5000);
                if (isInviteButtonReady) {
                    console.log(`✓ Tìm thấy nút mời với text: "${inviteText}"`);
                    break;
                }
            }
        }

        // Đóng popup nếu đã mở
        if (isInviteButtonReady) {
            await currentPage.keyboard.press('Escape');
            await sleep(1000);
        }

        if (!isInviteButtonReady) {
            console.log('Không tìm thấy nút mời thành viên, nhưng vẫn tiếp tục...');
        } else {
            console.log('Đã đến trang quản lý thành viên và tìm thấy nút mời.');
        }

    } catch (error) {
        console.error('Lỗi nghiêm trọng trong quá trình khởi tạo trình duyệt hoặc đăng nhập:', error);
        if (currentPage) {
            console.error('URL hiện tại khi lỗi:', currentPage.url());
        }
        throw error;
    }
}

// Endpoint API để thêm thành viên
app.get('/apicanva', async (req, res) => {
    const { email } = req.query;

    if (!email) {
        return res.status(400).send('Tham số email bị thiếu. Sử dụng: /apicanva?email=<EMAIL>');
    }

    if (!currentPage || currentPage.isClosed()) {
        console.warn('Page instance không hợp lệ hoặc đã đóng. Thử khởi tạo lại trình duyệt và đăng nhập...');
        try {
            await setupBrowserAndLogin();
            if (!currentPage || currentPage.isClosed()) {
                 return res.status(500).send('Không thể khôi phục trạng thái trình duyệt. Vui lòng kiểm tra log server.');
            }
        } catch (reloginError) {
            return res.status(500).send(`Không thể đăng nhập lại Canva để thêm thành viên. Lỗi: ${reloginError.message}`);
        }
    }

    try {
        console.log(`Đang cố gắng thêm thành viên: ${email}`);

        // Luôn điều hướng về trang settings/people cho mỗi request mới
        console.log('Điều hướng về trang quản lý thành viên...');
        await currentPage.goto('https://www.canva.com/settings/people', {
            waitUntil: 'networkidle0',
            timeout: 60000
        });
        await sleep(3000); // Chờ trang load hoàn toàn

        // Chờ một chút để trang load
        await sleep(2000);

        // Bước 8: Click "Mời thành viên" bằng selector cụ thể (giả lập người thật)
        console.log('Tìm và click nút mời thành viên...');
        await sleep(800 + Math.floor(Math.random() * 700)); // Chờ ngẫu nhiên 0.8-1.5s

        let clickedInviteMember = await clickInviteButton(currentPage);

        // Nếu không thành công, thử các text khác
        if (!clickedInviteMember) {
            const possibleInviteTexts = ['Invite members', 'Add members', 'Invite people'];
            for (const inviteText of possibleInviteTexts) {
                console.log(`Thử click với text: "${inviteText}"`);
                clickedInviteMember = await clickElementByText(currentPage, inviteText, '*', 5000);
                if (clickedInviteMember) break;
            }
        }

        if (!clickedInviteMember) {
            throw new Error('Không thể tìm thấy và click nút mời thành viên.');
        }
        await sleep(2000); // Tạm dừng cho popup ổn định



        // Bước 9: Click "Mời qua email" (giả lập người thật)
        console.log('Click "Mời qua email"...');
        await sleep(1000 + Math.floor(Math.random() * 800)); // Chờ ngẫu nhiên 1-1.8s

        const clickedInviteByEmail = await clickInviteByEmailButton(currentPage);
        if (!clickedInviteByEmail) {
            // Thử fallback với text search
            const fallbackClicked = await clickElementByText(currentPage, 'Mời qua email');
            if (!fallbackClicked) {
                throw new Error('Không thể click tab "Mời qua email".');
            }
        }
        await sleep(1500); // Tạm dừng ngắn cho trường nhập liệu xuất hiện

        // Bước 10: Nhập email người dùng gửi từ truy vấn API (giả lập người thật)
        console.log('Chờ form load và chuẩn bị nhập email...');
        await sleep(1500 + Math.floor(Math.random() * 1000)); // Chờ ngẫu nhiên 1.5-2.5s

        // Di chuyển chuột một chút để giả lập người thật
        await currentPage.mouse.move(500, 400);
        await sleep(200);

        const emailInputSuccess = await typeEmailInInput(currentPage, email);
        if (!emailInputSuccess) {
            throw new Error('Không thể nhập email vào trường input');
        }

        // Chờ một chút sau khi nhập xong
        await sleep(800 + Math.floor(Math.random() * 500)); // Chờ ngẫu nhiên 0.8-1.3s
        await sleep(300);

        // Bước 11: Click nút "Gửi lời mời" (giả lập người thật)
        console.log('Click nút "Gửi lời mời"...');
        await sleep(1200 + Math.floor(Math.random() * 800)); // Chờ ngẫu nhiên 1.2-2s

        // Di chuyển chuột đến vị trí nút trước khi click
        await currentPage.mouse.move(600, 500);
        await sleep(300);

        const sendClicked = await clickSendInviteButton(currentPage);
        if (!sendClicked) {
            // Fallback: thử nhấn Enter
            console.log('Fallback: Nhấn Enter để gửi lời mời...');
            await currentPage.keyboard.press('Enter');
        }
        
        // Chờ cho popup đóng lại và xử lý kết quả
        await sleep(3000); // Đợi Canva xử lý và hiển thị kết quả

        console.log(`Đã gửi lời mời thành công đến ${email}`);
        res.send(`Đã mời thành công ${email} vào Canva.`);

        // Đóng tất cả popup và chuẩn bị cho request tiếp theo
        await currentPage.keyboard.press('Escape'); // Đóng popup hiện tại
        await sleep(500);
        await currentPage.keyboard.press('Escape'); // Đóng thêm lần nữa để chắc chắn
        await sleep(1000);

        // Quay về trang chính để sẵn sàng cho request tiếp theo
        console.log('Chuẩn bị sẵn sàng cho request tiếp theo...');

    } catch (error) {
        console.error(`Lỗi khi thêm thành viên ${email}:`, error);
        res.status(500).send(`Không thể mời ${email}. Lỗi: ${error.message}`);
    }
});

app.listen(port, () => {
    console.log(`Server đang lắng nghe tại http://localhost:${port}`);
    console.log('Bắt đầu khởi tạo trình duyệt và đăng nhập Canva...');
    setupBrowserAndLogin().catch(err => {
        console.error('Ứng dụng không thể khởi động do lỗi đăng nhập trình duyệt ban đầu:', err);
    });
});