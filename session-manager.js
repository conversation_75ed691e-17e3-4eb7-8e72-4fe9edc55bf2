const fs = require('fs');
const path = require('path');

// Đường dẫn lưu session data
const USER_DATA_DIR = path.join(__dirname, 'user-data');
const SESSION_FILE = path.join(USER_DATA_DIR, 'canva-session.json');
const COOKIES_FILE = path.join(USER_DATA_DIR, 'canva-cookies.json');

// Tạo thư mục user-data nếu chưa có
if (!fs.existsSync(USER_DATA_DIR)) {
    fs.mkdirSync(USER_DATA_DIR, { recursive: true });
    console.log('✓ Đã tạo thư mục user-data');
}

class SessionManager {
    constructor() {
        this.sessionFile = SESSION_FILE;
        this.cookiesFile = COOKIES_FILE;
    }

    // Lưu session data đầy đủ
    async saveFullSession(page) {
        try {
            console.log('Đang lưu session data đầy đủ...');
            
            // Lấy cookies
            const cookies = await page.cookies();
            
            // Lấy localStorage
            const localStorage = await page.evaluate(() => {
                const data = {};
                for (let i = 0; i < window.localStorage.length; i++) {
                    const key = window.localStorage.key(i);
                    data[key] = window.localStorage.getItem(key);
                }
                return data;
            });
            
            // Lấy sessionStorage
            const sessionStorage = await page.evaluate(() => {
                const data = {};
                for (let i = 0; i < window.sessionStorage.length; i++) {
                    const key = window.sessionStorage.key(i);
                    data[key] = window.sessionStorage.getItem(key);
                }
                return data;
            });
            
            const sessionData = {
                cookies,
                localStorage,
                sessionStorage,
                timestamp: Date.now(),
                url: await page.url(),
                userAgent: await page.evaluate(() => navigator.userAgent),
                viewport: await page.viewport()
            };
            
            fs.writeFileSync(this.sessionFile, JSON.stringify(sessionData, null, 2));
            console.log('✓ Đã lưu session data đầy đủ');
            
            return true;
            
        } catch (error) {
            console.log('Lỗi khi lưu session data:', error.message);
            return false;
        }
    }

    // Lưu chỉ cookies
    async saveCookiesOnly(page) {
        try {
            console.log('Đang lưu cookies...');
            
            const cookies = await page.cookies();
            const cookieData = {
                cookies,
                timestamp: Date.now(),
                domain: 'www.canva.com',
                count: cookies.length
            };
            
            fs.writeFileSync(this.cookiesFile, JSON.stringify(cookieData, null, 2));
            console.log(`✓ Đã lưu ${cookies.length} cookies`);
            
            return true;
            
        } catch (error) {
            console.log('Lỗi khi lưu cookies:', error.message);
            return false;
        }
    }

    // Khôi phục session đầy đủ
    async restoreFullSession(page) {
        try {
            if (!fs.existsSync(this.sessionFile)) {
                console.log('Không tìm thấy session data đã lưu');
                return false;
            }
            
            console.log('Đang khôi phục session data đầy đủ...');
            const sessionData = JSON.parse(fs.readFileSync(this.sessionFile, 'utf8'));
            
            // Kiểm tra session có quá cũ không (7 ngày)
            const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 ngày
            if (Date.now() - sessionData.timestamp > maxAge) {
                console.log('Session data đã quá cũ, bỏ qua khôi phục');
                return false;
            }
            
            // Điều hướng đến trang Canva trước
            await page.goto('https://www.canva.com', { waitUntil: 'networkidle0' });
            
            // Khôi phục cookies
            if (sessionData.cookies && sessionData.cookies.length > 0) {
                await page.setCookie(...sessionData.cookies);
                console.log(`✓ Đã khôi phục ${sessionData.cookies.length} cookies`);
            }
            
            // Khôi phục localStorage
            if (sessionData.localStorage) {
                await page.evaluate((data) => {
                    for (const [key, value] of Object.entries(data)) {
                        window.localStorage.setItem(key, value);
                    }
                }, sessionData.localStorage);
                console.log('✓ Đã khôi phục localStorage');
            }
            
            // Khôi phục sessionStorage
            if (sessionData.sessionStorage) {
                await page.evaluate((data) => {
                    for (const [key, value] of Object.entries(data)) {
                        window.sessionStorage.setItem(key, value);
                    }
                }, sessionData.sessionStorage);
                console.log('✓ Đã khôi phục sessionStorage');
            }
            
            // Refresh trang để áp dụng session
            await page.reload({ waitUntil: 'networkidle0' });
            console.log('✓ Đã khôi phục session data đầy đủ thành công');
            
            return true;
            
        } catch (error) {
            console.log('Lỗi khi khôi phục session data:', error.message);
            return false;
        }
    }

    // Load chỉ cookies
    async loadCookiesOnly(page) {
        try {
            if (!fs.existsSync(this.cookiesFile)) {
                console.log('Không tìm thấy file cookies');
                return false;
            }
            
            console.log('Đang load cookies...');
            const cookieData = JSON.parse(fs.readFileSync(this.cookiesFile, 'utf8'));
            
            // Kiểm tra cookies có quá cũ không (3 ngày)
            const maxAge = 3 * 24 * 60 * 60 * 1000; // 3 ngày
            if (Date.now() - cookieData.timestamp > maxAge) {
                console.log('Cookies đã quá cũ, bỏ qua load');
                return false;
            }
            
            // Điều hướng đến trang Canva trước
            await page.goto('https://www.canva.com', { waitUntil: 'networkidle0' });
            
            // Load cookies
            if (cookieData.cookies && cookieData.cookies.length > 0) {
                await page.setCookie(...cookieData.cookies);
                console.log(`✓ Đã load ${cookieData.cookies.length} cookies`);
                
                // Refresh trang để áp dụng cookies
                await page.reload({ waitUntil: 'networkidle0' });
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.log('Lỗi khi load cookies:', error.message);
            return false;
        }
    }

    // Xóa tất cả session data
    clearAllSessionData() {
        try {
            let cleared = false;
            
            if (fs.existsSync(this.sessionFile)) {
                fs.unlinkSync(this.sessionFile);
                console.log('✓ Đã xóa session data');
                cleared = true;
            }
            
            if (fs.existsSync(this.cookiesFile)) {
                fs.unlinkSync(this.cookiesFile);
                console.log('✓ Đã xóa cookies');
                cleared = true;
            }
            
            if (!cleared) {
                console.log('Không có session data để xóa');
            }
            
            return cleared;
            
        } catch (error) {
            console.log('Lỗi khi xóa session data:', error.message);
            return false;
        }
    }

    // Kiểm tra session có tồn tại không
    hasSessionData() {
        return fs.existsSync(this.sessionFile) || fs.existsSync(this.cookiesFile);
    }

    // Lấy thông tin session
    getSessionInfo() {
        const info = {
            hasFullSession: fs.existsSync(this.sessionFile),
            hasCookies: fs.existsSync(this.cookiesFile),
            sessionAge: null,
            cookiesAge: null,
            cookiesCount: 0
        };

        if (info.hasFullSession) {
            try {
                const sessionData = JSON.parse(fs.readFileSync(this.sessionFile, 'utf8'));
                info.sessionAge = Date.now() - sessionData.timestamp;
            } catch (error) {
                console.log('Lỗi khi đọc session info:', error.message);
            }
        }

        if (info.hasCookies) {
            try {
                const cookieData = JSON.parse(fs.readFileSync(this.cookiesFile, 'utf8'));
                info.cookiesAge = Date.now() - cookieData.timestamp;
                info.cookiesCount = cookieData.count || 0;
            } catch (error) {
                console.log('Lỗi khi đọc cookies info:', error.message);
            }
        }

        return info;
    }
}

module.exports = SessionManager;
