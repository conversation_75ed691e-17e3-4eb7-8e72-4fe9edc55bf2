{"version": 3, "file": "feedback.js", "sources": ["../../src/feedback.ts"], "sourcesContent": ["import { getClient, getCurrentScope } from './currentScopes';\nimport type { EventHint } from './types-hoist/event';\nimport type { FeedbackEvent, SendFeedbackParams } from './types-hoist/feedback';\n\n/**\n * Send user feedback to Sentry.\n */\nexport function captureFeedback(\n  params: SendFeedbackParams,\n  hint: EventHint & { includeReplay?: boolean } = {},\n  scope = getCurrentScope(),\n): string {\n  const { message, name, email, url, source, associatedEventId, tags } = params;\n\n  const feedbackEvent: FeedbackEvent = {\n    contexts: {\n      feedback: {\n        contact_email: email,\n        name,\n        message,\n        url,\n        source,\n        associated_event_id: associatedEventId,\n      },\n    },\n    type: 'feedback',\n    level: 'info',\n    tags,\n  };\n\n  const client = scope?.getClient() || getClient();\n\n  if (client) {\n    client.emit('beforeSendFeedback', feedbackEvent, hint);\n  }\n\n  const eventId = scope.captureEvent(feedbackEvent, hint);\n\n  return eventId;\n}\n"], "names": ["getCurrentScope", "getClient"], "mappings": ";;;;AAIA;AACA;AACA;AACO,SAAS,eAAe;AAC/B,EAAE,MAAM;AACR,EAAE,IAAI,GAA4C,EAAE;AACpD,EAAE,KAAM,GAAEA,6BAAe,EAAE;AAC3B,EAAU;AACV,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAK,EAAA,GAAI,MAAM;;AAE/E,EAAE,MAAM,aAAa,GAAkB;AACvC,IAAI,QAAQ,EAAE;AACd,MAAM,QAAQ,EAAE;AAChB,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,IAAI;AACZ,QAAQ,OAAO;AACf,QAAQ,GAAG;AACX,QAAQ,MAAM;AACd,QAAQ,mBAAmB,EAAE,iBAAiB;AAC9C,OAAO;AACP,KAAK;AACL,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,KAAK,EAAE,MAAM;AACjB,IAAI,IAAI;AACR,GAAG;;AAEH,EAAE,MAAM,MAAO,GAAE,KAAK,EAAE,SAAS,EAAG,IAAGC,uBAAS,EAAE;;AAElD,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,aAAa,EAAE,IAAI,CAAC;AAC1D;;AAEA,EAAE,MAAM,OAAQ,GAAE,KAAK,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC;;AAEzD,EAAE,OAAO,OAAO;AAChB;;;;"}