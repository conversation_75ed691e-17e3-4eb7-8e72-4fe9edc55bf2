{"version": 3, "file": "handleCallbackErrors.js", "sources": ["../../../src/utils/handleCallbackErrors.ts"], "sourcesContent": ["import { isThenable } from '../utils/is';\n\n/**\n * Wrap a callback function with error handling.\n * If an error is thrown, it will be passed to the `onError` callback and re-thrown.\n *\n * If the return value of the function is a promise, it will be handled with `maybeHandlePromiseRejection`.\n *\n * If an `onFinally` callback is provided, this will be called when the callback has finished\n * - so if it returns a promise, once the promise resolved/rejected,\n * else once the callback has finished executing.\n * The `onFinally` callback will _always_ be called, no matter if an error was thrown or not.\n */\nexport function handleCallbackErrors<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  Fn extends () => any,\n>(\n  fn: Fn,\n  onError: (error: unknown) => void,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onFinally: () => void = () => {},\n): ReturnType<Fn> {\n  let maybePromiseResult: ReturnType<Fn>;\n  try {\n    maybePromiseResult = fn();\n  } catch (e) {\n    onError(e);\n    onFinally();\n    throw e;\n  }\n\n  return maybeHandlePromiseRejection(maybePromiseResult, onError, onFinally);\n}\n\n/**\n * Maybe handle a promise rejection.\n * This expects to be given a value that _may_ be a promise, or any other value.\n * If it is a promise, and it rejects, it will call the `onError` callback.\n * Other than this, it will generally return the given value as-is.\n */\nfunction maybeHandlePromiseRejection<MaybePromise>(\n  value: MaybePromise,\n  onError: (error: unknown) => void,\n  onFinally: () => void,\n): MaybePromise {\n  if (isThenable(value)) {\n    // @ts-expect-error - the isThenable check returns the \"wrong\" type here\n    return value.then(\n      res => {\n        onFinally();\n        return res;\n      },\n      e => {\n        onError(e);\n        onFinally();\n        throw e;\n      },\n    );\n  }\n\n  onFinally();\n  return value;\n}\n"], "names": [], "mappings": ";;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS;;AAGhB;AACA,EAAE,EAAE;AACJ,EAAE,OAAO;AACT;AACA,EAAE,SAAS,GAAe,MAAM,EAAE;AAClC,EAAkB;AAClB,EAAE,IAAI,kBAAkB;AACxB,EAAE,IAAI;AACN,IAAI,kBAAmB,GAAE,EAAE,EAAE;AAC7B,GAAI,CAAA,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,IAAI,SAAS,EAAE;AACf,IAAI,MAAM,CAAC;AACX;;AAEA,EAAE,OAAO,2BAA2B,CAAC,kBAAkB,EAAE,OAAO,EAAE,SAAS,CAAC;AAC5E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,2BAA2B;AACpC,EAAE,KAAK;AACP,EAAE,OAAO;AACT,EAAE,SAAS;AACX,EAAgB;AAChB,EAAE,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;AACzB;AACA,IAAI,OAAO,KAAK,CAAC,IAAI;AACrB,MAAM,OAAO;AACb,QAAQ,SAAS,EAAE;AACnB,QAAQ,OAAO,GAAG;AAClB,OAAO;AACP,MAAM,KAAK;AACX,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,QAAQ,SAAS,EAAE;AACnB,QAAQ,MAAM,CAAC;AACf,OAAO;AACP,KAAK;AACL;;AAEA,EAAE,SAAS,EAAE;AACb,EAAE,OAAO,KAAK;AACd;;;;"}