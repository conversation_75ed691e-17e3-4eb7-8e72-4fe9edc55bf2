const { GoogleSpreadsheet } = require('google-spreadsheet');
const { JWT } = require('google-auth-library');
const fs = require('fs');
const path = require('path');

class GoogleSheetsManager {
    constructor() {
        this.doc = null;
        this.serviceAccountAuth = null;
        this.sheetId = process.env.GOOGLE_SHEET_ID;
        this.credentialsPath = path.join(__dirname, 'google-credentials.json');
    }

    // Khởi tạo kết nối Google Sheets
    async initialize() {
        try {
            console.log('Đang khởi tạo Google Sheets connection...');
            
            // Kiểm tra credentials file
            if (!fs.existsSync(this.credentialsPath)) {
                throw new Error('Không tìm thấy file google-credentials.json');
            }

            // Đọc credentials
            const credentials = JSON.parse(fs.readFileSync(this.credentialsPath, 'utf8'));
            
            // Tạo JWT auth
            this.serviceAccountAuth = new JWT({
                email: credentials.client_email,
                key: credentials.private_key,
                scopes: ['https://www.googleapis.com/auth/spreadsheets'],
            });

            // Khởi tạo document
            this.doc = new GoogleSpreadsheet(this.sheetId, this.serviceAccountAuth);
            await this.doc.loadInfo();
            
            console.log(`✓ Đã kết nối Google Sheet: ${this.doc.title}`);
            return true;
            
        } catch (error) {
            console.error('Lỗi khởi tạo Google Sheets:', error.message);
            return false;
        }
    }

    // Ghi log cho 1 tháng
    async logOneMonth(email) {
        try {
            if (!this.doc) {
                await this.initialize();
            }

            // Tìm sheet "1 Month" hoặc tạo mới
            let sheet = this.doc.sheetsByTitle['1 Month'];
            if (!sheet) {
                sheet = await this.doc.addSheet({ 
                    title: '1 Month',
                    headerValues: ['Email', 'Date Added', 'Duration', 'Status']
                });
                console.log('✓ Đã tạo sheet "1 Month"');
            }

            // Thêm dòng mới
            const now = new Date();
            const dateString = now.toLocaleString('vi-VN', { 
                timeZone: 'Asia/Ho_Chi_Minh',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            await sheet.addRow({
                'Email': email,
                'Date Added': dateString,
                'Duration': '1 Month',
                'Status': 'Invited'
            });

            console.log(`✓ Đã ghi log 1 tháng cho: ${email}`);
            return true;

        } catch (error) {
            console.error('Lỗi ghi log 1 tháng:', error.message);
            return false;
        }
    }

    // Ghi log cho 1 năm
    async logOneYear(email) {
        try {
            if (!this.doc) {
                await this.initialize();
            }

            // Tìm sheet "1 Year" hoặc tạo mới
            let sheet = this.doc.sheetsByTitle['1 Year'];
            if (!sheet) {
                sheet = await this.doc.addSheet({ 
                    title: '1 Year',
                    headerValues: ['Email', 'Date Added', 'Duration', 'Status']
                });
                console.log('✓ Đã tạo sheet "1 Year"');
            }

            // Thêm dòng mới
            const now = new Date();
            const dateString = now.toLocaleString('vi-VN', { 
                timeZone: 'Asia/Ho_Chi_Minh',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            await sheet.addRow({
                'Email': email,
                'Date Added': dateString,
                'Duration': '1 Year',
                'Status': 'Invited'
            });

            console.log(`✓ Đã ghi log 1 năm cho: ${email}`);
            return true;

        } catch (error) {
            console.error('Lỗi ghi log 1 năm:', error.message);
            return false;
        }
    }

    // Cập nhật status
    async updateStatus(email, duration, newStatus) {
        try {
            if (!this.doc) {
                await this.initialize();
            }

            const sheetName = duration === '1m' ? '1 Month' : '1 Year';
            const sheet = this.doc.sheetsByTitle[sheetName];
            
            if (!sheet) {
                console.log(`Sheet "${sheetName}" không tồn tại`);
                return false;
            }

            const rows = await sheet.getRows();
            const targetRow = rows.find(row => row.get('Email') === email);
            
            if (targetRow) {
                targetRow.set('Status', newStatus);
                await targetRow.save();
                console.log(`✓ Đã cập nhật status cho ${email}: ${newStatus}`);
                return true;
            } else {
                console.log(`Không tìm thấy email ${email} trong sheet ${sheetName}`);
                return false;
            }

        } catch (error) {
            console.error('Lỗi cập nhật status:', error.message);
            return false;
        }
    }

    // Lấy thống kê
    async getStats() {
        try {
            if (!this.doc) {
                await this.initialize();
            }

            const stats = {
                oneMonth: { total: 0, invited: 0, failed: 0 },
                oneYear: { total: 0, invited: 0, failed: 0 }
            };

            // Thống kê 1 tháng
            const monthSheet = this.doc.sheetsByTitle['1 Month'];
            if (monthSheet) {
                const monthRows = await monthSheet.getRows();
                stats.oneMonth.total = monthRows.length;
                stats.oneMonth.invited = monthRows.filter(row => row.get('Status') === 'Invited').length;
                stats.oneMonth.failed = monthRows.filter(row => row.get('Status') === 'Failed').length;
            }

            // Thống kê 1 năm
            const yearSheet = this.doc.sheetsByTitle['1 Year'];
            if (yearSheet) {
                const yearRows = await yearSheet.getRows();
                stats.oneYear.total = yearRows.length;
                stats.oneYear.invited = yearRows.filter(row => row.get('Status') === 'Invited').length;
                stats.oneYear.failed = yearRows.filter(row => row.get('Status') === 'Failed').length;
            }

            return stats;

        } catch (error) {
            console.error('Lỗi lấy thống kê:', error.message);
            return null;
        }
    }
}

module.exports = GoogleSheetsManager;
