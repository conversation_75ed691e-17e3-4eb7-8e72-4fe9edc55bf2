const { GoogleSpreadsheet } = require('google-spreadsheet');
const { JWT } = require('google-auth-library');
const fs = require('fs');
const path = require('path');

class GoogleSheetsManager {
    constructor() {
        this.doc = null;
        this.serviceAccountAuth = null;
        this.sheetId = process.env.GOOGLE_SHEET_ID;
        this.canvaSheetGid = **********; // GID của tab CANVA
        this.credentialsPath = path.join(__dirname, 'google-credentials.json');
    }

    // Khởi tạo kết nối Google Sheets
    async initialize() {
        try {
            console.log('Đang khởi tạo Google Sheets connection...');
            
            // Ki<PERSON><PERSON> tra credentials file
            if (!fs.existsSync(this.credentialsPath)) {
                throw new Error('Không tìm thấy file google-credentials.json');
            }

            // Đọc credentials
            const credentials = JSON.parse(fs.readFileSync(this.credentialsPath, 'utf8'));
            
            // Tạo JWT auth
            this.serviceAccountAuth = new JWT({
                email: credentials.client_email,
                key: credentials.private_key,
                scopes: ['https://www.googleapis.com/auth/spreadsheets'],
            });

            // Khởi tạo document
            this.doc = new GoogleSpreadsheet(this.sheetId, this.serviceAccountAuth);
            await this.doc.loadInfo();
            
            console.log(`✓ Đã kết nối Google Sheet: ${this.doc.title}`);
            return true;
            
        } catch (error) {
            console.error('Lỗi khởi tạo Google Sheets:', error.message);
            return false;
        }
    }

    // Ghi log cho 1 tháng
    async logOneMonth(email) {
        return await this.logToCanvaSheet(email, '1 Month');
    }

    // Ghi log cho 1 năm
    async logOneYear(email) {
        return await this.logToCanvaSheet(email, '1 Year');
    }

    // Ghi log vào sheet CANVA bằng GID
    async logToCanvaSheet(email, duration) {
        try {
            if (!this.doc) {
                await this.initialize();
            }

            // Tìm sheet bằng GID
            let sheet = this.doc.sheetsById[this.canvaSheetGid];
            if (!sheet) {
                console.log('❌ Không tìm thấy sheet CANVA với GID:', this.canvaSheetGid);
                return false;
            }

            // Thêm dòng mới
            const now = new Date();
            const dateString = now.toLocaleString('vi-VN', {
                timeZone: 'Asia/Ho_Chi_Minh',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            await sheet.addRow({
                'Email': email,
                'Date Added': dateString,
                'Duration': duration,
                'Status': 'Invited'
            });

            console.log(`✓ Đã ghi log ${duration} cho: ${email}`);
            return true;

        } catch (error) {
            console.error(`Lỗi ghi log ${duration}:`, error.message);
            return false;
        }
    }

    // Cập nhật status
    async updateStatus(email, newStatus) {
        try {
            if (!this.doc) {
                await this.initialize();
            }

            // Sử dụng sheet CANVA bằng GID
            const sheet = this.doc.sheetsById[this.canvaSheetGid];

            if (!sheet) {
                console.log('Sheet CANVA không tồn tại với GID:', this.canvaSheetGid);
                return false;
            }

            const rows = await sheet.getRows();
            const targetRow = rows.find(row => row.get('Email') === email);

            if (targetRow) {
                targetRow.set('Status', newStatus);
                await targetRow.save();
                console.log(`✓ Đã cập nhật status cho ${email}: ${newStatus}`);
                return true;
            } else {
                console.log(`Không tìm thấy email ${email} trong sheet CANVA`);
                return false;
            }

        } catch (error) {
            console.error('Lỗi cập nhật status:', error.message);
            return false;
        }
    }

    // Lấy thống kê từ sheet CANVA
    async getStats() {
        try {
            if (!this.doc) {
                await this.initialize();
            }

            const stats = {
                oneMonth: { total: 0, invited: 0, failed: 0 },
                oneYear: { total: 0, invited: 0, failed: 0 },
                total: { total: 0, invited: 0, failed: 0 }
            };

            // Thống kê từ sheet CANVA bằng GID
            const canvaSheet = this.doc.sheetsById[this.canvaSheetGid];
            if (canvaSheet) {
                const rows = await canvaSheet.getRows();

                // Tổng thống kê
                stats.total.total = rows.length;
                stats.total.invited = rows.filter(row => row.get('Status') === 'Invited').length;
                stats.total.failed = rows.filter(row => row.get('Status') === 'Failed').length;

                // Thống kê theo duration
                const monthRows = rows.filter(row => row.get('Duration') === '1 Month');
                const yearRows = rows.filter(row => row.get('Duration') === '1 Year');

                stats.oneMonth.total = monthRows.length;
                stats.oneMonth.invited = monthRows.filter(row => row.get('Status') === 'Invited').length;
                stats.oneMonth.failed = monthRows.filter(row => row.get('Status') === 'Failed').length;

                stats.oneYear.total = yearRows.length;
                stats.oneYear.invited = yearRows.filter(row => row.get('Status') === 'Invited').length;
                stats.oneYear.failed = yearRows.filter(row => row.get('Status') === 'Failed').length;
            }

            return stats;

        } catch (error) {
            console.error('Lỗi lấy thống kê:', error.message);
            return null;
        }
    }
}

module.exports = GoogleSheetsManager;
