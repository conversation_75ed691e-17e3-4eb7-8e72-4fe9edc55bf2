Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const exports$1 = require('./logs/exports.js');
const index = require('./integrations/http/index.js');
const index$1 = require('./integrations/node-fetch/index.js');
const fs = require('./integrations/fs.js');
const context = require('./integrations/context.js');
const contextlines = require('./integrations/contextlines.js');
const index$2 = require('./integrations/local-variables/index.js');
const modules = require('./integrations/modules.js');
const onuncaughtexception = require('./integrations/onuncaughtexception.js');
const onunhandledrejection = require('./integrations/onunhandledrejection.js');
const index$3 = require('./integrations/anr/index.js');
const express = require('./integrations/tracing/express.js');
const index$4 = require('./integrations/tracing/fastify/index.js');
const graphql = require('./integrations/tracing/graphql.js');
const kafka = require('./integrations/tracing/kafka.js');
const lrumemoizer = require('./integrations/tracing/lrumemoizer.js');
const mongo = require('./integrations/tracing/mongo.js');
const mongoose = require('./integrations/tracing/mongoose.js');
const mysql = require('./integrations/tracing/mysql.js');
const mysql2 = require('./integrations/tracing/mysql2.js');
const redis = require('./integrations/tracing/redis.js');
const postgres = require('./integrations/tracing/postgres.js');
const postgresjs = require('./integrations/tracing/postgresjs.js');
const prisma = require('./integrations/tracing/prisma.js');
const index$5 = require('./integrations/tracing/hapi/index.js');
const koa = require('./integrations/tracing/koa.js');
const connect = require('./integrations/tracing/connect.js');
const spotlight = require('./integrations/spotlight.js');
const knex = require('./integrations/tracing/knex.js');
const tedious = require('./integrations/tracing/tedious.js');
const genericPool = require('./integrations/tracing/genericPool.js');
const dataloader = require('./integrations/tracing/dataloader.js');
const amqplib = require('./integrations/tracing/amqplib.js');
const index$6 = require('./integrations/tracing/vercelai/index.js');
const childProcess = require('./integrations/childProcess.js');
const winston = require('./integrations/winston.js');
const launchDarkly = require('./integrations/featureFlagShims/launchDarkly.js');
const openFeature = require('./integrations/featureFlagShims/openFeature.js');
const statsig = require('./integrations/featureFlagShims/statsig.js');
const unleash = require('./integrations/featureFlagShims/unleash.js');
const contextManager = require('./otel/contextManager.js');
const instrument = require('./otel/instrument.js');
const index$7 = require('./sdk/index.js');
const initOtel = require('./sdk/initOtel.js');
const index$8 = require('./integrations/tracing/index.js');
const api = require('./sdk/api.js');
const module$1 = require('./utils/module.js');
const http = require('./transports/http.js');
const client = require('./sdk/client.js');
const index$9 = require('./cron/index.js');
const nodeVersion = require('./nodeVersion.js');
const opentelemetry = require('@sentry/opentelemetry');
const core = require('@sentry/core');



exports.logger = exports$1;
exports.httpIntegration = index.httpIntegration;
exports.nativeNodeFetchIntegration = index$1.nativeNodeFetchIntegration;
exports.fsIntegration = fs.fsIntegration;
exports.nodeContextIntegration = context.nodeContextIntegration;
exports.contextLinesIntegration = contextlines.contextLinesIntegration;
exports.localVariablesIntegration = index$2.localVariablesIntegration;
exports.modulesIntegration = modules.modulesIntegration;
exports.onUncaughtExceptionIntegration = onuncaughtexception.onUncaughtExceptionIntegration;
exports.onUnhandledRejectionIntegration = onunhandledrejection.onUnhandledRejectionIntegration;
exports.anrIntegration = index$3.anrIntegration;
exports.disableAnrDetectionForCallback = index$3.disableAnrDetectionForCallback;
exports.expressErrorHandler = express.expressErrorHandler;
exports.expressIntegration = express.expressIntegration;
exports.setupExpressErrorHandler = express.setupExpressErrorHandler;
exports.fastifyIntegration = index$4.fastifyIntegration;
exports.setupFastifyErrorHandler = index$4.setupFastifyErrorHandler;
exports.graphqlIntegration = graphql.graphqlIntegration;
exports.kafkaIntegration = kafka.kafkaIntegration;
exports.lruMemoizerIntegration = lrumemoizer.lruMemoizerIntegration;
exports.mongoIntegration = mongo.mongoIntegration;
exports.mongooseIntegration = mongoose.mongooseIntegration;
exports.mysqlIntegration = mysql.mysqlIntegration;
exports.mysql2Integration = mysql2.mysql2Integration;
exports.redisIntegration = redis.redisIntegration;
exports.postgresIntegration = postgres.postgresIntegration;
exports.postgresJsIntegration = postgresjs.postgresJsIntegration;
exports.prismaIntegration = prisma.prismaIntegration;
exports.hapiIntegration = index$5.hapiIntegration;
exports.setupHapiErrorHandler = index$5.setupHapiErrorHandler;
exports.koaIntegration = koa.koaIntegration;
exports.setupKoaErrorHandler = koa.setupKoaErrorHandler;
exports.connectIntegration = connect.connectIntegration;
exports.setupConnectErrorHandler = connect.setupConnectErrorHandler;
exports.spotlightIntegration = spotlight.spotlightIntegration;
exports.knexIntegration = knex.knexIntegration;
exports.tediousIntegration = tedious.tediousIntegration;
exports.genericPoolIntegration = genericPool.genericPoolIntegration;
exports.dataloaderIntegration = dataloader.dataloaderIntegration;
exports.amqplibIntegration = amqplib.amqplibIntegration;
exports.vercelAIIntegration = index$6.vercelAIIntegration;
exports.childProcessIntegration = childProcess.childProcessIntegration;
exports.createSentryWinstonTransport = winston.createSentryWinstonTransport;
exports.buildLaunchDarklyFlagUsedHandler = launchDarkly.buildLaunchDarklyFlagUsedHandlerShim;
exports.launchDarklyIntegration = launchDarkly.launchDarklyIntegrationShim;
exports.OpenFeatureIntegrationHook = openFeature.OpenFeatureIntegrationHookShim;
exports.openFeatureIntegration = openFeature.openFeatureIntegrationShim;
exports.statsigIntegration = statsig.statsigIntegrationShim;
exports.unleashIntegration = unleash.unleashIntegrationShim;
exports.SentryContextManager = contextManager.SentryContextManager;
exports.generateInstrumentOnce = instrument.generateInstrumentOnce;
exports.getDefaultIntegrations = index$7.getDefaultIntegrations;
exports.getDefaultIntegrationsWithoutPerformance = index$7.getDefaultIntegrationsWithoutPerformance;
exports.init = index$7.init;
exports.initWithoutDefaultIntegrations = index$7.initWithoutDefaultIntegrations;
exports.validateOpenTelemetrySetup = index$7.validateOpenTelemetrySetup;
exports.initOpenTelemetry = initOtel.initOpenTelemetry;
exports.preloadOpenTelemetry = initOtel.preloadOpenTelemetry;
exports.getAutoPerformanceIntegrations = index$8.getAutoPerformanceIntegrations;
exports.defaultStackParser = api.defaultStackParser;
exports.getSentryRelease = api.getSentryRelease;
exports.createGetModuleFromFilename = module$1.createGetModuleFromFilename;
exports.makeNodeTransport = http.makeNodeTransport;
exports.NodeClient = client.NodeClient;
exports.cron = index$9.cron;
exports.NODE_VERSION = nodeVersion.NODE_VERSION;
exports.setNodeAsyncContextStrategy = opentelemetry.setOpenTelemetryContextAsyncContextStrategy;
exports.SDK_VERSION = core.SDK_VERSION;
exports.SEMANTIC_ATTRIBUTE_SENTRY_OP = core.SEMANTIC_ATTRIBUTE_SENTRY_OP;
exports.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN = core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN;
exports.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE = core.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE;
exports.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE = core.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE;
exports.Scope = core.Scope;
exports.addBreadcrumb = core.addBreadcrumb;
exports.addEventProcessor = core.addEventProcessor;
exports.addIntegration = core.addIntegration;
exports.captureCheckIn = core.captureCheckIn;
exports.captureConsoleIntegration = core.captureConsoleIntegration;
exports.captureEvent = core.captureEvent;
exports.captureException = core.captureException;
exports.captureFeedback = core.captureFeedback;
exports.captureMessage = core.captureMessage;
exports.captureSession = core.captureSession;
exports.close = core.close;
exports.consoleIntegration = core.consoleIntegration;
exports.consoleLoggingIntegration = core.consoleLoggingIntegration;
exports.continueTrace = core.continueTrace;
exports.createTransport = core.createTransport;
exports.dedupeIntegration = core.dedupeIntegration;
exports.endSession = core.endSession;
exports.eventFiltersIntegration = core.eventFiltersIntegration;
exports.extraErrorDataIntegration = core.extraErrorDataIntegration;
exports.featureFlagsIntegration = core.featureFlagsIntegration;
exports.flush = core.flush;
exports.functionToStringIntegration = core.functionToStringIntegration;
exports.getActiveSpan = core.getActiveSpan;
exports.getClient = core.getClient;
exports.getCurrentScope = core.getCurrentScope;
exports.getGlobalScope = core.getGlobalScope;
exports.getIsolationScope = core.getIsolationScope;
exports.getRootSpan = core.getRootSpan;
exports.getSpanDescendants = core.getSpanDescendants;
exports.getSpanStatusFromHttpCode = core.getSpanStatusFromHttpCode;
exports.getTraceData = core.getTraceData;
exports.getTraceMetaTags = core.getTraceMetaTags;
exports.inboundFiltersIntegration = core.inboundFiltersIntegration;
exports.instrumentSupabaseClient = core.instrumentSupabaseClient;
exports.isEnabled = core.isEnabled;
exports.isInitialized = core.isInitialized;
exports.lastEventId = core.lastEventId;
exports.linkedErrorsIntegration = core.linkedErrorsIntegration;
exports.parameterize = core.parameterize;
exports.profiler = core.profiler;
exports.requestDataIntegration = core.requestDataIntegration;
exports.rewriteFramesIntegration = core.rewriteFramesIntegration;
exports.setContext = core.setContext;
exports.setCurrentClient = core.setCurrentClient;
exports.setExtra = core.setExtra;
exports.setExtras = core.setExtras;
exports.setHttpStatus = core.setHttpStatus;
exports.setMeasurement = core.setMeasurement;
exports.setTag = core.setTag;
exports.setTags = core.setTags;
exports.setUser = core.setUser;
exports.spanToBaggageHeader = core.spanToBaggageHeader;
exports.spanToJSON = core.spanToJSON;
exports.spanToTraceHeader = core.spanToTraceHeader;
exports.startInactiveSpan = core.startInactiveSpan;
exports.startNewTrace = core.startNewTrace;
exports.startSession = core.startSession;
exports.startSpan = core.startSpan;
exports.startSpanManual = core.startSpanManual;
exports.supabaseIntegration = core.supabaseIntegration;
exports.suppressTracing = core.suppressTracing;
exports.trpcMiddleware = core.trpcMiddleware;
exports.updateSpanName = core.updateSpanName;
exports.withActiveSpan = core.withActiveSpan;
exports.withIsolationScope = core.withIsolationScope;
exports.withMonitor = core.withMonitor;
exports.withScope = core.withScope;
exports.wrapMcpServerWithSentry = core.wrapMcpServerWithSentry;
exports.zodErrorsIntegration = core.zodErrorsIntegration;
//# sourceMappingURL=index.js.map
